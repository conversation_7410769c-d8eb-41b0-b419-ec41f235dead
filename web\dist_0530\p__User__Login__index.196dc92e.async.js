"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9366],{31736:function(S,r,n){n.r(r),n.d(r,{default:function(){return j}});var c=n(15009),t=n.n(c),d=n(99289),v=n.n(d),h=n(10915),l=n(84226),g=n(2453),f=n(68744),a=n(85893),m=function(){var P=(0,l.useIntl)(),s=(0,l.useModel)("@@initialState"),p=s.initialState,C=s.setInitialState,y=function(){var F=v()(t()().mark(function i(){var u,o;return t()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,u=new URLSearchParams(window.location.search),o=u.get("redirect")||"/Console/projects",e.next=5,f.e.login(o);case 5:e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("Keycloak\u767B\u5F55\u5931\u8D25:",e.t0),g.ZP.error("Keycloak\u767B\u5F55\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5\uFF01");case 11:case"end":return e.stop()}},i,null,[[0,7]])}));return function(){return F.apply(this,arguments)}}();return(0,a.jsxs)("div",{className:"login-container",children:[(0,a.jsx)("div",{className:"login-content-left"}),(0,a.jsx)("div",{className:"login-content-right",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{children:"\u82AF\u5408\u8DE8\u67B6\u6784\u7CFB\u7EDF-DeepSeek\u4E13\u4EAB\u5957\u4EF6"}),(0,a.jsx)("button",{onClick:y,className:"login-button",children:"\u4F7F\u7528 Keycloak \u767B\u5F55"})]})})]})},j=function(){return(0,a.jsx)(h._Y,{dark:!0,children:(0,a.jsx)(m,{})})}}}]);
