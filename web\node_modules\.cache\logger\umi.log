{"level":30,"time":1748571653040,"pid":21656,"hostname":"小丸犊子","msg":"\u001b[33m[你知道吗？] 如果想检测未使用的文件和导出，可尝试新出的 deadCode 配置项，详见 https://umijs.org/docs/api/config#deadcode\u001b[39m"}
{"level":30,"time":1748571653094,"pid":21656,"hostname":"小丸犊子","msg":"generate files"}
{"level":30,"time":1748571654238,"pid":21656,"hostname":"小丸犊子","msg":"Preparing..."}
{"level":30,"time":1748571704196,"pid":22020,"hostname":"小丸犊子","msg":"\u001b[33m[你知道吗？] 全局样式、全局脚本写在哪里？创建 src/global.(ts|css) 轻松解决，详见 https://umijs.org/docs/guides/directory-structure#globaljtsx\u001b[39m"}
{"level":30,"time":1748571704199,"pid":22020,"hostname":"小丸犊子","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748571705337,"pid":22020,"hostname":"小丸犊子","msg":"Preparing..."}
{"level":20,"time":1748571713502,"pid":22020,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":31,"time":1748571713550,"pid":22020,"hostname":"小丸犊子","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://*********:8088\u001b[39m                  \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748571724431,"pid":22020,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 10925 ms (703 modules)"}
{"level":30,"time":1748571724436,"pid":22020,"hostname":"小丸犊子","msg":"[MFSU] buildDeps since cacheDependency has changed"}
{"level":20,"time":1748571724436,"pid":22020,"hostname":"小丸犊子","msg":"D:/project/web_app_0527v2/web/node_modules/@umijs/renderer-react, D:/project/web_app_0527v2/web/node_modules/antd/dist/reset.css, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js, D:/project/web_app_0527v2/web/node_modules/react/jsx-dev-runtime, D:/project/web_app_0527v2/web/node_modules/antd, D:/project/web_app_0527v2/web/node_modules/umi/client/client/plugin.js, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/duration, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/localizedFormat, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/localeData, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/isMoment, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/weekOfYear, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/weekYear, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/weekday, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/customParseFormat, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/advancedFormat, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/isSameOrAfter, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/isSameOrBefore, D:/project/web_app_0527v2/web/node_modules/antd-dayjs-webpack-plugin/src/antd-plugin.js, D:/project/web_app_0527v2/web/node_modules/dayjs, D:/project/web_app_0527v2/web/node_modules/@umijs/preset-umi/node_modules/regenerator-runtime/runtime.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.url-search-params.size.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.url-search-params.has.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.url-search-params.delete.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.url.can-parse.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.structured-clone.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.self.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.immediate.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.dom-exception.stack.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-set.of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-set.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-set.delete-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-set.add-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-map.upsert.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-map.emplace.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-map.of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-map.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-map.delete-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.uint8-array.to-hex.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.uint8-array.to-base64.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.uint8-array.from-hex.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.uint8-array.from-base64.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.unique-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.to-spliced.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.group-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.filter-reject.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.filter-out.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.from-async.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.replace-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.pattern-match.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.observable.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.metadata-key.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.matcher.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.is-well-known.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.is-well-known-symbol.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.is-registered.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.is-registered-symbol.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.dispose.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.async-dispose.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.string.dedent.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.string.code-points.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.string.cooked.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.string.at.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.union.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.union.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.symmetric-difference.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.symmetric-difference.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.some.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.reduce.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.join.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-superset-of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-superset-of.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-subset-of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-subset-of.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-disjoint-from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-disjoint-from.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.intersection.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.intersection.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.find.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.filter.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.every.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.difference.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.difference.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.delete-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.add-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.regexp.escape.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.has-own-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.has-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.get-own-metadata-keys.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.get-own-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.get-metadata-keys.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.get-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.delete-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.define-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.promise.try.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.observable.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.object.iterate-values.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.object.iterate-keys.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.object.iterate-entries.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.number.range.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.number.from-string.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.umulh.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.signbit.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.seeded-prng.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.scale.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.radians.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.rad-per-deg.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.isubh.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.imulh.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.iaddh.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.f16round.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.fscale.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.degrees.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.deg-per-rad.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.clamp.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.upsert.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.update-or-insert.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.update.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.some.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.reduce.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.merge.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.map-values.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.map-keys.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.key-of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.key-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.includes.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.find-key.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.find.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.filter.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.every.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.emplace.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.delete-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.json.raw-json.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.json.parse.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.json.is-raw-json.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.to-async.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.to-array.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.take.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.some.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.reduce.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.range.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.indexed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.for-each.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.flat-map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.find.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.filter.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.every.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.drop.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.dispose.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.as-indexed-pairs.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.function.un-this.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.function.metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.function.is-constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.function.is-callable.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.function.demethodize.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.disposable-stack.constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.data-view.set-uint8-clamped.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.data-view.set-float16.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.data-view.get-uint8-clamped.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.data-view.get-float16.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.composite-symbol.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.composite-key.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.bigint.range.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.to-array.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.take.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.some.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.reduce.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.indexed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.for-each.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.flat-map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.find.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.filter.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.every.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.drop.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.async-dispose.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.as-indexed-pairs.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-disposable-stack.constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array-buffer.transfer-to-fixed-length.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array-buffer.transfer.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array-buffer.detached.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.unique-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.last-item.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.last-index.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.is-template-object.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.group-to-map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.group-by-to-map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.group-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.group.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.filter-reject.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.filter-out.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.from-async.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.suppressed-error.constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.with.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.to-sorted.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.to-reversed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.set.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.find-last-index.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.find-last.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.at.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.string.to-well-formed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.string.replace-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.string.is-well-formed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.string.at-alternative.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.regexp.flags.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.reflect.to-string-tag.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.promise.with-resolvers.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.promise.any.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.object.has-own.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.object.group-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.map.group-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.with.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.to-spliced.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.to-sorted.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.to-reversed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.reduce-right.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.reduce.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.push.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.find-last-index.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.find-last.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.at.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.aggregate-error.cause.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.aggregate-error.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.error.cause.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/typeof.js, D:/project/web_app_0527v2/web/node_modules/react, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js, D:/project/web_app_0527v2/web/node_modules/@ant-design/pro-components, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js, D:/project/web_app_0527v2/web/node_modules/fast-deep-equal/index.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/defineProperty.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/classCallCheck.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/createClass.js, D:/project/web_app_0527v2/web/node_modules/@umijs/preset-umi/node_modules/react-router-dom, @ant-design/pro-components, @ant-design/icons, monaco-editor, dayjs/plugin/relativeTime, dayjs, dayjs/locale/zh-cn, D:/project/web_app_0527v2/web/node_modules/antd/es/date-picker/locale/zh_CN, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectWithoutProperties.js, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/DesktopOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/AppstoreOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/DeploymentUnitOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/LineChartOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/ToolOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/ProjectOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/ConsoleSqlOutlined, D:/project/web_app_0527v2/web/node_modules/@umijs/plugins/node_modules/@ahooksjs/use-request, D:/project/web_app_0527v2/web/node_modules/axios, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/zh_TW, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/zh_CN, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/pt_BR, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/ja_JP, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/id_ID, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/fa_IR, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/en_US, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/bn_BD, D:/project/web_app_0527v2/web/node_modules/@umijs/plugins/node_modules/react-intl, D:/project/web_app_0527v2/web/node_modules/warning, D:/project/web_app_0527v2/web/node_modules/event-emitter, antd-style, lodash, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/zh-tw, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/zh-cn, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/pt-br, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/ja, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/id, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/fa, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/en, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/bn-bd, keycloak-js, @monaco-editor/react, D:/project/web_app_0527v2/web/node_modules/react-dom, querystring, numeral, classnames"}
{"level":55,"time":1748571724666,"pid":22020,"hostname":"小丸犊子","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748571724667,"pid":22020,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":32,"time":1748571727747,"pid":22020,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 3072 ms (599 modules)"}
{"level":55,"time":1748571727908,"pid":22020,"hostname":"小丸犊子","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748571727919,"pid":22020,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":32,"time":1748571729381,"pid":22020,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 1468 ms (599 modules)"}
{"level":32,"time":1748571756147,"pid":22020,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 24361 ms (13459 modules)"}
{"level":30,"time":1748571756227,"pid":22020,"hostname":"小丸犊子","msg":"[MFSU] write cache"}
{"level":30,"time":1748573118382,"pid":1468,"hostname":"小丸犊子","msg":"\u001b[33m[你知道吗？] @umijs/max 和 umi 如何选择？max 内置了很多好用的插件，即开即用，而 umi 也可以手动配置插件，独立使用，详见 https://umijs.org/docs/guides/use-plugins\u001b[39m"}
{"level":30,"time":1748573118384,"pid":1468,"hostname":"小丸犊子","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748573119574,"pid":1468,"hostname":"小丸犊子","msg":"Preparing..."}
{"level":30,"time":1748573151082,"pid":1468,"hostname":"小丸犊子","msg":"Memory Usage: 919.48 MB (RSS: 1687.53 MB)"}
{"level":30,"time":1748573154701,"pid":1468,"hostname":"小丸犊子","msg":"File sizes after gzip:\n"}
{"level":30,"time":1748575541936,"pid":9052,"hostname":"小丸犊子","msg":"\u001b[33m[你知道吗？] HMR=none max dev 可以关闭 Umi 开发服务器的模块热替换功能。\u001b[39m"}
{"level":30,"time":1748575541937,"pid":9052,"hostname":"小丸犊子","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748575543036,"pid":9052,"hostname":"小丸犊子","msg":"Preparing..."}
{"level":30,"time":1748575574494,"pid":9052,"hostname":"小丸犊子","msg":"Memory Usage: 1153.34 MB (RSS: 1640.38 MB)"}
{"level":30,"time":1748575578522,"pid":9052,"hostname":"小丸犊子","msg":"File sizes after gzip:\n"}
{"level":32,"time":1748575578863,"pid":9052,"hostname":"小丸犊子","msg":"Build index.html"}
{"level":30,"time":1748586741646,"pid":14860,"hostname":"小丸犊子","msg":"\u001b[33m[你知道吗？] 你知道可以通过 UMI_ENV 定义多个环境的配置吗，详见 https://umijs.org/docs/guides/env-variables#umi_env\u001b[39m"}
{"level":30,"time":1748586741647,"pid":14860,"hostname":"小丸犊子","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748586742801,"pid":14860,"hostname":"小丸犊子","msg":"Preparing..."}
{"level":30,"time":1748586777729,"pid":14860,"hostname":"小丸犊子","msg":"Memory Usage: 1283.08 MB (RSS: 1612.19 MB)"}
{"level":30,"time":1748586782361,"pid":14860,"hostname":"小丸犊子","msg":"File sizes after gzip:\n"}
{"level":30,"time":1748587647315,"pid":26548,"hostname":"小丸犊子","msg":"\u001b[33m[你知道吗？] father 4 正式发布了，详见 https://zhuanlan.zhihu.com/p/558192063\u001b[39m"}
{"level":30,"time":1748587647316,"pid":26548,"hostname":"小丸犊子","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748587648467,"pid":26548,"hostname":"小丸犊子","msg":"Preparing..."}
{"level":30,"time":1748587682029,"pid":26548,"hostname":"小丸犊子","msg":"Memory Usage: 846.24 MB (RSS: 1609.65 MB)"}
{"level":30,"time":1748587685652,"pid":26548,"hostname":"小丸犊子","msg":"File sizes after gzip:\n"}
{"level":32,"time":1748587685988,"pid":26548,"hostname":"小丸犊子","msg":"Build index.html"}
{"level":30,"time":1748588088548,"pid":18956,"hostname":"小丸犊子","msg":"\u001b[33m[你知道吗？] 如果你有 MPA（多页应用）需求，可尝试新出的 mpa 配置项，详见 https://umijs.org/docs/guides/mpa\u001b[39m"}
{"level":30,"time":1748588088551,"pid":18956,"hostname":"小丸犊子","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748588089689,"pid":18956,"hostname":"小丸犊子","msg":"Preparing..."}
{"level":30,"time":1748588091024,"pid":18956,"hostname":"小丸犊子","msg":"[MFSU] restore cache"}
{"level":20,"time":1748588091990,"pid":18956,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":31,"time":1748588092038,"pid":18956,"hostname":"小丸犊子","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://*************:8088\u001b[39m              \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748588096804,"pid":18956,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 4812 ms (613 modules)"}
{"level":30,"time":1748588096807,"pid":18956,"hostname":"小丸犊子","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748588096995,"pid":18956,"hostname":"小丸犊子","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748588096996,"pid":18956,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":32,"time":1748588097338,"pid":18956,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 342 ms (599 modules)"}
{"level":30,"time":1748588097339,"pid":18956,"hostname":"小丸犊子","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748588097380,"pid":18956,"hostname":"小丸犊子","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748588097381,"pid":18956,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":32,"time":1748588097467,"pid":18956,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 86 ms (599 modules)"}
{"level":30,"time":1748588097468,"pid":18956,"hostname":"小丸犊子","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748588228338,"pid":18956,"hostname":"小丸犊子","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748588228367,"pid":18956,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":32,"time":1748588228600,"pid":18956,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 234 ms (599 modules)"}
{"level":30,"time":1748588228602,"pid":18956,"hostname":"小丸犊子","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748588245495,"pid":18956,"hostname":"小丸犊子","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748588245521,"pid":18956,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":32,"time":1748588245690,"pid":18956,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 169 ms (599 modules)"}
{"level":30,"time":1748588245692,"pid":18956,"hostname":"小丸犊子","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748588248741,"pid":18956,"hostname":"小丸犊子","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748588248766,"pid":18956,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":32,"time":1748588248914,"pid":18956,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 149 ms (599 modules)"}
{"level":30,"time":1748588248916,"pid":18956,"hostname":"小丸犊子","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748588248924,"pid":18956,"hostname":"小丸犊子","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748588248946,"pid":18956,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":32,"time":1748588249103,"pid":18956,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 158 ms (599 modules)"}
{"level":30,"time":1748588249105,"pid":18956,"hostname":"小丸犊子","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748588359935,"pid":17924,"hostname":"小丸犊子","msg":"\u001b[33m[你知道吗？] max g tsconfig 可一键完成项目的 TypeScript 配置。\u001b[39m"}
{"level":30,"time":1748588359936,"pid":17924,"hostname":"小丸犊子","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748588361047,"pid":17924,"hostname":"小丸犊子","msg":"Preparing..."}
{"level":30,"time":1748588392687,"pid":17924,"hostname":"小丸犊子","msg":"Memory Usage: 1384.44 MB (RSS: 1612.8 MB)"}
{"level":30,"time":1748588396952,"pid":17924,"hostname":"小丸犊子","msg":"File sizes after gzip:\n"}
{"level":32,"time":1748588397288,"pid":17924,"hostname":"小丸犊子","msg":"Build index.html"}
{"level":30,"time":1748590419025,"pid":19256,"hostname":"小丸犊子","msg":"\u001b[33m[你知道吗？] 请求加载态、数据管理、避免竟态问题，用 react-query 帮你全部解决，详见 https://umijs.org/docs/max/react-query\u001b[39m"}
{"level":30,"time":1748590419028,"pid":19256,"hostname":"小丸犊子","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748590420262,"pid":19256,"hostname":"小丸犊子","msg":"Preparing..."}
{"level":30,"time":1748590421797,"pid":19256,"hostname":"小丸犊子","msg":"[MFSU] restore cache"}
{"level":20,"time":1748590422741,"pid":19256,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":31,"time":1748590422790,"pid":19256,"hostname":"小丸犊子","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://*************:8088\u001b[39m              \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748590427230,"pid":19256,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 4488 ms (615 modules)"}
{"level":30,"time":1748590427235,"pid":19256,"hostname":"小丸犊子","msg":"[MFSU] buildDeps since cacheDependency has changed"}
{"level":20,"time":1748590427235,"pid":19256,"hostname":"小丸犊子","msg":"D:/project/web_app_0527v2/web/node_modules/@umijs/renderer-react, D:/project/web_app_0527v2/web/node_modules/antd/dist/reset.css, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/duration, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/localizedFormat, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/localeData, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/isMoment, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/weekOfYear, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/weekYear, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/weekday, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/customParseFormat, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/advancedFormat, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/isSameOrAfter, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/isSameOrBefore, D:/project/web_app_0527v2/web/node_modules/antd-dayjs-webpack-plugin/src/antd-plugin.js, D:/project/web_app_0527v2/web/node_modules/dayjs, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/typeof.js, D:/project/web_app_0527v2/web/node_modules/react, D:/project/web_app_0527v2/web/node_modules/react/jsx-dev-runtime, D:/project/web_app_0527v2/web/node_modules/@umijs/preset-umi/node_modules/regenerator-runtime/runtime.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.url-search-params.size.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.url-search-params.has.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.url-search-params.delete.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.url.can-parse.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.structured-clone.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.self.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.immediate.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.dom-exception.stack.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-set.of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-set.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-set.delete-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-set.add-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-map.upsert.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-map.emplace.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-map.of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-map.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-map.delete-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.uint8-array.to-hex.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.uint8-array.to-base64.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.uint8-array.from-hex.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.uint8-array.from-base64.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.unique-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.to-spliced.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.group-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.filter-reject.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.filter-out.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.from-async.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.replace-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.pattern-match.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.observable.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.metadata-key.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.matcher.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.is-well-known.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.is-well-known-symbol.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.is-registered.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.is-registered-symbol.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.dispose.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.async-dispose.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.string.dedent.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.string.code-points.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.string.cooked.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.string.at.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.union.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.union.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.symmetric-difference.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.symmetric-difference.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.some.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.reduce.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.join.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-superset-of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-superset-of.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-subset-of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-subset-of.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-disjoint-from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-disjoint-from.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.intersection.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.intersection.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.find.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.filter.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.every.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.difference.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.difference.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.delete-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.add-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.regexp.escape.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.has-own-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.has-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.get-own-metadata-keys.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.get-own-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.get-metadata-keys.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.get-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.delete-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.define-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.promise.try.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.observable.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.object.iterate-values.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.object.iterate-keys.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.object.iterate-entries.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.number.range.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.number.from-string.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.umulh.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.signbit.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.seeded-prng.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.scale.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.radians.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.rad-per-deg.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.isubh.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.imulh.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.iaddh.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.f16round.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.fscale.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.degrees.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.deg-per-rad.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.clamp.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.upsert.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.update-or-insert.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.update.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.some.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.reduce.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.merge.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.map-values.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.map-keys.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.key-of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.key-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.includes.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.find-key.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.find.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.filter.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.every.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.emplace.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.delete-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.json.raw-json.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.json.parse.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.json.is-raw-json.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.to-async.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.to-array.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.take.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.some.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.reduce.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.range.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.indexed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.for-each.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.flat-map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.find.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.filter.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.every.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.drop.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.dispose.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.as-indexed-pairs.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.function.un-this.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.function.metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.function.is-constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.function.is-callable.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.function.demethodize.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.disposable-stack.constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.data-view.set-uint8-clamped.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.data-view.set-float16.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.data-view.get-uint8-clamped.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.data-view.get-float16.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.composite-symbol.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.composite-key.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.bigint.range.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.to-array.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.take.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.some.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.reduce.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.indexed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.for-each.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.flat-map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.find.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.filter.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.every.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.drop.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.async-dispose.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.as-indexed-pairs.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-disposable-stack.constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array-buffer.transfer-to-fixed-length.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array-buffer.transfer.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array-buffer.detached.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.unique-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.last-item.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.last-index.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.is-template-object.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.group-to-map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.group-by-to-map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.group-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.group.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.filter-reject.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.filter-out.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.from-async.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.suppressed-error.constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.with.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.to-sorted.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.to-reversed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.set.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.find-last-index.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.find-last.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.at.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.string.to-well-formed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.string.replace-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.string.is-well-formed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.string.at-alternative.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.regexp.flags.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.reflect.to-string-tag.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.promise.with-resolvers.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.promise.any.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.object.has-own.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.object.group-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.map.group-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.with.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.to-spliced.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.to-sorted.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.to-reversed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.reduce-right.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.reduce.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.push.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.find-last-index.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.find-last.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.at.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.aggregate-error.cause.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.aggregate-error.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.error.cause.js, D:/project/web_app_0527v2/web/node_modules/antd, D:/project/web_app_0527v2/web/node_modules/umi/client/client/plugin.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js, D:/project/web_app_0527v2/web/node_modules/@ant-design/pro-components, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/DesktopOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/AppstoreOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/DeploymentUnitOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/LineChartOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/ToolOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/ProjectOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/ConsoleSqlOutlined, D:/project/web_app_0527v2/web/node_modules/@umijs/plugins/node_modules/@ahooksjs/use-request, D:/project/web_app_0527v2/web/node_modules/axios, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectWithoutProperties.js, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/zh-tw, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/zh-cn, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/pt-br, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/ja, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/id, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/fa, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/en, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/bn-bd, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/zh_TW, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/zh_CN, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/pt_BR, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/ja_JP, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/id_ID, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/fa_IR, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/en_US, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/bn_BD, D:/project/web_app_0527v2/web/node_modules/@umijs/plugins/node_modules/react-intl, D:/project/web_app_0527v2/web/node_modules/warning, D:/project/web_app_0527v2/web/node_modules/event-emitter, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/defineProperty.js, D:/project/web_app_0527v2/web/node_modules/fast-deep-equal/index.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/classCallCheck.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/createClass.js, D:/project/web_app_0527v2/web/node_modules/@umijs/preset-umi/node_modules/react-router-dom, @ant-design/pro-components, @ant-design/icons, monaco-editor, antd-style, dayjs/plugin/relativeTime, dayjs, dayjs/locale/zh-cn, D:/project/web_app_0527v2/web/node_modules/antd/es/date-picker/locale/zh_CN, keycloak-js, lodash, D:/project/web_app_0527v2/web/node_modules/react-dom, querystring, @monaco-editor/react, numeral, classnames"}
{"level":55,"time":1748590427409,"pid":19256,"hostname":"小丸犊子","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748590427410,"pid":19256,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":32,"time":1748590427766,"pid":19256,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 357 ms (601 modules)"}
{"level":55,"time":1748590427795,"pid":19256,"hostname":"小丸犊子","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748590427796,"pid":19256,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":32,"time":1748590427911,"pid":19256,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 116 ms (601 modules)"}
{"level":32,"time":1748590439463,"pid":19256,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 11012 ms (12776 modules)"}
{"level":30,"time":1748590439469,"pid":19256,"hostname":"小丸犊子","msg":"[MFSU] write cache"}
{"level":30,"time":1748590509021,"pid":16872,"hostname":"小丸犊子","msg":"\u001b[33m[你知道吗？] 如果你需要使用 Jest 来测试 Umi 项目, max g jest 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#jest-配置生成器\u001b[39m"}
{"level":30,"time":1748590509022,"pid":16872,"hostname":"小丸犊子","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748590510121,"pid":16872,"hostname":"小丸犊子","msg":"Preparing..."}
{"level":30,"time":1748590545358,"pid":16872,"hostname":"小丸犊子","msg":"Memory Usage: 847.08 MB (RSS: 1570.93 MB)"}
{"level":30,"time":1748590548903,"pid":16872,"hostname":"小丸犊子","msg":"File sizes after gzip:\n"}
{"level":32,"time":1748590549232,"pid":16872,"hostname":"小丸犊子","msg":"Build index.html"}
