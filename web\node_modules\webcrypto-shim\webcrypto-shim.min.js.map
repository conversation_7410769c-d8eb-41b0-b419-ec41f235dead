{"version": 3, "sources": ["webcrypto-shim.js"], "names": ["global", "factory", "define", "amd", "module", "exports", "self", "this", "Promise", "_subtle", "_Crypto", "_SubtleCrypto", "isEdge", "isIE", "isWebkit", "oid2str", "str2oid", "_digest", "_crypto", "crypto", "msCrypto", "s2a", "s", "btoa", "replace", "a2s", "slice", "length", "atob", "s2b", "b", "Uint8Array", "i", "charCodeAt", "b2s", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "String", "fromCharCode", "apply", "alg", "a", "r", "name", "toUpperCase", "hash", "publicExponent", "modulus<PERSON>ength", "SyntaxError", "jwkAlg", "HMAC", "SHA-1", "SHA-256", "SHA-384", "SHA-512", "RSASSA-PKCS1-v1_5", "RSAES-PKCS1-v1_5", "", "RSA-OAEP", "AES-KW", "128", "192", "256", "AES-GCM", "AES-CBC", "b2jwk", "k", "JSON", "parse", "decodeURIComponent", "escape", "jwk", "kty", "ext", "extractable", "for<PERSON>ach", "x", "TypeError", "b2der", "buf", "ctx", "pos", "end", "RangeError", "rv", "tag", "len", "xlen", "subarray", "Error", "buffer", "oid", "push", "toString", "der2b", "val", "byteLength", "Array", "value", "splice", "CryptoKey", "key", "use", "Object", "defineProperties", "_key", "type", "enumerable", "undefined", "algorithm", "usages", "isPubKeyUse", "u", "isPrvKeyUse", "subtle", "webkitSubtle", "Crypto", "constructor", "SubtleCrypto", "Key", "navigator", "userAgent", "indexOf", "KoZIhvcNAQEB", "1.2.840.113549.1.1.1", "m", "_fn", "c", "ka", "kx", "ku", "op", "args", "call", "arguments", "key_ops", "filter", "unescape", "encodeURIComponent", "stringify", "importKey", "getRandomValues", "<PERSON><PERSON>ey", "then", "all", "exportKey", "public<PERSON>ey", "privateKey", "keys", "info", "prv", "shift", "rsaComp", "rsaKey", "pkcs2jwk", "decrypt", "e", "reject", "res", "rej", "<PERSON>ab<PERSON>", "onerror", "oncomplete", "target", "result", "search", "encrypt", "set", "unshift", "jwk2pkcs", "d", "tl", "tag<PERSON><PERSON><PERSON>", "t", "AesGcmEncryptResult", "ciphertext", "digest", "create"], "mappings": ";CAKC,SAAUA,EAAQC,GACO,mBAAXC,QAAyBA,OAAOC,IAEvCD,OAAO,GAAI,WACP,OAAOD,EAAQD,KAEM,iBAAXI,QAAuBA,OAAOC,QAE5CD,OAAOC,QAAUJ,EAAQD,GAEzBC,EAAQD,GAVhB,CAYkB,oBAATM,KAAuBA,KAAOC,KAAM,SAAUP,gBAGnD,GAAwB,mBAAZQ,QACR,KAAM,2BAEV,IAGIC,EAGAC,EACAC,EAGAC,EACAC,EACAC,EAiKAC,EACAC,EAkYIC,EAhjBJC,EAAUlB,EAAOmB,QAAUnB,EAAOoB,SAetC,SAASC,EAAMC,GACX,OAAOC,KAAKD,GAAGE,QAAQ,OAAQ,IAAIA,QAAQ,MAAO,KAAKA,QAAQ,MAAO,KAG1E,SAASC,EAAMH,GAEX,OADYA,GAAZA,GAAK,OAAaI,MAAO,GAAIJ,EAAEK,OAAS,GACjCC,KAAMN,EAAEE,QAAQ,KAAM,KAAKA,QAAQ,KAAM,MAGpD,SAASK,EAAMP,GAEX,IADA,IAAIQ,EAAI,IAAIC,WAAWT,EAAEK,QACfK,EAAI,EAAGA,EAAIV,EAAEK,OAAQK,IAAMF,EAAEE,GAAKV,EAAEW,WAAWD,GACzD,OAAOF,EAGX,SAASI,EAAMJ,GAEX,OADKA,aAAaK,cAAcL,EAAI,IAAIC,WAAWD,IAC5CM,OAAOC,aAAaC,MAAOF,OAAQN,GAG9C,SAASS,EAAMC,GACX,IAAIC,EAAI,CAAEC,MAASF,EAAEE,MAAQF,GAAK,IAAIG,cAAcnB,QAAQ,IAAI,MAChE,OAASiB,EAAEC,MACP,IAAK,QACL,IAAK,UACL,IAAK,UACL,IAAK,UACD,MACJ,IAAK,UACL,IAAK,UACL,IAAK,SACIF,EAAEb,SAASc,EAAU,OAAID,EAAEb,QAChC,MACJ,IAAK,OACIa,EAAEI,OAAOH,EAAQ,KAAIF,EAAIC,EAAEI,OAC3BJ,EAAEb,SAASc,EAAU,OAAID,EAAEb,QAChC,MACJ,IAAK,mBACIa,EAAEK,iBAAiBJ,EAAkB,eAAI,IAAIV,WAAWS,EAAEK,iBAC1DL,EAAEM,gBAAgBL,EAAiB,cAAID,EAAEM,eAC9C,MACJ,IAAK,oBACL,IAAK,WACIN,EAAEI,OAAOH,EAAQ,KAAIF,EAAIC,EAAEI,OAC3BJ,EAAEK,iBAAiBJ,EAAkB,eAAI,IAAIV,WAAWS,EAAEK,iBAC1DL,EAAEM,gBAAgBL,EAAiB,cAAID,EAAEM,eAC9C,MACJ,QACI,MAAM,IAAIC,YAAY,sBAE9B,OAAON,EAGX,SAASO,EAASR,GACd,MAAO,CACHS,KAAQ,CACJC,QAAS,MACTC,UAAW,QACXC,UAAW,QACXC,UAAW,SAEfC,oBAAqB,CACjBJ,QAAS,MACTC,UAAW,QACXC,UAAW,QACXC,UAAW,SAEfE,mBAAoB,CAChBC,GAAI,UAERC,WAAY,CACRP,QAAS,WACTC,UAAW,gBAEfO,SAAU,CACNC,IAAO,SACPC,IAAO,SACPC,IAAO,UAEXC,UAAW,CACPH,IAAO,UACPC,IAAO,UACPC,IAAO,WAEXE,UAAW,CACPJ,IAAO,UACPC,IAAO,UACPC,IAAO,YAEbrB,EAAEE,OAASF,EAAEI,MAAQ,IAAKF,MAAQF,EAAEb,QAAU,IAGpD,SAASqC,EAAQC,IACRA,aAAa9B,aAAe8B,aAAalC,cAAakC,EAAIC,KAAKC,MAAOC,mBAAoBC,OAAQnC,EAAI+B,OAC3G,IAAIK,EAAM,CAAEC,IAAON,EAAEM,IAAKhC,IAAO0B,EAAE1B,IAAKiC,IAAOP,EAAEO,KAAOP,EAAEQ,aAC1D,OAASH,EAAIC,KACT,IAAK,MACDD,EAAIL,EAAIA,EAAEA,EACd,IAAK,MACD,CAAE,IAAK,IAAK,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,OAAQS,QAAS,SAAWC,GAAWA,KAAKV,IAAIK,EAAIK,GAAKV,EAAEU,MACxG,MACJ,QACI,MAAM,IAAIC,UAAU,wBAE5B,OAAON,EAyDX,SAASO,EAAQC,EAAKC,GAIlB,GAHKD,aAAe3C,cAAc2C,EAAM,IAAI/C,WAAW+C,KAC3CC,EAANA,GAAY,CAAEC,IAAK,EAAGC,IAAKH,EAAInD,SAE5BsD,IAAMF,EAAIC,IAAM,GAAKD,EAAIE,IAAMH,EAAInD,OAAS,MAAM,IAAIuD,WAAW,iBAE1E,IAYIC,EAZAC,EAAMN,EAAIC,EAAIC,OACdK,EAAMP,EAAIC,EAAIC,OAElB,GAAY,KAAPK,EAAc,CAEf,GADAA,GAAO,IACFN,EAAIE,IAAMF,EAAIC,IAAMK,EAAM,MAAM,IAAIH,WAAW,iBACpD,IAAM,IAAII,EAAO,EAAGD,KAASC,IAAS,EAAGA,GAAQR,EAAIC,EAAIC,OACzDK,EAAMC,EAGV,GAAKP,EAAIE,IAAMF,EAAIC,IAAMK,EAAM,MAAM,IAAIH,WAAW,iBAIpD,OAASE,GACL,KAAK,EACDD,EAAKL,EAAIS,SAAUR,EAAIC,IAAKD,EAAIC,KAAOK,GACvC,MACJ,KAAK,EACD,GAAKP,EAAIC,EAAIC,OAAS,MAAM,IAAIQ,MAAO,0BACvCH,IACJ,KAAK,EACDF,EAAK,IAAIpD,WAAY+C,EAAIS,SAAUR,EAAIC,IAAKD,EAAIC,KAAOK,IAAQI,OAC/D,MACJ,KAAK,EACDN,EAAK,KACL,MACJ,KAAK,EACD,IAAIO,EAAMnE,KAAMW,EAAK4C,EAAIS,SAAUR,EAAIC,IAAKD,EAAIC,KAAOK,KACvD,KAAQK,KAAO3E,GAAY,MAAM,IAAIyE,MAAO,yBAA2BE,GACvEP,EAAKpE,EAAQ2E,GACb,MACJ,KAAK,GACDP,EAAK,GACL,IAAM,IAAIF,EAAMF,EAAIC,IAAMK,EAAKN,EAAIC,IAAMC,GAAOE,EAAGQ,KAAMd,EAAOC,EAAKC,IACrE,MACJ,QACI,MAAM,IAAIS,MAAO,yBAA2BJ,EAAIQ,SAAS,KAGjE,OAAOT,EAGX,SAASU,EAAQC,EAAKhB,GAGlB,IAAIM,EAAM,EAAGC,EAAM,EACfL,GAHQF,EAANA,GAAY,IAGJnD,OAAS,EAIvB,GAFAmD,EAAIa,KAAM,EAAG,GAERG,aAAe/D,WAAa,CAC7BqD,EAAM,EAAMC,EAAMS,EAAInE,OACtB,IAAM,IAAIK,EAAI,EAAGA,EAAIqD,EAAKrD,IAAM8C,EAAIa,KAAMG,EAAI9D,SAE7C,GAAK8D,aAAe3D,YAAc,CACnCiD,EAAM,EAAMC,EAAMS,EAAIC,WAAYD,EAAM,IAAI/D,WAAW+D,GACvD,IAAU9D,EAAI,EAAGA,EAAIqD,EAAKrD,IAAM8C,EAAIa,KAAMG,EAAI9D,SAE7C,GAAa,OAAR8D,EACNV,EAAM,EAAMC,EAAM,OAEjB,GAAoB,iBAARS,GAAoBA,KAAO9E,EAAU,CAClD,IAAI0E,EAAM7D,EAAKD,KAAMZ,EAAQ8E,KAC7BV,EAAM,EAAMC,EAAMK,EAAI/D,OACtB,IAAUK,EAAI,EAAGA,EAAIqD,EAAKrD,IAAM8C,EAAIa,KAAMD,EAAI1D,SAE7C,GAAK8D,aAAeE,MAAQ,CAC7B,IAAUhE,EAAI,EAAGA,EAAI8D,EAAInE,OAAQK,IAAM6D,EAAOC,EAAI9D,GAAI8C,GACtDM,EAAM,GAAMC,EAAMP,EAAInD,OAASqD,MAE9B,CAAA,KAAoB,iBAARc,GAAgC,IAAZA,EAAIV,KAAgBU,EAAIG,iBAAiB9D,aAM1E,MAAM,IAAIqD,MAAO,yBAA2BM,GALXV,EAAM,EAAMC,GAA7CS,EAAM,IAAI/D,WAAW+D,EAAIG,QAA8BF,WACvDjB,EAAIa,KAAK,GAAI,IAAU3D,EAAI,EAAGA,EAAIqD,EAAKrD,IAAM8C,EAAIa,KAAMG,EAAI9D,IAC3DqD,IAMJ,GAAY,KAAPA,EAAc,CACf,IAAIC,EAAOD,EAAKA,EAAM,EAEtB,IADAP,EAAIoB,OAAQlB,EAAK,EAAIM,GAAQ,GAAM,IAAOA,GAAQ,GAAM,IAAOA,GAAQ,EAAK,IAAa,IAAPA,GACpE,EAAND,KAAaC,GAAQ,KAAMA,IAAS,EAAGD,IAC1CA,EAAM,GAAIP,EAAIoB,OAAQlB,EAAK,EAAIK,GACpCA,GAAO,IAKX,OAFAP,EAAIoB,OAAQlB,EAAM,EAAG,EAAGI,EAAKC,GAEtBP,EAGX,SAASqB,EAAYC,EAAK7D,EAAKiC,EAAK6B,GAChCC,OAAOC,iBAAkBhG,KAAM,CAC3BiG,KAAM,CACFP,MAAOG,GAEXK,KAAM,CACFR,MAAOG,EAAIK,KACXC,YAAY,GAEhBjC,YAAa,CACTwB,WAAgBU,IAARnC,EAAqB4B,EAAI3B,YAAcD,EAC/CkC,YAAY,GAEhBE,UAAW,CACPX,WAAgBU,IAARpE,EAAqB6D,EAAIQ,UAAYrE,EAC7CmE,YAAY,GAEhBG,OAAQ,CACJZ,WAAgBU,IAARN,EAAqBD,EAAIS,OAASR,EAC1CK,YAAY,KAKxB,SAASI,EAAcC,GACnB,MAAa,WAANA,GAAwB,YAANA,GAAyB,YAANA,EAGhD,SAASC,EAAcD,GACnB,MAAa,SAANA,GAAsB,YAANA,GAAyB,cAANA,GA/SxC7F,IAEFT,EAAUS,EAAQ+F,QAAU/F,EAAQgG,gBAGpCxG,EAAcV,EAAOmH,QAAUjG,EAAQkG,aAAed,OACtD3F,EAAgBX,EAAOqH,cAAgB5G,EAAQ2G,aAAed,OAChDtG,EAAOmG,WAAanG,EAAOsH,KAAOhB,OAEhD1F,GAAwD,EAA/CZ,EAAOuH,UAAUC,UAAUC,QAAQ,SAC5C5G,IAAYb,EAAOoB,WAAaR,EAChCE,GAAYI,EAAQ+F,UAAY/F,EAAQgG,cACtCrG,GAASC,KAgKXC,EAAU,CAAE2G,aAAgB,wBAC5B1G,EAAU,CAAE2G,uBAAwB,gBAqIxC,CAAE,cAAe,YAAa,aACzBjD,QAAS,SAAWkD,GACjB,IAAIC,EAAMpH,EAAQmH,GAElBnH,EAAQmH,GAAK,SAAWpF,EAAGV,EAAGgG,GAC1B,IACIC,EAAIC,EAAIC,EA9LhB3D,EAiQQ4D,EApEAC,EAAO,GAAGzG,MAAM0G,KAAKC,WAGzB,OAAST,GACL,IAAK,cACDG,EAAKxF,EAAIC,GAAIwF,EAAKlG,EAAGmG,EAAKH,EAC1B,MACJ,IAAK,YACDC,EAAKxF,EAAIuF,GAAIE,EAAKG,EAAK,GAAIF,EAAKE,EAAK,GAC1B,QAAN3F,KACDV,EAAIkC,EAAMlC,IACFS,MAAMT,EAAES,IAAMS,EAAO+E,IACvBjG,EAAEwG,UAAUxG,EAAEwG,QAAsB,QAAVxG,EAAEyC,IAAoB,MAAOzC,EAAMmG,EAAGM,OAAOvB,GAAeiB,EAAGM,OAAOzB,GAAemB,EAAGvG,SACxHyG,EAAK,IA1MrB7D,EAAMN,EA0M0BlC,GAzM/BjB,IAAOyD,EAAiB,YAAIA,EAAIE,WAAYF,EAAIE,KAC9C3C,EAAK2G,SAAUC,mBAAoBvE,KAAKwE,UAAUpE,MAAWmB,SA0MpD,MACJ,IAAK,YACDsC,EAAKI,EAAK,GAAIH,EAAKG,EAAK,GAAIF,EAAKE,EAAK,GACtCA,EAAK,GAAKL,EAAEtB,KAIpB,GAAW,gBAANoB,GAAmC,SAAZG,EAAGrF,MAAmBqF,EAAGnF,KAEjD,OADAmF,EAAGpG,OAASoG,EAAGpG,QAAU,CAAEuB,QAAS,IAAKC,UAAW,IAAKC,UAAW,KAAMC,UAAW,MAAO0E,EAAGnF,KAAKF,MAC7FjC,EAAQkI,UAAW,MAAOzH,EAAQ0H,gBAAiB,IAAI7G,WAAagG,EAAGpG,OAAO,GAAI,IAAOoG,EAAIC,EAAIC,GAG5G,GAAKnH,GAAkB,gBAAN8G,GAAmC,sBAAZG,EAAGrF,QAAmCqF,EAAGjF,eAAqC,MAApBiF,EAAGjF,eAEjG,OADAN,EAAID,EAAIC,IAAME,KAAO,0BAA2BF,EAAEI,KAC3CnC,EAAQoI,YAAarG,GAAG,EAAM,CAAE,UAAW,YAC7CsG,KAAM,SAAW7E,GACd,OAAOzD,QAAQuI,IAAI,CACftI,EAAQuI,UAAW,MAAO/E,EAAEgF,WAC5BxI,EAAQuI,UAAW,MAAO/E,EAAEiF,gBAGnCJ,KAAM,SAAWK,GAGd,OAFAA,EAAK,GAAG5G,IAAM4G,EAAK,GAAG5G,IAAMS,EAAO+E,GACnCoB,EAAK,GAAGb,QAAUL,EAAGM,OAAOzB,GAAcqC,EAAK,GAAGb,QAAUL,EAAGM,OAAOvB,GAC/DxG,QAAQuI,IAAI,CACftI,EAAQkI,UAAW,MAAOQ,EAAK,GAAIpB,GAAI,EAAMoB,EAAK,GAAGb,SACrD7H,EAAQkI,UAAW,MAAOQ,EAAK,GAAIpB,EAAIC,EAAImB,EAAK,GAAGb,aAG1DQ,KAAM,SAAWK,GACd,MAAO,CACHF,UAAWE,EAAK,GAChBD,WAAYC,EAAK,MAKjC,IAAOrI,GAAcD,GAAmC,WAAzBkH,EAAGnF,MAAQ,IAAKF,OAC9B,cAANkF,GAA2B,QAANpF,GAA2B,SAAZuF,EAAGrF,MAA6B,QAAVZ,EAAEyC,IACnE,OAAO9D,EAAQkI,UAAW,MAAO9G,EAAKJ,EAAIK,EAAEmC,IAAM6D,EAAGK,EAAK,GAAIA,EAAK,IAGvE,GAAKrH,GAAkB,cAAN8G,IAA6B,SAANpF,GAAsB,UAANA,GACpD,OAAO/B,EAAQkI,UAAW,MAlP1C,SAAoB1E,GAChB,IAAImF,EAAOvE,EAAMZ,GAAIoF,GAAM,EACR,EAAdD,EAAKzH,SAAa0H,GAAM,EAAMD,EAAKE,SACxC,IAAIhF,EAAM,CAAEE,KAAO,GACnB,GACS,yBADA4E,EAAK,GAAG,GAYT,MAAM,IAAIxE,UAAU,wBAVpB,IAAI2E,EAAU,CAAE,IAAK,IAAK,IAAK,IAAK,IAAK,KAAM,KAAM,MACjDC,EAAU3E,EAAOuE,EAAK,IACrBC,GAAMG,EAAOF,QAClB,IAAM,IAAItH,EAAI,EAAGA,EAAIwH,EAAO7H,OAAQK,IAC1BwH,EAAOxH,GAAG,KAAKwH,EAAOxH,GAAKwH,EAAOxH,GAAGuD,SAAS,IACpDjB,EAAKiF,EAAQvH,IAAOX,EAAKa,EAAKsH,EAAOxH,KAOjD,OALQsC,EAAS,IAAI,MAKdA,EAgOsCmF,CAAS3H,GAAIgG,EAAGK,EAAK,GAAIA,EAAK,IAGnE,GAAKtH,GAAc,cAAN+G,EACT,OAAOnH,EAAQiJ,QAASvB,EAAK,GAAIL,EAAGhG,GAC/BgH,KAAM,SAAW7E,GACd,OAAOxD,EAAQkI,UAAWnG,EAAGyB,EAAGkE,EAAK,GAAIA,EAAK,GAAIA,EAAK,MAKnE,IACID,EAAKL,EAAIvF,MAAO7B,EAAS0H,GAE7B,MAAQwB,GACJ,OAAOnJ,QAAQoJ,OAAOD,GA+B1B,OA5BK9I,IACDqH,EAAK,IAAI1H,QAAS,SAAWqJ,EAAKC,GAC9B5B,EAAG6B,QACH7B,EAAG8B,QAAa,SAAWL,GAAMG,EAAIH,IACrCzB,EAAG+B,WAAa,SAAWxH,GAAMoH,EAAIpH,EAAEyH,OAAOC,YAItDjC,EAAKA,EAAGY,KAAM,SAAW7E,GAiBrB,MAhBiB,SAAZ8D,EAAGrF,OACEqF,EAAGpG,SAASoG,EAAGpG,OAAS,EAAIsC,EAAE2C,UAAUjF,SAEpB,GAAzBoG,EAAGrF,KAAK0H,OAAO,SACVrC,EAAGjF,gBAAgBiF,EAAGjF,eAAiBmB,EAAEgF,WAAahF,GAAG2C,UAAU9D,eACnEiF,EAAGlF,iBAAiBkF,EAAGlF,gBAAkBoB,EAAEgF,WAAahF,GAAG2C,UAAU/D,iBAG3EoB,EADCA,EAAEgF,WAAahF,EAAEiF,WACd,CACAD,UAAW,IAAI9C,EAAWlC,EAAEgF,UAAWlB,EAAIC,EAAIC,EAAGM,OAAOzB,IACzDoC,WAAY,IAAI/C,EAAWlC,EAAEiF,WAAYnB,EAAIC,EAAIC,EAAGM,OAAOvB,KAI3D,IAAIb,EAAWlC,EAAG8D,EAAIC,EAAIC,QASlD,CAAE,YAAa,WACVvD,QAAS,SAAWkD,GACjB,IAAIC,EAAMpH,EAAQmH,GAElBnH,EAAQmH,GAAK,SAAWpF,EAAGV,EAAGgG,GAC1B,IA4BII,EA5BAC,EAAO,GAAGzG,MAAM0G,KAAKC,WAEzB,OAAST,GACL,IAAK,YACDO,EAAK,GAAKrG,EAAE0E,KACZ,MACJ,IAAK,UACD2B,EAAK,GAAKrG,EAAE0E,KAAM2B,EAAK,GAAKL,EAAEtB,KAatC,IATO1F,GAAcD,GAA4C,WAAlCiB,EAAE8E,UAAUhE,MAAQ,IAAKF,OACvC,cAANkF,GAA2B,QAANpF,GAAoC,SAArBV,EAAE8E,UAAUlE,OACvDyF,EAAK,GAAK,QAGTrH,GAAkB,cAAN8G,GAA6B,SAANpF,GAAsB,UAANA,IACpD2F,EAAK,GAAK,OAGTtH,GAAc,YAAN+G,EACT,OAAOnH,EAAQuI,UAAWxG,EAAGV,GACxBgH,KAAM,SAAW7E,GAEd,MADW,QAANzB,IAAcyB,EAAIpC,EAAK2G,SAAUC,mBAAoBvE,KAAKwE,UAAW1E,EAAMC,QACxExD,EAAQ4J,QAASlC,EAAK,GAAIL,EAAG7D,KAKjD,IACIiE,EAAKL,EAAIvF,MAAO7B,EAAS0H,GAE7B,MAAQwB,GACJ,OAAOnJ,QAAQoJ,OAAOD,GA+B1B,OA5BK9I,IACDqH,EAAK,IAAI1H,QAAS,SAAWqJ,EAAKC,GAC9B5B,EAAG6B,QACH7B,EAAG8B,QAAa,SAAWL,GAAMG,EAAIH,IACrCzB,EAAG+B,WAAa,SAAWxH,GAAMoH,EAAIpH,EAAEyH,OAAOC,YAI3C,cAANvC,GAA2B,QAANpF,IACtB0F,EAAKA,EAAGY,KAAM,SAAW7E,GACrB,OAAOnD,GAAcD,GAA4C,WAAlCiB,EAAE8E,UAAUhE,MAAQ,IAAKF,OACxB,SAArBZ,EAAE8E,UAAUlE,KACZ,CAAE6B,IAAO,MAAOhC,IAAOS,EAAOlB,EAAE8E,WAAY0B,QAAWxG,EAAE+E,OAAOnF,QAAS8C,KAAO,EAAMP,EAAK5C,EAAKa,EAAI+B,OAE/GA,EAAID,EAAMC,IACF1B,MAAM0B,EAAO,IAAIjB,EAAOlB,EAAE8E,YAC5B3C,EAAEqE,UAAUrE,EAAW,QAAiB,WAAXnC,EAAE2E,KAAsB3E,EAAE+E,OAAO0B,OAAOzB,GAA4B,YAAXhF,EAAE2E,KAAuB3E,EAAE+E,OAAO0B,OAAOvB,GAAelF,EAAE+E,OAAOnF,SACtJuC,OAIVnD,GAAkB,cAAN8G,GAA6B,SAANpF,GAAsB,UAANA,IACpD0F,EAAKA,EAAGY,KAAM,SAAW7E,GAErB,OADAA,EA/UpB,SAAoBA,GAChB,IAASmF,EAAO,CAAE,CAAE,GAAI,OAAUC,GAAM,EACxC,GACS,QADApF,EAAEM,IAcH,MAAM,IAAIK,UAAU,wBAVpB,IAFA,IAAI2E,EAAU,CAAE,IAAK,IAAK,IAAK,IAAK,IAAK,KAAM,KAAM,MACjDC,EAAS,GACHxH,EAAI,EAAGA,EAAIuH,EAAQ5H,QACjB4H,EAAQvH,KAAMiC,EADWjC,IAAM,CAEvC,IAAIF,EAAI0H,EAAOxH,GAAKH,EAAKJ,EAAKwC,EAAGsF,EAAQvH,MAC7B,IAAPF,EAAE,KAAY0H,EAAOxH,GAAK,IAAID,WAAWD,EAAEH,OAAS,GAAI6H,EAAOxH,GAAGsI,IAAKxI,EAAG,IAY3F,OAV6B,EAAhB0H,EAAO7H,SAAa0H,GAAM,EAAMG,EAAOe,QAAS,IAAIxI,WAAW,CAAC,MACrEqH,EAAK,GAAG,GAAK,uBAMrBA,EAAKzD,KAAM,IAAI5D,WAAY8D,EALb2D,IAK0B/D,QAClC4D,EACDD,EAAKmB,QAAS,IAAIxI,WAAW,CAAC,KADvBqH,EAAK,GAAK,CAAEhE,IAAO,EAAMa,MAASmD,EAAK,IAE5C,IAAIrH,WAAY8D,EAAMuD,IAAQ3D,OA0TjB+E,CAAUxG,EAAMC,OAKrBiE,KAInB,CAAE,UAAW,UAAW,OAAQ,UAC3BxD,QAAS,SAAWkD,GACjB,IAAIC,EAAMpH,EAAQmH,GAElBnH,EAAQmH,GAAK,SAAWpF,EAAGV,EAAGgG,EAAG2C,GAC7B,GAAK5J,KAAWiH,EAAE/B,YAAgB0E,IAAMA,EAAE1E,YACtC,MAAM,IAAIP,MAAM,8BAEpB,IAYQkF,EAWJxC,EAvBAC,EAAO,GAAGzG,MAAM0G,KAAKC,WACrBN,EAAKxF,EAAIC,IAER3B,GAAgB,SAAN+G,GAAsB,WAANA,GAA2B,sBAANpF,GAAmC,SAANA,IAC7E2F,EAAK,GAAK,CAAEzF,KAAMF,IAGjB3B,GAAQiB,EAAE8E,UAAUhE,OACrBuF,EAAK,GAAGvF,KAAOuF,EAAK,GAAGvF,MAAQd,EAAE8E,UAAUhE,MAG1C/B,GAAc,YAAN+G,GAA+B,YAAZG,EAAGrF,OAC3BgI,EAAKlI,EAAEmI,WAAa,EACxBxC,EAAK,IAAML,EAAErC,QAAUqC,GAAGpG,MAAO,EAAGoG,EAAE/B,WAAa2E,GACnDlI,EAAE4C,KAAO0C,EAAErC,QAAUqC,GAAGpG,MAAOoG,EAAE/B,WAAa2E,IAG7C7J,GAAoB,YAAZkH,EAAGrF,WAA4CiE,IAAtBwB,EAAK,GAAGwC,YAC1CxC,EAAK,GAAGwC,UAAY,KAGxBxC,EAAK,GAAKrG,EAAE0E,KAGZ,IACI0B,EAAKL,EAAIvF,MAAO7B,EAAS0H,GAE7B,MAAQwB,GACJ,OAAOnJ,QAAQoJ,OAAOD,GA0B1B,OAvBK9I,IACDqH,EAAK,IAAI1H,QAAS,SAAWqJ,EAAKC,GAC9B5B,EAAG6B,QACH7B,EAAG8B,QAAU,SAAWL,GACpBG,EAAIH,IAGRzB,EAAG+B,WAAa,SAAWxH,GACvB,IAGQqF,EAAkB8C,EAHtBnI,EAAIA,EAAEyH,OAAOC,OAEN,YAANvC,GAAmBnF,aAAaoI,sBAC7B/C,EAAIrF,EAAEqI,WAAYF,EAAInI,EAAE2C,KAC5B3C,EAAI,IAAIV,WAAY+F,EAAE/B,WAAa6E,EAAE7E,aACnCuE,IAAK,IAAIvI,WAAW+F,GAAI,GAC1BrF,EAAE6H,IAAK,IAAIvI,WAAW6I,GAAI9C,EAAE/B,YAC5BtD,EAAIA,EAAEgD,QAGVoE,EAAIpH,OAKTyF,KAIdrH,IACGI,EAAUR,EAAQsK,OAEtBtK,EAAgB,OAAI,SAAW+B,EAAGV,GAC9B,IAAMA,EAAEiE,WACJ,MAAM,IAAIP,MAAM,8BAEpB,IAAI0C,EACJ,IACIA,EAAKjH,EAAQmH,KAAM3H,EAAS+B,EAAGV,GAEnC,MAAQ6H,GACJ,OAAOnJ,QAAQoJ,OAAOD,GAS1B,OANAzB,EAAK,IAAI1H,QAAS,SAAWqJ,EAAKC,GAC9B5B,EAAG6B,QACH7B,EAAG8B,QAAa,SAAWL,GAAMG,EAAIH,IACrCzB,EAAG+B,WAAa,SAAWxH,GAAMoH,EAAIpH,EAAEyH,OAAOC,YAMtDnK,EAAOmB,OAASmF,OAAO0E,OAAQ9J,EAAS,CACpC0H,gBAAiB,CAAE3C,MAAO,SAAWzD,GAAM,OAAOtB,EAAQ0H,gBAAgBpG,KAC1EyE,OAAiB,CAAEhB,MAAOxF,KAG9BT,EAAOmG,UAAYA,GAGlBrF,IACDI,EAAQ+F,OAASxG,EAEjBT,EAAOmH,OAASzG,EAChBV,EAAOqH,aAAe1G,EACtBX,EAAOmG,UAAYA", "file": "webcrypto-shim.min.js", "sourcesContent": ["/**\n * @file Web Cryptography API shim\n * <AUTHOR> <v<PERSON><PERSON><PERSON>@gmail.com>\n * @license MIT\n */\n(function (global, factory) {\n    if (typeof define === 'function' && define.amd) {\n        // AMD. Register as an anonymous module.\n        define([], function () {\n            return factory(global);\n        });\n    } else if (typeof module === 'object' && module.exports) {\n        // CommonJS-like environments that support module.exports\n        module.exports = factory(global);\n    } else {\n        factory(global);\n    }\n}(typeof self !== 'undefined' ? self : this, function (global) {\n    'use strict';\n\n    if ( typeof Promise !== 'function' )\n        throw \"Promise support required\";\n\n    var _crypto = global.crypto || global.msCrypto;\n    if ( !_crypto ) return;\n\n    var _subtle = _crypto.subtle || _crypto.webkitSubtle;\n    if ( !_subtle ) return;\n\n    var _Crypto     = global.Crypto || _crypto.constructor || Object,\n        _SubtleCrypto = global.SubtleCrypto || _subtle.constructor || Object,\n        _CryptoKey  = global.CryptoKey || global.Key || Object;\n\n    var isEdge = global.navigator.userAgent.indexOf('Edge/') > -1;\n    var isIE    = !!global.msCrypto && !isEdge;\n    var isWebkit = !_crypto.subtle && !!_crypto.webkitSubtle;\n    if ( !isIE && !isWebkit ) return;\n\n    function s2a ( s ) {\n        return btoa(s).replace(/\\=+$/, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n    }\n\n    function a2s ( s ) {\n        s += '===', s = s.slice( 0, -s.length % 4 );\n        return atob( s.replace(/-/g, '+').replace(/_/g, '/') );\n    }\n\n    function s2b ( s ) {\n        var b = new Uint8Array(s.length);\n        for ( var i = 0; i < s.length; i++ ) b[i] = s.charCodeAt(i);\n        return b;\n    }\n\n    function b2s ( b ) {\n        if ( b instanceof ArrayBuffer ) b = new Uint8Array(b);\n        return String.fromCharCode.apply( String, b );\n    }\n\n    function alg ( a ) {\n        var r = { 'name': (a.name || a || '').toUpperCase().replace('V','v') };\n        switch ( r.name ) {\n            case 'SHA-1':\n            case 'SHA-256':\n            case 'SHA-384':\n            case 'SHA-512':\n                break;\n            case 'AES-CBC':\n            case 'AES-GCM':\n            case 'AES-KW':\n                if ( a.length ) r['length'] = a.length;\n                break;\n            case 'HMAC':\n                if ( a.hash ) r['hash'] = alg(a.hash);\n                if ( a.length ) r['length'] = a.length;\n                break;\n            case 'RSAES-PKCS1-v1_5':\n                if ( a.publicExponent ) r['publicExponent'] = new Uint8Array(a.publicExponent);\n                if ( a.modulusLength ) r['modulusLength'] = a.modulusLength;\n                break;\n            case 'RSASSA-PKCS1-v1_5':\n            case 'RSA-OAEP':\n                if ( a.hash ) r['hash'] = alg(a.hash);\n                if ( a.publicExponent ) r['publicExponent'] = new Uint8Array(a.publicExponent);\n                if ( a.modulusLength ) r['modulusLength'] = a.modulusLength;\n                break;\n            default:\n                throw new SyntaxError(\"Bad algorithm name\");\n        }\n        return r;\n    };\n\n    function jwkAlg ( a ) {\n        return {\n            'HMAC': {\n                'SHA-1': 'HS1',\n                'SHA-256': 'HS256',\n                'SHA-384': 'HS384',\n                'SHA-512': 'HS512',\n            },\n            'RSASSA-PKCS1-v1_5': {\n                'SHA-1': 'RS1',\n                'SHA-256': 'RS256',\n                'SHA-384': 'RS384',\n                'SHA-512': 'RS512',\n            },\n            'RSAES-PKCS1-v1_5': {\n                '': 'RSA1_5',\n            },\n            'RSA-OAEP': {\n                'SHA-1': 'RSA-OAEP',\n                'SHA-256': 'RSA-OAEP-256',\n            },\n            'AES-KW': {\n                '128': 'A128KW',\n                '192': 'A192KW',\n                '256': 'A256KW',\n            },\n            'AES-GCM': {\n                '128': 'A128GCM',\n                '192': 'A192GCM',\n                '256': 'A256GCM',\n            },\n            'AES-CBC': {\n                '128': 'A128CBC',\n                '192': 'A192CBC',\n                '256': 'A256CBC',\n            },\n        }[a.name][ ( a.hash || {} ).name || a.length || '' ];\n    }\n\n    function b2jwk ( k ) {\n        if ( k instanceof ArrayBuffer || k instanceof Uint8Array ) k = JSON.parse( decodeURIComponent( escape( b2s(k) ) ) );\n        var jwk = { 'kty': k.kty, 'alg': k.alg, 'ext': k.ext || k.extractable };\n        switch ( jwk.kty ) {\n            case 'oct':\n                jwk.k = k.k;\n            case 'RSA':\n                [ 'n', 'e', 'd', 'p', 'q', 'dp', 'dq', 'qi', 'oth' ].forEach( function ( x ) { if ( x in k ) jwk[x] = k[x] } );\n                break;\n            default:\n                throw new TypeError(\"Unsupported key type\");\n        }\n        return jwk;\n    }\n\n    function jwk2b ( k ) {\n        var jwk = b2jwk(k);\n        if ( isIE ) jwk['extractable'] = jwk.ext, delete jwk.ext;\n        return s2b( unescape( encodeURIComponent( JSON.stringify(jwk) ) ) ).buffer;\n    }\n\n    function pkcs2jwk ( k ) {\n        var info = b2der(k), prv = false;\n        if ( info.length > 2 ) prv = true, info.shift(); // remove version from PKCS#8 PrivateKeyInfo structure\n        var jwk = { 'ext': true };\n        switch ( info[0][0] ) {\n            case '1.2.840.113549.1.1.1':\n                var rsaComp = [ 'n', 'e', 'd', 'p', 'q', 'dp', 'dq', 'qi' ],\n                    rsaKey  = b2der( info[1] );\n                if ( prv ) rsaKey.shift(); // remove version from PKCS#1 RSAPrivateKey structure\n                for ( var i = 0; i < rsaKey.length; i++ ) {\n                    if ( !rsaKey[i][0] ) rsaKey[i] = rsaKey[i].subarray(1);\n                    jwk[ rsaComp[i] ] = s2a( b2s( rsaKey[i] ) );\n                }\n                jwk['kty'] = 'RSA';\n                break;\n            default:\n                throw new TypeError(\"Unsupported key type\");\n        }\n        return jwk;\n    }\n\n    function jwk2pkcs ( k ) {\n        var key, info = [ [ '', null ] ], prv = false;\n        switch ( k.kty ) {\n            case 'RSA':\n                var rsaComp = [ 'n', 'e', 'd', 'p', 'q', 'dp', 'dq', 'qi' ],\n                    rsaKey = [];\n                for ( var i = 0; i < rsaComp.length; i++ ) {\n                    if ( !( rsaComp[i] in k ) ) break;\n                    var b = rsaKey[i] = s2b( a2s( k[ rsaComp[i] ] ) );\n                    if ( b[0] & 0x80 ) rsaKey[i] = new Uint8Array(b.length + 1), rsaKey[i].set( b, 1 );\n                }\n                if ( rsaKey.length > 2 ) prv = true, rsaKey.unshift( new Uint8Array([0]) ); // add version to PKCS#1 RSAPrivateKey structure\n                info[0][0] = '1.2.840.113549.1.1.1';\n                key = rsaKey;\n                break;\n            default:\n                throw new TypeError(\"Unsupported key type\");\n        }\n        info.push( new Uint8Array( der2b(key) ).buffer );\n        if ( !prv ) info[1] = { 'tag': 0x03, 'value': info[1] };\n        else info.unshift( new Uint8Array([0]) ); // add version to PKCS#8 PrivateKeyInfo structure\n        return new Uint8Array( der2b(info) ).buffer;\n    }\n\n    var oid2str = { 'KoZIhvcNAQEB': '1.2.840.113549.1.1.1' },\n        str2oid = { '1.2.840.113549.1.1.1': 'KoZIhvcNAQEB' };\n\n    function b2der ( buf, ctx ) {\n        if ( buf instanceof ArrayBuffer ) buf = new Uint8Array(buf);\n        if ( !ctx ) ctx = { pos: 0, end: buf.length };\n\n        if ( ctx.end - ctx.pos < 2 || ctx.end > buf.length ) throw new RangeError(\"Malformed DER\");\n\n        var tag = buf[ctx.pos++],\n            len = buf[ctx.pos++];\n\n        if ( len >= 0x80 ) {\n            len &= 0x7f;\n            if ( ctx.end - ctx.pos < len ) throw new RangeError(\"Malformed DER\");\n            for ( var xlen = 0; len--; ) xlen <<= 8, xlen |= buf[ctx.pos++];\n            len = xlen;\n        }\n\n        if ( ctx.end - ctx.pos < len ) throw new RangeError(\"Malformed DER\");\n\n        var rv;\n\n        switch ( tag ) {\n            case 0x02: // Universal Primitive INTEGER\n                rv = buf.subarray( ctx.pos, ctx.pos += len );\n                break;\n            case 0x03: // Universal Primitive BIT STRING\n                if ( buf[ctx.pos++] ) throw new Error( \"Unsupported bit string\" );\n                len--;\n            case 0x04: // Universal Primitive OCTET STRING\n                rv = new Uint8Array( buf.subarray( ctx.pos, ctx.pos += len ) ).buffer;\n                break;\n            case 0x05: // Universal Primitive NULL\n                rv = null;\n                break;\n            case 0x06: // Universal Primitive OBJECT IDENTIFIER\n                var oid = btoa( b2s( buf.subarray( ctx.pos, ctx.pos += len ) ) );\n                if ( !( oid in oid2str ) ) throw new Error( \"Unsupported OBJECT ID \" + oid );\n                rv = oid2str[oid];\n                break;\n            case 0x30: // Universal Constructed SEQUENCE\n                rv = [];\n                for ( var end = ctx.pos + len; ctx.pos < end; ) rv.push( b2der( buf, ctx ) );\n                break;\n            default:\n                throw new Error( \"Unsupported DER tag 0x\" + tag.toString(16) );\n        }\n\n        return rv;\n    }\n\n    function der2b ( val, buf ) {\n        if ( !buf ) buf = [];\n\n        var tag = 0, len = 0,\n            pos = buf.length + 2;\n\n        buf.push( 0, 0 ); // placeholder\n\n        if ( val instanceof Uint8Array ) {  // Universal Primitive INTEGER\n            tag = 0x02, len = val.length;\n            for ( var i = 0; i < len; i++ ) buf.push( val[i] );\n        }\n        else if ( val instanceof ArrayBuffer ) { // Universal Primitive OCTET STRING\n            tag = 0x04, len = val.byteLength, val = new Uint8Array(val);\n            for ( var i = 0; i < len; i++ ) buf.push( val[i] );\n        }\n        else if ( val === null ) { // Universal Primitive NULL\n            tag = 0x05, len = 0;\n        }\n        else if ( typeof val === 'string' && val in str2oid ) { // Universal Primitive OBJECT IDENTIFIER\n            var oid = s2b( atob( str2oid[val] ) );\n            tag = 0x06, len = oid.length;\n            for ( var i = 0; i < len; i++ ) buf.push( oid[i] );\n        }\n        else if ( val instanceof Array ) { // Universal Constructed SEQUENCE\n            for ( var i = 0; i < val.length; i++ ) der2b( val[i], buf );\n            tag = 0x30, len = buf.length - pos;\n        }\n        else if ( typeof val === 'object' && val.tag === 0x03 && val.value instanceof ArrayBuffer ) { // Tag hint\n            val = new Uint8Array(val.value), tag = 0x03, len = val.byteLength;\n            buf.push(0); for ( var i = 0; i < len; i++ ) buf.push( val[i] );\n            len++;\n        }\n        else {\n            throw new Error( \"Unsupported DER value \" + val );\n        }\n\n        if ( len >= 0x80 ) {\n            var xlen = len, len = 4;\n            buf.splice( pos, 0, (xlen >> 24) & 0xff, (xlen >> 16) & 0xff, (xlen >> 8) & 0xff, xlen & 0xff );\n            while ( len > 1 && !(xlen >> 24) ) xlen <<= 8, len--;\n            if ( len < 4 ) buf.splice( pos, 4 - len );\n            len |= 0x80;\n        }\n\n        buf.splice( pos - 2, 2, tag, len );\n\n        return buf;\n    }\n\n    function CryptoKey ( key, alg, ext, use ) {\n        Object.defineProperties( this, {\n            _key: {\n                value: key\n            },\n            type: {\n                value: key.type,\n                enumerable: true,\n            },\n            extractable: {\n                value: (ext === undefined) ? key.extractable : ext,\n                enumerable: true,\n            },\n            algorithm: {\n                value: (alg === undefined) ? key.algorithm : alg,\n                enumerable: true,\n            },\n            usages: {\n                value: (use === undefined) ? key.usages : use,\n                enumerable: true,\n            },\n        });\n    }\n\n    function isPubKeyUse ( u ) {\n        return u === 'verify' || u === 'encrypt' || u === 'wrapKey';\n    }\n\n    function isPrvKeyUse ( u ) {\n        return u === 'sign' || u === 'decrypt' || u === 'unwrapKey';\n    }\n\n    [ 'generateKey', 'importKey', 'unwrapKey' ]\n        .forEach( function ( m ) {\n            var _fn = _subtle[m];\n\n            _subtle[m] = function ( a, b, c ) {\n                var args = [].slice.call(arguments),\n                    ka, kx, ku;\n\n                switch ( m ) {\n                    case 'generateKey':\n                        ka = alg(a), kx = b, ku = c;\n                        break;\n                    case 'importKey':\n                        ka = alg(c), kx = args[3], ku = args[4];\n                        if ( a === 'jwk' ) {\n                            b = b2jwk(b);\n                            if ( !b.alg ) b.alg = jwkAlg(ka);\n                            if ( !b.key_ops ) b.key_ops = ( b.kty !== 'oct' ) ? ( 'd' in b ) ? ku.filter(isPrvKeyUse) : ku.filter(isPubKeyUse) : ku.slice();\n                            args[1] = jwk2b(b);\n                        }\n                        break;\n                    case 'unwrapKey':\n                        ka = args[4], kx = args[5], ku = args[6];\n                        args[2] = c._key;\n                        break;\n                }\n\n                if ( m === 'generateKey' && ka.name === 'HMAC' && ka.hash ) {\n                    ka.length = ka.length || { 'SHA-1': 512, 'SHA-256': 512, 'SHA-384': 1024, 'SHA-512': 1024 }[ka.hash.name];\n                    return _subtle.importKey( 'raw', _crypto.getRandomValues( new Uint8Array( (ka.length+7)>>3 ) ), ka, kx, ku );\n                }\n\n                if ( isWebkit && m === 'generateKey' && ka.name === 'RSASSA-PKCS1-v1_5' && ( !ka.modulusLength || ka.modulusLength >= 2048 ) ) {\n                    a = alg(a), a.name = 'RSAES-PKCS1-v1_5', delete a.hash;\n                    return _subtle.generateKey( a, true, [ 'encrypt', 'decrypt' ] )\n                        .then( function ( k ) {\n                            return Promise.all([\n                                _subtle.exportKey( 'jwk', k.publicKey ),\n                                _subtle.exportKey( 'jwk', k.privateKey ),\n                            ]);\n                        })\n                        .then( function ( keys ) {\n                            keys[0].alg = keys[1].alg = jwkAlg(ka);\n                            keys[0].key_ops = ku.filter(isPubKeyUse), keys[1].key_ops = ku.filter(isPrvKeyUse);\n                            return Promise.all([\n                                _subtle.importKey( 'jwk', keys[0], ka, true, keys[0].key_ops ),\n                                _subtle.importKey( 'jwk', keys[1], ka, kx, keys[1].key_ops ),\n                            ]);\n                        })\n                        .then( function ( keys ) {\n                            return {\n                                publicKey: keys[0],\n                                privateKey: keys[1],\n                            };\n                        });\n                }\n\n                if ( ( isWebkit || ( isIE && ( ka.hash || {} ).name === 'SHA-1' ) )\n                        && m === 'importKey' && a === 'jwk' && ka.name === 'HMAC' && b.kty === 'oct' ) {\n                    return _subtle.importKey( 'raw', s2b( a2s(b.k) ), c, args[3], args[4] );\n                }\n\n                if ( isWebkit && m === 'importKey' && ( a === 'spki' || a === 'pkcs8' ) ) {\n                    return _subtle.importKey( 'jwk', pkcs2jwk(b), c, args[3], args[4] );\n                }\n\n                if ( isIE && m === 'unwrapKey' ) {\n                    return _subtle.decrypt( args[3], c, b )\n                        .then( function ( k ) {\n                            return _subtle.importKey( a, k, args[4], args[5], args[6] );\n                        });\n                }\n\n                var op;\n                try {\n                    op = _fn.apply( _subtle, args );\n                }\n                catch ( e ) {\n                    return Promise.reject(e);\n                }\n\n                if ( isIE ) {\n                    op = new Promise( function ( res, rej ) {\n                        op.onabort =\n                        op.onerror =    function ( e ) { rej(e)               };\n                        op.oncomplete = function ( r ) { res(r.target.result) };\n                    });\n                }\n\n                op = op.then( function ( k ) {\n                    if ( ka.name === 'HMAC' ) {\n                        if ( !ka.length ) ka.length = 8 * k.algorithm.length;\n                    }\n                    if ( ka.name.search('RSA') == 0 ) {\n                        if ( !ka.modulusLength ) ka.modulusLength = (k.publicKey || k).algorithm.modulusLength;\n                        if ( !ka.publicExponent ) ka.publicExponent = (k.publicKey || k).algorithm.publicExponent;\n                    }\n                    if ( k.publicKey && k.privateKey ) {\n                        k = {\n                            publicKey: new CryptoKey( k.publicKey, ka, kx, ku.filter(isPubKeyUse) ),\n                            privateKey: new CryptoKey( k.privateKey, ka, kx, ku.filter(isPrvKeyUse) ),\n                        };\n                    }\n                    else {\n                        k = new CryptoKey( k, ka, kx, ku );\n                    }\n                    return k;\n                });\n\n                return op;\n            }\n        });\n\n    [ 'exportKey', 'wrapKey' ]\n        .forEach( function ( m ) {\n            var _fn = _subtle[m];\n\n            _subtle[m] = function ( a, b, c ) {\n                var args = [].slice.call(arguments);\n\n                switch ( m ) {\n                    case 'exportKey':\n                        args[1] = b._key;\n                        break;\n                    case 'wrapKey':\n                        args[1] = b._key, args[2] = c._key;\n                        break;\n                }\n\n                if ( ( isWebkit || ( isIE && ( b.algorithm.hash || {} ).name === 'SHA-1' ) )\n                        && m === 'exportKey' && a === 'jwk' && b.algorithm.name === 'HMAC' ) {\n                    args[0] = 'raw';\n                }\n\n                if ( isWebkit && m === 'exportKey' && ( a === 'spki' || a === 'pkcs8' ) ) {\n                    args[0] = 'jwk';\n                }\n\n                if ( isIE && m === 'wrapKey' ) {\n                    return _subtle.exportKey( a, b )\n                        .then( function ( k ) {\n                            if ( a === 'jwk' ) k = s2b( unescape( encodeURIComponent( JSON.stringify( b2jwk(k) ) ) ) );\n                            return  _subtle.encrypt( args[3], c, k );\n                        });\n                }\n\n                var op;\n                try {\n                    op = _fn.apply( _subtle, args );\n                }\n                catch ( e ) {\n                    return Promise.reject(e);\n                }\n\n                if ( isIE ) {\n                    op = new Promise( function ( res, rej ) {\n                        op.onabort =\n                        op.onerror =    function ( e ) { rej(e)               };\n                        op.oncomplete = function ( r ) { res(r.target.result) };\n                    });\n                }\n\n                if ( m === 'exportKey' && a === 'jwk' ) {\n                    op = op.then( function ( k ) {\n                        if ( ( isWebkit || ( isIE && ( b.algorithm.hash || {} ).name === 'SHA-1' ) )\n                                && b.algorithm.name === 'HMAC') {\n                            return { 'kty': 'oct', 'alg': jwkAlg(b.algorithm), 'key_ops': b.usages.slice(), 'ext': true, 'k': s2a( b2s(k) ) };\n                        }\n                        k = b2jwk(k);\n                        if ( !k.alg ) k['alg'] = jwkAlg(b.algorithm);\n                        if ( !k.key_ops ) k['key_ops'] = ( b.type === 'public' ) ? b.usages.filter(isPubKeyUse) : ( b.type === 'private' ) ? b.usages.filter(isPrvKeyUse) : b.usages.slice();\n                        return k;\n                    });\n                }\n\n                if ( isWebkit && m === 'exportKey' && ( a === 'spki' || a === 'pkcs8' ) ) {\n                    op = op.then( function ( k ) {\n                        k = jwk2pkcs( b2jwk(k) );\n                        return k;\n                    });\n                }\n\n                return op;\n            }\n        });\n\n    [ 'encrypt', 'decrypt', 'sign', 'verify' ]\n        .forEach( function ( m ) {\n            var _fn = _subtle[m];\n\n            _subtle[m] = function ( a, b, c, d ) {\n                if ( isIE && ( !c.byteLength || ( d && !d.byteLength ) ) )\n                    throw new Error(\"Empty input is not allowed\");\n\n                var args = [].slice.call(arguments),\n                    ka = alg(a);\n\n                if ( isIE && ( m === 'sign' || m === 'verify') && ( a === 'RSASSA-PKCS1-v1_5' || a === 'HMAC' ) ) {\n                    args[0] = { name: a };\n                }\n\n                if ( isIE && b.algorithm.hash ) {\n                    args[0].hash = args[0].hash || b.algorithm.hash;\n                }\n\n                if ( isIE && m === 'decrypt' && ka.name === 'AES-GCM' ) {\n                    var tl = a.tagLength >> 3;\n                    args[2] = (c.buffer || c).slice( 0, c.byteLength - tl ),\n                    a.tag = (c.buffer || c).slice( c.byteLength - tl );\n                }\n\n                if ( isIE && ka.name === 'AES-GCM' && args[0].tagLength === undefined ) {\n                    args[0].tagLength = 128;\n                }\n\n                args[1] = b._key;\n\n                var op;\n                try {\n                    op = _fn.apply( _subtle, args );\n                }\n                catch ( e ) {\n                    return Promise.reject(e);\n                }\n\n                if ( isIE ) {\n                    op = new Promise( function ( res, rej ) {\n                        op.onabort =\n                        op.onerror = function ( e ) {\n                            rej(e);\n                        };\n\n                        op.oncomplete = function ( r ) {\n                            var r = r.target.result;\n\n                            if ( m === 'encrypt' && r instanceof AesGcmEncryptResult ) {\n                                var c = r.ciphertext, t = r.tag;\n                                r = new Uint8Array( c.byteLength + t.byteLength );\n                                r.set( new Uint8Array(c), 0 );\n                                r.set( new Uint8Array(t), c.byteLength );\n                                r = r.buffer;\n                            }\n\n                            res(r);\n                        };\n                    });\n                }\n\n                return op;\n            }\n        });\n\n    if ( isIE ) {\n        var _digest = _subtle.digest;\n\n        _subtle['digest'] = function ( a, b ) {\n            if ( !b.byteLength )\n                throw new Error(\"Empty input is not allowed\");\n\n            var op;\n            try {\n                op = _digest.call( _subtle, a, b );\n            }\n            catch ( e ) {\n                return Promise.reject(e);\n            }\n\n            op = new Promise( function ( res, rej ) {\n                op.onabort =\n                op.onerror =    function ( e ) { rej(e)               };\n                op.oncomplete = function ( r ) { res(r.target.result) };\n            });\n\n            return op;\n        };\n\n        global.crypto = Object.create( _crypto, {\n            getRandomValues: { value: function ( a ) { return _crypto.getRandomValues(a) } },\n            subtle:          { value: _subtle },\n        });\n\n        global.CryptoKey = CryptoKey;\n    }\n\n    if ( isWebkit ) {\n        _crypto.subtle = _subtle;\n\n        global.Crypto = _Crypto;\n        global.SubtleCrypto = _SubtleCrypto;\n        global.CryptoKey = CryptoKey;\n    }\n}));\n"]}