import React, { useEffect, useState } from 'react';
import { Spin, Alert } from 'antd';
import { history } from '@umijs/max';
import { keycloakService } from '@/services/keycloak';

const AuthCallback: React.FC = () => {
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        console.log('=== 开始处理Keycloak回调 ===');
        console.log('当前URL:', window.location.href);

        // 使用 keycloakService 处理回调
        const authenticated = await keycloakService.handleCallback();

        if (authenticated) {
          console.log('认证成功，准备重定向');

          // 获取保存的重定向路径
          const redirectPath = sessionStorage.getItem('auth_redirect_path') || '/Console/projects';
          sessionStorage.removeItem('auth_redirect_path');

          console.log('重定向到:', redirectPath);

          // 使用 history.replace 进行重定向
          history.replace(redirectPath);
        } else {
          console.log('认证失败');
          setError('认证失败，请重新登录');
          setTimeout(() => {
            history.replace('/user/login');
          }, 2000);
        }
      } catch (error: any) {
        console.error('回调处理失败:', error);
        setError(`回调处理失败: ${error.message || '未知错误'}`);
        setTimeout(() => {
          history.replace('/user/login');
        }, 3000);
      }
    };

    handleCallback();
  }, []);

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      flexDirection: 'column',
      padding: '20px',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white'
    }}>
      {error ? (
        <Alert
          message="认证错误"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      ) : (
        <>
          <Spin size="large" style={{ color: 'white' }} />
          <div style={{
            marginTop: 24,
            fontSize: '18px',
            fontWeight: 500,
            textAlign: 'center'
          }}>
            登录成功！正在跳转...
          </div>
          <div style={{
            marginTop: 8,
            fontSize: '14px',
            opacity: 0.8,
            textAlign: 'center'
          }}>
            请稍候，即将进入系统
          </div>
        </>
      )}
    </div>
  );
};

export default AuthCallback;
