"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8606],{42288:function(Re,de,l){l.r(de),l.d(de,{default:function(){return k}});var C=l(15009),V=l.n(C),pe=l(99289),q=l.n(pe),ve=l(5574),Y=l.n(ve),i=l(67294),_=l(71471),be=l(40056),ee=l(4393),he=l(93967),H=l.n(he),ue=l(74443),me=l(53124),ye=l(98675),$e=l(25378),je={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1},ne=i.createContext({}),Ce=l(50344),Oe=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]]);return a};const fe=e=>(0,Ce.Z)(e).map(t=>Object.assign(Object.assign({},t==null?void 0:t.props),{key:t.key}));function xe(e,t,a){const n=i.useMemo(()=>t||fe(a),[t,a]);return i.useMemo(()=>n.map(h=>{var{span:f}=h,x=Oe(h,["span"]);return f==="filled"?Object.assign(Object.assign({},x),{filled:!0}):Object.assign(Object.assign({},x),{span:typeof f=="number"?f:(0,ue.m9)(e,f)})}),[n,e])}var Se=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]]);return a};function Ie(e,t){let a=[],n=[],o=!1,h=0;return e.filter(f=>f).forEach(f=>{const{filled:x}=f,B=Se(f,["filled"]);if(x){n.push(B),a.push(n),n=[],h=0;return}const O=t-h;h+=f.span||1,h>=t?(h>t?(o=!0,n.push(Object.assign(Object.assign({},B),{span:O}))):n.push(B),a.push(n),n=[],h=0):n.push(B)}),n.length>0&&a.push(n),a=a.map(f=>{const x=f.reduce((B,O)=>B+(O.span||1),0);if(x<t){const B=f[f.length-1];return B.span=t-(x-(B.span||1)),f}return f}),[a,o]}var ge=(e,t)=>{const[a,n]=(0,i.useMemo)(()=>Ie(t,e),[t,e]);return a},Ee=({children:e})=>e;function oe(e){return e!=null}var le=e=>{const{itemPrefixCls:t,component:a,span:n,className:o,style:h,labelStyle:f,contentStyle:x,bordered:B,label:O,content:y,colon:D,type:L,styles:u}=e,R=a,g=i.useContext(ne),{classNames:S}=g;return B?i.createElement(R,{className:H()({[`${t}-item-label`]:L==="label",[`${t}-item-content`]:L==="content",[`${S==null?void 0:S.label}`]:L==="label",[`${S==null?void 0:S.content}`]:L==="content"},o),style:h,colSpan:n},oe(O)&&i.createElement("span",{style:Object.assign(Object.assign({},f),u==null?void 0:u.label)},O),oe(y)&&i.createElement("span",{style:Object.assign(Object.assign({},f),u==null?void 0:u.content)},y)):i.createElement(R,{className:H()(`${t}-item`,o),style:h,colSpan:n},i.createElement("div",{className:`${t}-item-container`},(O||O===0)&&i.createElement("span",{className:H()(`${t}-item-label`,S==null?void 0:S.label,{[`${t}-item-no-colon`]:!D}),style:Object.assign(Object.assign({},f),u==null?void 0:u.label)},O),(y||y===0)&&i.createElement("span",{className:H()(`${t}-item-content`,S==null?void 0:S.content),style:Object.assign(Object.assign({},x),u==null?void 0:u.content)},y)))};function se(e,{colon:t,prefixCls:a,bordered:n},{component:o,type:h,showLabel:f,showContent:x,labelStyle:B,contentStyle:O,styles:y}){return e.map(({label:D,children:L,prefixCls:u=a,className:R,style:g,labelStyle:S,contentStyle:$,span:K=1,key:Q,styles:j},F)=>typeof o=="string"?i.createElement(le,{key:`${h}-${Q||F}`,className:R,style:g,styles:{label:Object.assign(Object.assign(Object.assign(Object.assign({},B),y==null?void 0:y.label),S),j==null?void 0:j.label),content:Object.assign(Object.assign(Object.assign(Object.assign({},O),y==null?void 0:y.content),$),j==null?void 0:j.content)},span:K,colon:t,component:o,itemPrefixCls:u,bordered:n,label:f?D:null,content:x?L:null,type:h}):[i.createElement(le,{key:`label-${Q||F}`,className:R,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},B),y==null?void 0:y.label),g),S),j==null?void 0:j.label),span:1,colon:t,component:o[0],itemPrefixCls:u,bordered:n,label:D,type:"label"}),i.createElement(le,{key:`content-${Q||F}`,className:R,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},O),y==null?void 0:y.content),g),$),j==null?void 0:j.content),span:K*2-1,component:o[1],itemPrefixCls:u,bordered:n,content:L,type:"content"})])}var ze=e=>{const t=i.useContext(ne),{prefixCls:a,vertical:n,row:o,index:h,bordered:f}=e;return n?i.createElement(i.Fragment,null,i.createElement("tr",{key:`label-${h}`,className:`${a}-row`},se(o,e,Object.assign({component:"th",type:"label",showLabel:!0},t))),i.createElement("tr",{key:`content-${h}`,className:`${a}-row`},se(o,e,Object.assign({component:"td",type:"content",showContent:!0},t)))):i.createElement("tr",{key:h,className:`${a}-row`},se(o,e,Object.assign({component:f?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},t)))},Z=l(11568),re=l(14747),Be=l(83559),s=l(83262);const r=e=>{const{componentCls:t,labelBg:a}=e;return{[`&${t}-bordered`]:{[`> ${t}-view`]:{border:`${(0,Z.bf)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"> table":{tableLayout:"auto"},[`${t}-row`]:{borderBottom:`${(0,Z.bf)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:first-child":{"> th:first-child, > td:first-child":{borderStartStartRadius:e.borderRadiusLG}},"&:last-child":{borderBottom:"none","> th:first-child, > td:first-child":{borderEndStartRadius:e.borderRadiusLG}},[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,Z.bf)(e.padding)} ${(0,Z.bf)(e.paddingLG)}`,borderInlineEnd:`${(0,Z.bf)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderInlineEnd:"none"}},[`> ${t}-item-label`]:{color:e.colorTextSecondary,backgroundColor:a,"&::after":{display:"none"}}}},[`&${t}-middle`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,Z.bf)(e.paddingSM)} ${(0,Z.bf)(e.paddingLG)}`}}},[`&${t}-small`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,Z.bf)(e.paddingXS)} ${(0,Z.bf)(e.padding)}`}}}}}},c=e=>{const{componentCls:t,extraColor:a,itemPaddingBottom:n,itemPaddingEnd:o,colonMarginRight:h,colonMarginLeft:f,titleMarginBottom:x}=e;return{[t]:Object.assign(Object.assign(Object.assign({},(0,re.Wf)(e)),r(e)),{"&-rtl":{direction:"rtl"},[`${t}-header`]:{display:"flex",alignItems:"center",marginBottom:x},[`${t}-title`]:Object.assign(Object.assign({},re.vS),{flex:"auto",color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),[`${t}-extra`]:{marginInlineStart:"auto",color:a,fontSize:e.fontSize},[`${t}-view`]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed",borderCollapse:"collapse"}},[`${t}-row`]:{"> th, > td":{paddingBottom:n,paddingInlineEnd:o},"> th:last-child, > td:last-child":{paddingInlineEnd:0},"&:last-child":{borderBottom:"none","> th, > td":{paddingBottom:0}}},[`${t}-item-label`]:{color:e.labelColor,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:`${(0,Z.bf)(f)} ${(0,Z.bf)(h)}`},[`&${t}-item-no-colon::after`]:{content:'""'}},[`${t}-item-no-label`]:{"&::after":{margin:0,content:'""'}},[`${t}-item-content`]:{display:"table-cell",flex:1,color:e.contentColor,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},[`${t}-item`]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",[`${t}-item-label`]:{display:"inline-flex",alignItems:"baseline"},[`${t}-item-content`]:{display:"inline-flex",alignItems:"baseline",minWidth:"1em"}}},"&-middle":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}},d=e=>({labelBg:e.colorFillAlter,labelColor:e.colorTextTertiary,titleColor:e.colorText,titleMarginBottom:e.fontSizeSM*e.lineHeightSM,itemPaddingBottom:e.padding,itemPaddingEnd:e.padding,colonMarginRight:e.marginXS,colonMarginLeft:e.marginXXS/2,contentColor:e.colorText,extraColor:e.colorText});var p=(0,Be.I$)("Descriptions",e=>{const t=(0,s.IX)(e,{});return c(t)},d),P=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]]);return a};const M=e=>{const{prefixCls:t,title:a,extra:n,column:o,colon:h=!0,bordered:f,layout:x,children:B,className:O,rootClassName:y,style:D,size:L,labelStyle:u,contentStyle:R,styles:g,items:S,classNames:$}=e,K=P(e,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","styles","items","classNames"]),{getPrefixCls:Q,direction:j,className:F,style:ae,classNames:U,styles:X}=(0,me.dj)("descriptions"),v=Q("descriptions",t),I=(0,$e.Z)(),A=i.useMemo(()=>{var ce;return typeof o=="number"?o:(ce=(0,ue.m9)(I,Object.assign(Object.assign({},je),o)))!==null&&ce!==void 0?ce:3},[I,o]),b=xe(I,S,B),N=(0,ye.Z)(L),De=ge(A,b),[ie,Ne,Ae]=p(v),He=i.useMemo(()=>({labelStyle:u,contentStyle:R,styles:{content:Object.assign(Object.assign({},X.content),g==null?void 0:g.content),label:Object.assign(Object.assign({},X.label),g==null?void 0:g.label)},classNames:{label:H()(U.label,$==null?void 0:$.label),content:H()(U.content,$==null?void 0:$.content)}}),[u,R,g,$,U,X]);return ie(i.createElement(ne.Provider,{value:He},i.createElement("div",Object.assign({className:H()(v,F,U.root,$==null?void 0:$.root,{[`${v}-${N}`]:N&&N!=="default",[`${v}-bordered`]:!!f,[`${v}-rtl`]:j==="rtl"},O,y,Ne,Ae),style:Object.assign(Object.assign(Object.assign(Object.assign({},ae),X.root),g==null?void 0:g.root),D)},K),(a||n)&&i.createElement("div",{className:H()(`${v}-header`,U.header,$==null?void 0:$.header),style:Object.assign(Object.assign({},X.header),g==null?void 0:g.header)},a&&i.createElement("div",{className:H()(`${v}-title`,U.title,$==null?void 0:$.title),style:Object.assign(Object.assign({},X.title),g==null?void 0:g.title)},a),n&&i.createElement("div",{className:H()(`${v}-extra`,U.extra,$==null?void 0:$.extra),style:Object.assign(Object.assign({},X.extra),g==null?void 0:g.extra)},n)),i.createElement("div",{className:`${v}-view`},i.createElement("table",null,i.createElement("tbody",null,De.map((ce,Le)=>i.createElement(ze,{key:Le,index:Le,colon:h,prefixCls:v,vertical:x==="vertical",bordered:f,row:ce}))))))))};M.Item=Ee;var E=M,W=l(78957),T=l(83622),z=l(68744),m=l(85893),J=_.Z.Title,G=_.Z.Text,w=function(){var t=(0,i.useState)("\u672A\u521D\u59CB\u5316"),a=Y()(t,2),n=a[0],o=a[1],h=(0,i.useState)(!1),f=Y()(h,2),x=f[0],B=f[1],O=(0,i.useState)(null),y=Y()(O,2),D=y[0],L=y[1],u=(0,i.useState)(null),R=Y()(u,2),g=R[0],S=R[1],$=(0,i.useState)(null),K=Y()($,2),Q=K[0],j=K[1],F=function(){var v=q()(V()().mark(function I(){var A,b,N;return V()().wrap(function(ie){for(;;)switch(ie.prev=ie.next){case 0:try{j(null),o("\u68C0\u67E5\u4E2D..."),A=z.e.isAuthenticated(),B(A),A?(o("\u5DF2\u8BA4\u8BC1"),b=z.e.getUserInfo(),L(b),N=z.e.getToken(),S(N?N.substring(0,50)+"...":null)):(o("\u672A\u8BA4\u8BC1"),L(null),S(null))}catch(Ne){j(Ne.message),o("\u9519\u8BEF")}case 1:case"end":return ie.stop()}},I)}));return function(){return v.apply(this,arguments)}}(),ae=function(){var v=q()(V()().mark(function I(){var A;return V()().wrap(function(N){for(;;)switch(N.prev=N.next){case 0:return N.prev=0,j(null),o("\u521D\u59CB\u5316\u4E2D..."),N.next=5,z.e.init();case 5:return A=N.sent,o(A?"\u521D\u59CB\u5316\u6210\u529F":"\u521D\u59CB\u5316\u5931\u8D25"),N.next=9,F();case 9:N.next=15;break;case 11:N.prev=11,N.t0=N.catch(0),j(N.t0.message),o("\u521D\u59CB\u5316\u5931\u8D25");case 15:case"end":return N.stop()}},I,null,[[0,11]])}));return function(){return v.apply(this,arguments)}}(),U=function(){var v=q()(V()().mark(function I(){return V()().wrap(function(b){for(;;)switch(b.prev=b.next){case 0:return b.prev=0,j(null),b.next=4,z.e.login("/test-keycloak");case 4:b.next=9;break;case 6:b.prev=6,b.t0=b.catch(0),j(b.t0.message);case 9:case"end":return b.stop()}},I,null,[[0,6]])}));return function(){return v.apply(this,arguments)}}(),X=function(){var v=q()(V()().mark(function I(){return V()().wrap(function(b){for(;;)switch(b.prev=b.next){case 0:return b.prev=0,j(null),b.next=4,z.e.logout();case 4:return b.next=6,F();case 6:b.next=11;break;case 8:b.prev=8,b.t0=b.catch(0),j(b.t0.message);case 11:case"end":return b.stop()}},I,null,[[0,8]])}));return function(){return v.apply(this,arguments)}}();return(0,i.useEffect)(function(){F()},[]),(0,m.jsxs)("div",{style:{padding:"24px",maxWidth:"800px",margin:"0 auto"},children:[(0,m.jsx)(J,{level:2,children:"Keycloak \u6D4B\u8BD5\u9875\u9762"}),Q&&(0,m.jsx)(be.Z,{message:"\u9519\u8BEF",description:Q,type:"error",showIcon:!0,style:{marginBottom:"16px"}}),(0,m.jsx)(ee.Z,{title:"Keycloak \u72B6\u6001",style:{marginBottom:"16px"},children:(0,m.jsxs)(E,{column:1,children:[(0,m.jsx)(E.Item,{label:"\u72B6\u6001",children:n}),(0,m.jsx)(E.Item,{label:"\u8BA4\u8BC1\u72B6\u6001",children:(0,m.jsx)(G,{type:x?"success":"danger",children:x?"\u5DF2\u8BA4\u8BC1":"\u672A\u8BA4\u8BC1"})}),D&&(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(E.Item,{label:"\u7528\u6237ID",children:D.id}),(0,m.jsx)(E.Item,{label:"\u7528\u6237\u540D",children:D.username}),(0,m.jsx)(E.Item,{label:"\u90AE\u7BB1",children:D.email}),(0,m.jsx)(E.Item,{label:"\u59D3\u540D",children:D.name})]}),g&&(0,m.jsx)(E.Item,{label:"\u8BBF\u95EE\u4EE4\u724C",children:g})]})}),(0,m.jsx)(ee.Z,{title:"\u64CD\u4F5C",children:(0,m.jsxs)(W.Z,{children:[(0,m.jsx)(T.ZP,{onClick:ae,children:"\u521D\u59CB\u5316 Keycloak"}),(0,m.jsx)(T.ZP,{type:"primary",onClick:U,disabled:x,children:"\u767B\u5F55"}),(0,m.jsx)(T.ZP,{onClick:X,disabled:!x,children:"\u767B\u51FA"}),(0,m.jsx)(T.ZP,{onClick:F,children:"\u5237\u65B0\u72B6\u6001"})]})}),(0,m.jsxs)(ee.Z,{title:"\u8C03\u8BD5\u4FE1\u606F",style:{marginTop:"16px"},children:[(0,m.jsxs)(G,{code:!0,children:["\u5F53\u524D URL: ",window.location.href]}),(0,m.jsx)("br",{}),(0,m.jsxs)(G,{code:!0,children:["Web Crypto API: ",typeof window.crypto!="undefined"?"\u53EF\u7528":"\u4E0D\u53EF\u7528"]}),(0,m.jsx)("br",{}),(0,m.jsxs)(G,{code:!0,children:["\u73AF\u5883: ","production"]})]})]})},k=w},40056:function(Re,de,l){l.d(de,{Z:function(){return Be}});var C=l(67294),V=l(19735),pe=l(17012),q=l(62208),ve=l(29950),Y=l(1558),i=l(93967),_=l.n(i),be=l(29372),ee=l(64217),he=l(42550),H=l(96159),ue=l(53124),me=l(11568),ye=l(14747),$e=l(83559);const te=(s,r,c,d,p)=>({background:s,border:`${(0,me.bf)(d.lineWidth)} ${d.lineType} ${r}`,[`${p}-icon`]:{color:c}}),je=s=>{const{componentCls:r,motionDurationSlow:c,marginXS:d,marginSM:p,fontSize:P,fontSizeLG:M,lineHeight:E,borderRadiusLG:W,motionEaseInOutCirc:T,withDescriptionIconSize:z,colorText:m,colorTextHeading:J,withDescriptionPadding:G,defaultPadding:w}=s;return{[r]:Object.assign(Object.assign({},(0,ye.Wf)(s)),{position:"relative",display:"flex",alignItems:"center",padding:w,wordWrap:"break-word",borderRadius:W,[`&${r}-rtl`]:{direction:"rtl"},[`${r}-content`]:{flex:1,minWidth:0},[`${r}-icon`]:{marginInlineEnd:d,lineHeight:0},"&-description":{display:"none",fontSize:P,lineHeight:E},"&-message":{color:J},[`&${r}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${c} ${T}, opacity ${c} ${T},
        padding-top ${c} ${T}, padding-bottom ${c} ${T},
        margin-bottom ${c} ${T}`},[`&${r}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${r}-with-description`]:{alignItems:"flex-start",padding:G,[`${r}-icon`]:{marginInlineEnd:p,fontSize:z,lineHeight:0},[`${r}-message`]:{display:"block",marginBottom:d,color:J,fontSize:M},[`${r}-description`]:{display:"block",color:m}},[`${r}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},Pe=s=>{const{componentCls:r,colorSuccess:c,colorSuccessBorder:d,colorSuccessBg:p,colorWarning:P,colorWarningBorder:M,colorWarningBg:E,colorError:W,colorErrorBorder:T,colorErrorBg:z,colorInfo:m,colorInfoBorder:J,colorInfoBg:G}=s;return{[r]:{"&-success":te(p,d,c,s,r),"&-info":te(G,J,m,s,r),"&-warning":te(E,M,P,s,r),"&-error":Object.assign(Object.assign({},te(z,T,W,s,r)),{[`${r}-description > pre`]:{margin:0,padding:0}})}}},ne=s=>{const{componentCls:r,iconCls:c,motionDurationMid:d,marginXS:p,fontSizeIcon:P,colorIcon:M,colorIconHover:E}=s;return{[r]:{"&-action":{marginInlineStart:p},[`${r}-close-icon`]:{marginInlineStart:p,padding:0,overflow:"hidden",fontSize:P,lineHeight:(0,me.bf)(P),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${c}-close`]:{color:M,transition:`color ${d}`,"&:hover":{color:E}}},"&-close-text":{color:M,transition:`color ${d}`,"&:hover":{color:E}}}}},Ce=s=>({withDescriptionIconSize:s.fontSizeHeading3,defaultPadding:`${s.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${s.paddingMD}px ${s.paddingContentHorizontalLG}px`});var Oe=(0,$e.I$)("Alert",s=>[je(s),Pe(s),ne(s)],Ce),fe=function(s,r){var c={};for(var d in s)Object.prototype.hasOwnProperty.call(s,d)&&r.indexOf(d)<0&&(c[d]=s[d]);if(s!=null&&typeof Object.getOwnPropertySymbols=="function")for(var p=0,d=Object.getOwnPropertySymbols(s);p<d.length;p++)r.indexOf(d[p])<0&&Object.prototype.propertyIsEnumerable.call(s,d[p])&&(c[d[p]]=s[d[p]]);return c};const xe={success:V.Z,info:Y.Z,error:pe.Z,warning:ve.Z},Se=s=>{const{icon:r,prefixCls:c,type:d}=s,p=xe[d]||null;return r?(0,H.wm)(r,C.createElement("span",{className:`${c}-icon`},r),()=>({className:_()(`${c}-icon`,r.props.className)})):C.createElement(p,{className:`${c}-icon`})},Ie=s=>{const{isClosable:r,prefixCls:c,closeIcon:d,handleClose:p,ariaProps:P}=s,M=d===!0||d===void 0?C.createElement(q.Z,null):d;return r?C.createElement("button",Object.assign({type:"button",onClick:p,className:`${c}-close-icon`,tabIndex:0},P),M):null};var ge=C.forwardRef((s,r)=>{const{description:c,prefixCls:d,message:p,banner:P,className:M,rootClassName:E,style:W,onMouseEnter:T,onMouseLeave:z,onClick:m,afterClose:J,showIcon:G,closable:w,closeText:k,closeIcon:e,action:t,id:a}=s,n=fe(s,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[o,h]=C.useState(!1),f=C.useRef(null);C.useImperativeHandle(r,()=>({nativeElement:f.current}));const{getPrefixCls:x,direction:B,closable:O,closeIcon:y,className:D,style:L}=(0,ue.dj)("alert"),u=x("alert",d),[R,g,S]=Oe(u),$=v=>{var I;h(!0),(I=s.onClose)===null||I===void 0||I.call(s,v)},K=C.useMemo(()=>s.type!==void 0?s.type:P?"warning":"info",[s.type,P]),Q=C.useMemo(()=>typeof w=="object"&&w.closeIcon||k?!0:typeof w=="boolean"?w:e!==!1&&e!==null&&e!==void 0?!0:!!O,[k,e,w,O]),j=P&&G===void 0?!0:G,F=_()(u,`${u}-${K}`,{[`${u}-with-description`]:!!c,[`${u}-no-icon`]:!j,[`${u}-banner`]:!!P,[`${u}-rtl`]:B==="rtl"},D,M,E,S,g),ae=(0,ee.Z)(n,{aria:!0,data:!0}),U=C.useMemo(()=>typeof w=="object"&&w.closeIcon?w.closeIcon:k||(e!==void 0?e:typeof O=="object"&&O.closeIcon?O.closeIcon:y),[e,w,k,y]),X=C.useMemo(()=>{const v=w!=null?w:O;if(typeof v=="object"){const{closeIcon:I}=v;return fe(v,["closeIcon"])}return{}},[w,O]);return R(C.createElement(be.ZP,{visible:!o,motionName:`${u}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:v=>({maxHeight:v.offsetHeight}),onLeaveEnd:J},({className:v,style:I},A)=>C.createElement("div",Object.assign({id:a,ref:(0,he.sQ)(f,A),"data-show":!o,className:_()(F,v),style:Object.assign(Object.assign(Object.assign({},L),W),I),onMouseEnter:T,onMouseLeave:z,onClick:m,role:"alert"},ae),j?C.createElement(Se,{description:c,icon:s.icon,prefixCls:u,type:K}):null,C.createElement("div",{className:`${u}-content`},p?C.createElement("div",{className:`${u}-message`},p):null,c?C.createElement("div",{className:`${u}-description`},c):null),t?C.createElement("div",{className:`${u}-action`},t):null,C.createElement(Ie,{isClosable:Q,prefixCls:u,closeIcon:U,handleClose:$,ariaProps:X}))))}),we=l(15671),Ee=l(43144),oe=l(61120),Te=l(78814),le=l(82963);function se(s,r,c){return r=(0,oe.Z)(r),(0,le.Z)(s,(0,Te.Z)()?Reflect.construct(r,c||[],(0,oe.Z)(s).constructor):r.apply(s,c))}var Me=l(60136),Z=function(s){function r(){var c;return(0,we.Z)(this,r),c=se(this,r,arguments),c.state={error:void 0,info:{componentStack:""}},c}return(0,Me.Z)(r,s),(0,Ee.Z)(r,[{key:"componentDidCatch",value:function(d,p){this.setState({error:d,info:p})}},{key:"render",value:function(){const{message:d,description:p,id:P,children:M}=this.props,{error:E,info:W}=this.state,T=(W==null?void 0:W.componentStack)||null,z=typeof d=="undefined"?(E||"").toString():d,m=typeof p=="undefined"?T:p;return E?C.createElement(ge,{id:P,type:"error",message:z,description:C.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},m)}):M}}])}(C.Component);const re=ge;re.ErrorBoundary=Z;var Be=re}}]);
