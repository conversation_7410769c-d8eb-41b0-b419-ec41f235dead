"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7647],{20926:function(re,P,o){o.r(P);var l=o(15009),M=o.n(l),N=o(99289),L=o.n(N),W=o(5574),F=o.n(W),x=o(67294),D=o(40056),z=o(74330),S=o(84226),Z=o(68744),p=o(85893),w=function(){var U=(0,x.useState)(null),B=F()(U,2),C=B[0],A=B[1];return(0,x.useEffect)(function(){var K=function(){var G=L()(M()().mark(function T(){var R,I;return M()().wrap(function(c){for(;;)switch(c.prev=c.next){case 0:return c.prev=0,console.log("=== \u5F00\u59CB\u5904\u7406Keycloak\u56DE\u8C03 ==="),console.log("\u5F53\u524DURL:",window.location.href),c.next=5,Z.e.handleCallback();case 5:R=c.sent,R?(console.log("\u8BA4\u8BC1\u6210\u529F\uFF0C\u51C6\u5907\u91CD\u5B9A\u5411"),I=sessionStorage.getItem("auth_redirect_path")||"/Console/projects",sessionStorage.removeItem("auth_redirect_path"),console.log("\u91CD\u5B9A\u5411\u5230:",I),S.history.replace(I)):(console.log("\u8BA4\u8BC1\u5931\u8D25"),A("\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"),setTimeout(function(){S.history.replace("/user/login")},2e3)),c.next=14;break;case 9:c.prev=9,c.t0=c.catch(0),console.error("\u56DE\u8C03\u5904\u7406\u5931\u8D25:",c.t0),A("\u56DE\u8C03\u5904\u7406\u5931\u8D25: ".concat(c.t0.message||"\u672A\u77E5\u9519\u8BEF")),setTimeout(function(){S.history.replace("/user/login")},3e3);case 14:case"end":return c.stop()}},T,null,[[0,9]])}));return function(){return G.apply(this,arguments)}}();K()},[]),(0,p.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",flexDirection:"column",padding:"20px",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",color:"white"},children:C?(0,p.jsx)(D.Z,{message:"\u8BA4\u8BC1\u9519\u8BEF",description:C,type:"error",showIcon:!0,style:{marginBottom:16}}):(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(z.Z,{size:"large",style:{color:"white"}}),(0,p.jsx)("div",{style:{marginTop:24,fontSize:"18px",fontWeight:500,textAlign:"center"},children:"\u767B\u5F55\u6210\u529F\uFF01\u6B63\u5728\u8DF3\u8F6C..."}),(0,p.jsx)("div",{style:{marginTop:8,fontSize:"14px",opacity:.8,textAlign:"center"},children:"\u8BF7\u7A0D\u5019\uFF0C\u5373\u5C06\u8FDB\u5165\u7CFB\u7EDF"})]})})};P.default=w},40056:function(re,P,o){o.d(P,{Z:function(){return fe}});var l=o(67294),M=o(19735),N=o(17012),L=o(62208),W=o(29950),F=o(1558),x=o(93967),D=o.n(x),z=o(29372),S=o(64217),Z=o(42550),p=o(96159),w=o(53124),H=o(11568),U=o(14747),B=o(83559);const C=(e,n,t,r,s)=>({background:e,border:`${(0,H.bf)(r.lineWidth)} ${r.lineType} ${n}`,[`${s}-icon`]:{color:t}}),A=e=>{const{componentCls:n,motionDurationSlow:t,marginXS:r,marginSM:s,fontSize:a,fontSizeLG:u,lineHeight:m,borderRadiusLG:g,motionEaseInOutCirc:f,withDescriptionIconSize:h,colorText:y,colorTextHeading:_,withDescriptionPadding:$,defaultPadding:i}=e;return{[n]:Object.assign(Object.assign({},(0,U.Wf)(e)),{position:"relative",display:"flex",alignItems:"center",padding:i,wordWrap:"break-word",borderRadius:g,[`&${n}-rtl`]:{direction:"rtl"},[`${n}-content`]:{flex:1,minWidth:0},[`${n}-icon`]:{marginInlineEnd:r,lineHeight:0},"&-description":{display:"none",fontSize:a,lineHeight:m},"&-message":{color:_},[`&${n}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${t} ${f}, opacity ${t} ${f},
        padding-top ${t} ${f}, padding-bottom ${t} ${f},
        margin-bottom ${t} ${f}`},[`&${n}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${n}-with-description`]:{alignItems:"flex-start",padding:$,[`${n}-icon`]:{marginInlineEnd:s,fontSize:h,lineHeight:0},[`${n}-message`]:{display:"block",marginBottom:r,color:_,fontSize:u},[`${n}-description`]:{display:"block",color:y}},[`${n}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},K=e=>{const{componentCls:n,colorSuccess:t,colorSuccessBorder:r,colorSuccessBg:s,colorWarning:a,colorWarningBorder:u,colorWarningBg:m,colorError:g,colorErrorBorder:f,colorErrorBg:h,colorInfo:y,colorInfoBorder:_,colorInfoBg:$}=e;return{[n]:{"&-success":C(s,r,t,e,n),"&-info":C($,_,y,e,n),"&-warning":C(m,u,a,e,n),"&-error":Object.assign(Object.assign({},C(h,f,g,e,n)),{[`${n}-description > pre`]:{margin:0,padding:0}})}}},G=e=>{const{componentCls:n,iconCls:t,motionDurationMid:r,marginXS:s,fontSizeIcon:a,colorIcon:u,colorIconHover:m}=e;return{[n]:{"&-action":{marginInlineStart:s},[`${n}-close-icon`]:{marginInlineStart:s,padding:0,overflow:"hidden",fontSize:a,lineHeight:(0,H.bf)(a),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${t}-close`]:{color:u,transition:`color ${r}`,"&:hover":{color:m}}},"&-close-text":{color:u,transition:`color ${r}`,"&:hover":{color:m}}}}},T=e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`});var R=(0,B.I$)("Alert",e=>[A(e),K(e),G(e)],T),I=function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)n.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(t[r[s]]=e[r[s]]);return t};const V={success:M.Z,info:F.Z,error:N.Z,warning:W.Z},c=e=>{const{icon:n,prefixCls:t,type:r}=e,s=V[r]||null;return n?(0,p.wm)(n,l.createElement("span",{className:`${t}-icon`},n),()=>({className:D()(`${t}-icon`,n.props.className)})):l.createElement(s,{className:`${t}-icon`})},se=e=>{const{isClosable:n,prefixCls:t,closeIcon:r,handleClose:s,ariaProps:a}=e,u=r===!0||r===void 0?l.createElement(L.Z,null):r;return n?l.createElement("button",Object.assign({type:"button",onClick:s,className:`${t}-close-icon`,tabIndex:0},a),u):null};var X=l.forwardRef((e,n)=>{const{description:t,prefixCls:r,message:s,banner:a,className:u,rootClassName:m,style:g,onMouseEnter:f,onMouseLeave:h,onClick:y,afterClose:_,showIcon:$,closable:i,closeText:O,closeIcon:E,action:Y,id:pe}=e,ge=I(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[k,ve]=l.useState(!1),q=l.useRef(null);l.useImperativeHandle(n,()=>({nativeElement:q.current}));const{getPrefixCls:Ce,direction:he,closable:b,closeIcon:ee,className:ye,style:Ee}=(0,w.dj)("alert"),d=Ce("alert",r),[be,Ie,_e]=R(d),$e=v=>{var j;ve(!0),(j=e.onClose)===null||j===void 0||j.call(e,v)},ne=l.useMemo(()=>e.type!==void 0?e.type:a?"warning":"info",[e.type,a]),je=l.useMemo(()=>typeof i=="object"&&i.closeIcon||O?!0:typeof i=="boolean"?i:E!==!1&&E!==null&&E!==void 0?!0:!!b,[O,E,i,b]),oe=a&&$===void 0?!0:$,De=D()(d,`${d}-${ne}`,{[`${d}-with-description`]:!!t,[`${d}-no-icon`]:!oe,[`${d}-banner`]:!!a,[`${d}-rtl`]:he==="rtl"},ye,u,m,_e,Ie),Se=(0,S.Z)(ge,{aria:!0,data:!0}),Oe=l.useMemo(()=>typeof i=="object"&&i.closeIcon?i.closeIcon:O||(E!==void 0?E:typeof b=="object"&&b.closeIcon?b.closeIcon:ee),[E,i,O,ee]),Pe=l.useMemo(()=>{const v=i!=null?i:b;if(typeof v=="object"){const{closeIcon:j}=v;return I(v,["closeIcon"])}return{}},[i,b]);return be(l.createElement(z.ZP,{visible:!k,motionName:`${d}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:v=>({maxHeight:v.offsetHeight}),onLeaveEnd:_},({className:v,style:j},te)=>l.createElement("div",Object.assign({id:pe,ref:(0,Z.sQ)(q,te),"data-show":!k,className:D()(De,v),style:Object.assign(Object.assign(Object.assign({},Ee),g),j),onMouseEnter:f,onMouseLeave:h,onClick:y,role:"alert"},Se),oe?l.createElement(c,{description:t,icon:e.icon,prefixCls:d,type:ne}):null,l.createElement("div",{className:`${d}-content`},s?l.createElement("div",{className:`${d}-message`},s):null,t?l.createElement("div",{className:`${d}-description`},t):null),Y?l.createElement("div",{className:`${d}-action`},Y):null,l.createElement(se,{isClosable:je,prefixCls:d,closeIcon:Oe,handleClose:$e,ariaProps:Pe}))))}),le=o(15671),ae=o(43144),Q=o(61120),ie=o(78814),ce=o(82963);function de(e,n,t){return n=(0,Q.Z)(n),(0,ce.Z)(e,(0,ie.Z)()?Reflect.construct(n,t||[],(0,Q.Z)(e).constructor):n.apply(e,t))}var ue=o(60136),me=function(e){function n(){var t;return(0,le.Z)(this,n),t=de(this,n,arguments),t.state={error:void 0,info:{componentStack:""}},t}return(0,ue.Z)(n,e),(0,ae.Z)(n,[{key:"componentDidCatch",value:function(r,s){this.setState({error:r,info:s})}},{key:"render",value:function(){const{message:r,description:s,id:a,children:u}=this.props,{error:m,info:g}=this.state,f=(g==null?void 0:g.componentStack)||null,h=typeof r=="undefined"?(m||"").toString():r,y=typeof s=="undefined"?f:s;return m?l.createElement(X,{id:a,type:"error",message:h,description:l.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},y)}):u}}])}(l.Component);const J=X;J.ErrorBoundary=me;var fe=J}}]);
