{"name": "webcrypto-shim", "main": "webcrypto-shim.js", "version": "0.1.7", "description": "Web Cryptography API shim for legacy browsers", "license": "MIT", "author": "<PERSON><PERSON> S <PERSON> <vy<PERSON><PERSON>@gmail.com>", "homepage": "https://github.com/vibornoff/webcrypto-shim#readme", "repository": {"type": "git", "url": "git://github.com/vibornoff/webcrypto-shim.git"}, "bugs": {"url": "https://github.com/vibornoff/webcrypto-shim/issues"}, "keywords": ["webcrypto", "shim", "crypto", "mscrypto", "webkitcrypto", "mozcrypto", "subtle", "webkitsubtle", "sha", "hmac", "pbkdf", "aes", "cbc", "gcm", "rsa", "oaep", "pkcs1"], "devDependencies": {"gulp": "latest", "gulp-concat": "latest", "gulp-sourcemaps": "latest", "gulp-uglify": "latest"}, "scripts": {"prepublish": "gulp"}}