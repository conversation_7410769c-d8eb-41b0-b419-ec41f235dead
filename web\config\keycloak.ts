import { createKeycloakInstance } from '@/utils/keycloak-wrapper';

// 根据环境获取基础URL
const getBaseUrl = () => {
    // 如果是生产环境，使用服务器地址
    if (process.env.NODE_ENV === 'production') {
        return 'http://*************:9083';
    }
    // 开发环境使用本地地址
    return window.location.origin;
};

const keycloakConfig = {
    // 根据环境选择 Keycloak 服务器地址
    url: process.env.NODE_ENV === 'production'
        ? 'http://*************:8080'  // 生产环境 Keycloak 地址
        : 'http://localhost:8080',     // 开发环境 Keycloak 地址
    realm: 'dev_xh_key',
    clientId: 'sulei_01'
};

let keycloakInstance: any = null;

const getKeycloak = () => {
    if (!keycloakInstance) {
        console.log('Creating Keycloak instance with crypto workaround...');
        keycloakInstance = createKeycloakInstance(keycloakConfig);
    }
    return keycloakInstance;
};

export default getKeycloak();