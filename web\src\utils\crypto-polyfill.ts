// Web Crypto API polyfill for environments where it's not available
// This is needed for Keycloak in some browser environments

declare global {
  interface Window {
    crypto: Crypto;
  }
}

// Check if Web Crypto API is available
if (typeof window !== 'undefined' && !window.crypto) {
  console.warn('Web Crypto API not available, using polyfill');
  
  // Simple polyfill for getRandomValues
  const getRandomValues = (array: any) => {
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 256);
    }
    return array;
  };

  // Create a minimal crypto object
  (window as any).crypto = {
    getRandomValues,
    subtle: {
      // Minimal implementation - in production you'd want a proper polyfill
      digest: () => Promise.reject(new Error('SubtleCrypto not implemented in polyfill')),
      encrypt: () => Promise.reject(new Error('SubtleCrypto not implemented in polyfill')),
      decrypt: () => Promise.reject(new Error('SubtleCrypto not implemented in polyfill')),
      sign: () => Promise.reject(new Error('SubtleCrypto not implemented in polyfill')),
      verify: () => Promise.reject(new Error('SubtleCrypto not implemented in polyfill')),
      generateKey: () => Promise.reject(new Error('SubtleCrypto not implemented in polyfill')),
      deriveKey: () => Promise.reject(new Error('SubtleCrypto not implemented in polyfill')),
      deriveBits: () => Promise.reject(new Error('SubtleCrypto not implemented in polyfill')),
      importKey: () => Promise.reject(new Error('SubtleCrypto not implemented in polyfill')),
      exportKey: () => Promise.reject(new Error('SubtleCrypto not implemented in polyfill')),
      wrapKey: () => Promise.reject(new Error('SubtleCrypto not implemented in polyfill')),
      unwrapKey: () => Promise.reject(new Error('SubtleCrypto not implemented in polyfill')),
    }
  };
}

export {};
