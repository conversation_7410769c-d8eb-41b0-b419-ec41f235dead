/*! WebCrypto API shim v0.1.7, (c) 2015 <PERSON><PERSON> <v<PERSON><PERSON><PERSON>@gmail.com>, opensource.org/licenses/MIT */
!function(e,t){"function"==typeof define&&define.amd?define([],function(){return t(e)}):"object"==typeof module&&module.exports?module.exports=t(e):t(e)}("undefined"!=typeof self?self:this,function(e){"use strict";if("function"!=typeof Promise)throw"Promise support required";var l,t,r,n,y,f,s,p,a,h=e.crypto||e.msCrypto;function g(e){return btoa(e).replace(/\=+$/,"").replace(/\+/g,"-").replace(/\//g,"_")}function A(e){return e=(e+="===").slice(0,-e.length%4),atob(e.replace(/-/g,"+").replace(/_/g,"/"))}function m(e){for(var t=new Uint8Array(e.length),r=0;r<e.length;r++)t[r]=e.charCodeAt(r);return t}function b(e){return e instanceof ArrayBuffer&&(e=new Uint8Array(e)),String.fromCharCode.apply(String,e)}function w(e){var t={name:(e.name||e||"").toUpperCase().replace("V","v")};switch(t.name){case"SHA-1":case"SHA-256":case"SHA-384":case"SHA-512":break;case"AES-CBC":case"AES-GCM":case"AES-KW":e.length&&(t.length=e.length);break;case"HMAC":e.hash&&(t.hash=w(e.hash)),e.length&&(t.length=e.length);break;case"RSAES-PKCS1-v1_5":e.publicExponent&&(t.publicExponent=new Uint8Array(e.publicExponent)),e.modulusLength&&(t.modulusLength=e.modulusLength);break;case"RSASSA-PKCS1-v1_5":case"RSA-OAEP":e.hash&&(t.hash=w(e.hash)),e.publicExponent&&(t.publicExponent=new Uint8Array(e.publicExponent)),e.modulusLength&&(t.modulusLength=e.modulusLength);break;default:throw new SyntaxError("Bad algorithm name")}return t}function S(e){return{HMAC:{"SHA-1":"HS1","SHA-256":"HS256","SHA-384":"HS384","SHA-512":"HS512"},"RSASSA-PKCS1-v1_5":{"SHA-1":"RS1","SHA-256":"RS256","SHA-384":"RS384","SHA-512":"RS512"},"RSAES-PKCS1-v1_5":{"":"RSA1_5"},"RSA-OAEP":{"SHA-1":"RSA-OAEP","SHA-256":"RSA-OAEP-256"},"AES-KW":{128:"A128KW",192:"A192KW",256:"A256KW"},"AES-GCM":{128:"A128GCM",192:"A192GCM",256:"A256GCM"},"AES-CBC":{128:"A128CBC",192:"A192CBC",256:"A256CBC"}}[e.name][(e.hash||{}).name||e.length||""]}function d(t){(t instanceof ArrayBuffer||t instanceof Uint8Array)&&(t=JSON.parse(decodeURIComponent(escape(b(t)))));var r={kty:t.kty,alg:t.alg,ext:t.ext||t.extractable};switch(r.kty){case"oct":r.k=t.k;case"RSA":["n","e","d","p","q","dp","dq","qi","oth"].forEach(function(e){e in t&&(r[e]=t[e])});break;default:throw new TypeError("Unsupported key type")}return r}function k(e,t){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(t=t||{pos:0,end:e.length}).end-t.pos<2||t.end>e.length)throw new RangeError("Malformed DER");var r,n=e[t.pos++],a=e[t.pos++];if(128<=a){if(a&=127,t.end-t.pos<a)throw new RangeError("Malformed DER");for(var o=0;a--;)o<<=8,o|=e[t.pos++];a=o}if(t.end-t.pos<a)throw new RangeError("Malformed DER");switch(n){case 2:r=e.subarray(t.pos,t.pos+=a);break;case 3:if(e[t.pos++])throw new Error("Unsupported bit string");a--;case 4:r=new Uint8Array(e.subarray(t.pos,t.pos+=a)).buffer;break;case 5:r=null;break;case 6:var i=btoa(b(e.subarray(t.pos,t.pos+=a)));if(!(i in s))throw new Error("Unsupported OBJECT ID "+i);r=s[i];break;case 48:r=[];for(var u=t.pos+a;t.pos<u;)r.push(k(e,t));break;default:throw new Error("Unsupported DER tag 0x"+n.toString(16))}return r}function c(e,t){var r=0,n=0,a=(t=t||[]).length+2;if(t.push(0,0),e instanceof Uint8Array){r=2,n=e.length;for(var o=0;o<n;o++)t.push(e[o])}else if(e instanceof ArrayBuffer){r=4,n=e.byteLength,e=new Uint8Array(e);for(o=0;o<n;o++)t.push(e[o])}else if(null===e)r=5,n=0;else if("string"==typeof e&&e in p){var i=m(atob(p[e])),r=6;n=i.length;for(o=0;o<n;o++)t.push(i[o])}else if(e instanceof Array){for(o=0;o<e.length;o++)c(e[o],t);r=48,n=t.length-a}else{if(!("object"==typeof e&&3===e.tag&&e.value instanceof ArrayBuffer))throw new Error("Unsupported DER value "+e);r=3,n=(e=new Uint8Array(e.value)).byteLength,t.push(0);for(o=0;o<n;o++)t.push(e[o]);n++}if(128<=n){var u=n,n=4;for(t.splice(a,0,u>>24&255,u>>16&255,u>>8&255,255&u);1<n&&!(u>>24);)u<<=8,n--;n<4&&t.splice(a,4-n),n|=128}return t.splice(a-2,2,r,n),t}function v(e,t,r,n){Object.defineProperties(this,{_key:{value:e},type:{value:e.type,enumerable:!0},extractable:{value:void 0===r?e.extractable:r,enumerable:!0},algorithm:{value:void 0===t?e.algorithm:t,enumerable:!0},usages:{value:void 0===n?e.usages:n,enumerable:!0}})}function K(e){return"verify"===e||"encrypt"===e||"wrapKey"===e}function E(e){return"sign"===e||"decrypt"===e||"unwrapKey"===e}!h||(l=h.subtle||h.webkitSubtle)&&(t=e.Crypto||h.constructor||Object,r=e.SubtleCrypto||l.constructor||Object,e.CryptoKey||e.Key||Object,n=-1<e.navigator.userAgent.indexOf("Edge/"),y=!!e.msCrypto&&!n,f=!h.subtle&&!!h.webkitSubtle,(y||f)&&(s={KoZIhvcNAQEB:"1.2.840.113549.1.1.1"},p={"1.2.840.113549.1.1.1":"KoZIhvcNAQEB"},["generateKey","importKey","unwrapKey"].forEach(function(p){var c=l[p];l[p]=function(t,e,r){var n,a,o,i,u,s=[].slice.call(arguments);switch(p){case"generateKey":n=w(t),a=e,o=r;break;case"importKey":n=w(r),a=s[3],o=s[4],"jwk"===t&&((e=d(e)).alg||(e.alg=S(n)),e.key_ops||(e.key_ops="oct"!==e.kty?"d"in e?o.filter(E):o.filter(K):o.slice()),s[1]=(i=d(e),y&&(i.extractable=i.ext,delete i.ext),m(unescape(encodeURIComponent(JSON.stringify(i)))).buffer));break;case"unwrapKey":n=s[4],a=s[5],o=s[6],s[2]=r._key}if("generateKey"===p&&"HMAC"===n.name&&n.hash)return n.length=n.length||{"SHA-1":512,"SHA-256":512,"SHA-384":1024,"SHA-512":1024}[n.hash.name],l.importKey("raw",h.getRandomValues(new Uint8Array(n.length+7>>3)),n,a,o);if(f&&"generateKey"===p&&"RSASSA-PKCS1-v1_5"===n.name&&(!n.modulusLength||2048<=n.modulusLength))return(t=w(t)).name="RSAES-PKCS1-v1_5",delete t.hash,l.generateKey(t,!0,["encrypt","decrypt"]).then(function(e){return Promise.all([l.exportKey("jwk",e.publicKey),l.exportKey("jwk",e.privateKey)])}).then(function(e){return e[0].alg=e[1].alg=S(n),e[0].key_ops=o.filter(K),e[1].key_ops=o.filter(E),Promise.all([l.importKey("jwk",e[0],n,!0,e[0].key_ops),l.importKey("jwk",e[1],n,a,e[1].key_ops)])}).then(function(e){return{publicKey:e[0],privateKey:e[1]}});if((f||y&&"SHA-1"===(n.hash||{}).name)&&"importKey"===p&&"jwk"===t&&"HMAC"===n.name&&"oct"===e.kty)return l.importKey("raw",m(A(e.k)),r,s[3],s[4]);if(f&&"importKey"===p&&("spki"===t||"pkcs8"===t))return l.importKey("jwk",function(e){var t=k(e),r=!1;2<t.length&&(r=!0,t.shift());var n={ext:!0};if("1.2.840.113549.1.1.1"!==t[0][0])throw new TypeError("Unsupported key type");var a=["n","e","d","p","q","dp","dq","qi"],o=k(t[1]);r&&o.shift();for(var i=0;i<o.length;i++)o[i][0]||(o[i]=o[i].subarray(1)),n[a[i]]=g(b(o[i]));return n.kty="RSA",n}(e),r,s[3],s[4]);if(y&&"unwrapKey"===p)return l.decrypt(s[3],r,e).then(function(e){return l.importKey(t,e,s[4],s[5],s[6])});try{u=c.apply(l,s)}catch(e){return Promise.reject(e)}return y&&(u=new Promise(function(t,r){u.onabort=u.onerror=function(e){r(e)},u.oncomplete=function(e){t(e.target.result)}})),u=u.then(function(e){return"HMAC"===n.name&&(n.length||(n.length=8*e.algorithm.length)),0==n.name.search("RSA")&&(n.modulusLength||(n.modulusLength=(e.publicKey||e).algorithm.modulusLength),n.publicExponent||(n.publicExponent=(e.publicKey||e).algorithm.publicExponent)),e=e.publicKey&&e.privateKey?{publicKey:new v(e.publicKey,n,a,o.filter(K)),privateKey:new v(e.privateKey,n,a,o.filter(E))}:new v(e,n,a,o)})}}),["exportKey","wrapKey"].forEach(function(o){var i=l[o];l[o]=function(t,r,n){var e,a=[].slice.call(arguments);switch(o){case"exportKey":a[1]=r._key;break;case"wrapKey":a[1]=r._key,a[2]=n._key}if((f||y&&"SHA-1"===(r.algorithm.hash||{}).name)&&"exportKey"===o&&"jwk"===t&&"HMAC"===r.algorithm.name&&(a[0]="raw"),!f||"exportKey"!==o||"spki"!==t&&"pkcs8"!==t||(a[0]="jwk"),y&&"wrapKey"===o)return l.exportKey(t,r).then(function(e){return"jwk"===t&&(e=m(unescape(encodeURIComponent(JSON.stringify(d(e)))))),l.encrypt(a[3],n,e)});try{e=i.apply(l,a)}catch(e){return Promise.reject(e)}return y&&(e=new Promise(function(t,r){e.onabort=e.onerror=function(e){r(e)},e.oncomplete=function(e){t(e.target.result)}})),"exportKey"===o&&"jwk"===t&&(e=e.then(function(e){return(f||y&&"SHA-1"===(r.algorithm.hash||{}).name)&&"HMAC"===r.algorithm.name?{kty:"oct",alg:S(r.algorithm),key_ops:r.usages.slice(),ext:!0,k:g(b(e))}:((e=d(e)).alg||(e.alg=S(r.algorithm)),e.key_ops||(e.key_ops="public"===r.type?r.usages.filter(K):"private"===r.type?r.usages.filter(E):r.usages.slice()),e)})),!f||"exportKey"!==o||"spki"!==t&&"pkcs8"!==t||(e=e.then(function(e){return e=function(e){var t=[["",null]],r=!1;if("RSA"!==e.kty)throw new TypeError("Unsupported key type");for(var n=["n","e","d","p","q","dp","dq","qi"],a=[],o=0;o<n.length&&n[o]in e;o++){var i=a[o]=m(A(e[n[o]]));128&i[0]&&(a[o]=new Uint8Array(i.length+1),a[o].set(i,1))}return 2<a.length&&(r=!0,a.unshift(new Uint8Array([0]))),t[0][0]="1.2.840.113549.1.1.1",t.push(new Uint8Array(c(a)).buffer),r?t.unshift(new Uint8Array([0])):t[1]={tag:3,value:t[1]},new Uint8Array(c(t)).buffer}(d(e))})),e}}),["encrypt","decrypt","sign","verify"].forEach(function(s){var p=l[s];l[s]=function(e,t,r,n){if(y&&(!r.byteLength||n&&!n.byteLength))throw new Error("Empty input is not allowed");var a,o,i=[].slice.call(arguments),u=w(e);!y||"sign"!==s&&"verify"!==s||"RSASSA-PKCS1-v1_5"!==e&&"HMAC"!==e||(i[0]={name:e}),y&&t.algorithm.hash&&(i[0].hash=i[0].hash||t.algorithm.hash),y&&"decrypt"===s&&"AES-GCM"===u.name&&(a=e.tagLength>>3,i[2]=(r.buffer||r).slice(0,r.byteLength-a),e.tag=(r.buffer||r).slice(r.byteLength-a)),y&&"AES-GCM"===u.name&&void 0===i[0].tagLength&&(i[0].tagLength=128),i[1]=t._key;try{o=p.apply(l,i)}catch(e){return Promise.reject(e)}return y&&(o=new Promise(function(n,t){o.onabort=o.onerror=function(e){t(e)},o.oncomplete=function(e){var t,r,e=e.target.result;"encrypt"===s&&e instanceof AesGcmEncryptResult&&(t=e.ciphertext,r=e.tag,(e=new Uint8Array(t.byteLength+r.byteLength)).set(new Uint8Array(t),0),e.set(new Uint8Array(r),t.byteLength),e=e.buffer),n(e)}})),o}}),y&&(a=l.digest,l.digest=function(e,t){if(!t.byteLength)throw new Error("Empty input is not allowed");var n;try{n=a.call(l,e,t)}catch(e){return Promise.reject(e)}return n=new Promise(function(t,r){n.onabort=n.onerror=function(e){r(e)},n.oncomplete=function(e){t(e.target.result)}})},e.crypto=Object.create(h,{getRandomValues:{value:function(e){return h.getRandomValues(e)}},subtle:{value:l}}),e.CryptoKey=v),f&&(h.subtle=l,e.Crypto=t,e.SubtleCrypto=r,e.CryptoKey=v)))});
//# sourceMappingURL=webcrypto-shim.min.js.map
