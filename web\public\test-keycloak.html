<!DOCTYPE html>
<html>
<head>
    <title>Keycloak Test</title>
    <script src="https://unpkg.com/keycloak-js@latest/dist/keycloak.min.js"></script>
</head>
<body>
    <h1>Keycloak Test Page</h1>
    <div id="status">Loading...</div>
    <div id="results"></div>
    <button id="loginBtn" style="display:none;">Login</button>
    <button id="logoutBtn" style="display:none;">Logout</button>
    
    <script>
        const statusDiv = document.getElementById('status');
        const resultsDiv = document.getElementById('results');
        const loginBtn = document.getElementById('loginBtn');
        const logoutBtn = document.getElementById('logoutBtn');
        
        function log(message) {
            console.log(message);
            resultsDiv.innerHTML += '<p>' + message + '</p>';
        }
        
        // Simple crypto polyfill
        if (!window.crypto || !window.crypto.getRandomValues) {
            log('Installing crypto polyfill...');
            window.crypto = window.crypto || {};
            window.crypto.getRandomValues = function(array) {
                for (let i = 0; i < array.length; i++) {
                    array[i] = Math.floor(Math.random() * 256);
                }
                return array;
            };
        }
        
        log('Crypto API status: ' + (window.crypto ? 'Available' : 'Not available'));
        log('getRandomValues: ' + (window.crypto.getRandomValues ? 'Available' : 'Not available'));
        
        // Test Keycloak
        const keycloak = new Keycloak({
            url: 'http://localhost:8080',
            realm: 'dev_xh_key',
            clientId: 'sulei_01'
        });
        
        log('Keycloak instance created');
        
        keycloak.init({
            onLoad: 'check-sso',
            checkLoginIframe: false,
            flow: 'implicit',
            responseMode: 'fragment'
        }).then(function(authenticated) {
            statusDiv.innerHTML = authenticated ? 'Authenticated' : 'Not authenticated';
            log('Keycloak init success. Authenticated: ' + authenticated);
            
            if (authenticated) {
                logoutBtn.style.display = 'block';
                log('User: ' + keycloak.tokenParsed.preferred_username);
            } else {
                loginBtn.style.display = 'block';
            }
        }).catch(function(error) {
            statusDiv.innerHTML = 'Error: ' + error.message;
            log('Keycloak init failed: ' + error.message);
            console.error('Keycloak error:', error);
        });
        
        loginBtn.onclick = function() {
            keycloak.login();
        };
        
        logoutBtn.onclick = function() {
            keycloak.logout();
        };
    </script>
</body>
</html>
