describe( 'RSA-OAEP_SHA-256', function () {
    var alg = { name: 'RSA-OAEP', hash: 'SHA-256' },
        pubUse = 'encrypt',
        prvUse = 'decrypt',
        jwkAlg = 'RSA-OAEP-256';

    function skipModLen ( modLen ) {
        return modLen > 2048;
    }

    describe( "generateKey", function () {
        [ 1024, 2048, 3072, 4096 ].forEach( function ( modLen ) {
            (skipModLen(modLen) ? xdescribe : describe)( modLen + " bits", function ( done ) {
                var keyAlg = extend( normalizeAlg(alg), { modulusLength: modLen, publicExponent: x2b('10001') } );

                var generateKeyComplete = new Promise( function ( res, rej ) {
                        skipModLen(modLen) ? rej( new Error("Skipping large RSA moduli") )
                                           : res( crypto.subtle.generateKey( keyAlg, true, [ prvUse, pubUse ] ) );
                    });

                var vectors = [
                    { text: "" },
                    { text: "Hello World!" },
                ];

                it( "generateKey", function ( done ) {
                    generateKeyComplete
                        .then( function ( keyPair ) {
                            expect(keyPair).toBeDefined();

                            expect(keyPair.publicKey).toEqual(jasmine.any(CryptoKey));
                            expect(keyPair.publicKey.type).toBe('public');
                            expect(keyPair.publicKey.algorithm).toEqual(keyAlg);
                            expect(keyPair.publicKey.extractable).toBe(true);
                            expect(keyPair.publicKey.usages).toEqual([pubUse]);

                            expect(keyPair.privateKey).toEqual(jasmine.any(CryptoKey));
                            expect(keyPair.privateKey.type).toBe('private');
                            expect(keyPair.privateKey.algorithm).toEqual(keyAlg);
                            expect(keyPair.privateKey.extractable).toBe(true);
                            expect(keyPair.privateKey.usages).toEqual([prvUse]);
                        })
                        .catch(fail)
                        .then(done);
                }, 30000 );

                describe( "exportKey", function () {
                    it( "spki", function ( done ) {
                        generateKeyComplete
                            .then( function ( keyPair ) {
                                return crypto.subtle.exportKey( 'spki', keyPair.publicKey );
                            })
                            .then( function ( spkiData ) {
                                expect(spkiData).toEqual(jasmine.any(ArrayBuffer));
                            })
                            .catch(fail)
                            .then(done);
                    }, 30000 );

                    it( "pkcs8", function ( done ) {
                        generateKeyComplete
                            .then( function ( keyPair ) {
                                return crypto.subtle.exportKey( 'pkcs8', keyPair.privateKey );
                            })
                            .then( function ( pkcs8Data ) {
                                expect(pkcs8Data).toEqual(jasmine.any(ArrayBuffer));
                            })
                            .catch(fail)
                            .then(done);
                    }, 30000 );

                    it( "jwk publicKey", function ( done ) {
                        generateKeyComplete
                            .then( function ( keyPair ) {
                                return crypto.subtle.exportKey( 'jwk', keyPair.publicKey );
                            })
                            .then( function ( jwkPubKey ) {
                                expect(jwkPubKey).toEqual(jasmine.objectContaining( { 'kty': 'RSA', 'alg': jwkAlg, 'ext': true, 'key_ops': [pubUse] } ));
                            })
                            .catch(fail)
                            .then(done);
                    }, 30000 );

                    it( "jwk privateKey", function ( done ) {
                        generateKeyComplete
                            .then( function ( keyPair ) {
                                return crypto.subtle.exportKey( 'jwk', keyPair.privateKey );
                            })
                            .then( function ( jwkPrvKey ) {
                                expect(jwkPrvKey).toEqual(jasmine.objectContaining( { 'kty': 'RSA', 'alg': jwkAlg, 'ext': true, 'key_ops': [prvUse] } ));
                            })
                            .catch(fail)
                            .then(done);
                    }, 30000 );
                });

                describe( "encrypt", function () {
                    vectors.forEach( function ( v ) {
                        it( "'" + v.text + "' as ArrayBuffer", function ( done ) {
                            generateKeyComplete
                                .then( function ( keyPair ) {
                                    return crypto.subtle.encrypt( alg, keyPair.publicKey, s2b(v.text).buffer );
                                })
                                .then( function ( ciphertext ) {
                                    expect(ciphertext).toEqual( jasmine.any(ArrayBuffer) );
                                    expect(ciphertext.byteLength).toBe(modLen>>3);
                                })
                                .catch(fail)
                                .then(done);
                        });

                        it( "'" + v.text + "' as Uint8Array", function ( done ) {
                            generateKeyComplete
                                .then( function ( keyPair ) {
                                    return crypto.subtle.encrypt( alg, keyPair.publicKey, s2b(v.text) );
                                })
                                .then( function ( ciphertext ) {
                                    expect(ciphertext).toEqual( jasmine.any(ArrayBuffer) );
                                    expect(ciphertext.byteLength).toBe(modLen>>3);
                                })
                                .catch(fail)
                                .then(done);
                        });
                    });
                });

                describe( "decrypt", function () {
                    vectors.forEach( function ( v ) {
                        it( "'" + v.text + "'", function ( done ) {
                            generateKeyComplete
                                .then( function ( keyPair ) {
                                    return crypto.subtle.encrypt( alg, keyPair.publicKey, s2b(v.text) )
                                        .then( function ( ciphertext ) {
                                            return crypto.subtle.decrypt( alg, keyPair.privateKey, ciphertext );
                                        });
                                })
                                .then( function ( cleartext ) {
                                    expect( b2s(cleartext) ).toBe(v.text);
                                })
                                .catch(fail)
                                .then(done);
                        });
                    });
                });
            });
        });
    });

    describe( "importKey", function () {
        var vectors = [
            { modLen: 1024,
              spkiPubKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDCOLweTVN8Q/9IcadFwyH0tSyXHilAYykuyxBIi8xcmsl+zPs5k2o9I6COF5/CV2ISA0d4OeTzuRhDotgct+pUwrIHGfatQ0xx+VUpRJCcW05lkhaeR6OL7c6msheEfsbrnNS9gb+uPbHXcm02sNhJDYDd4k4ha+sDlMEU5IOH0wIDAQAB',
              pkcsPrvKey: 'MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAMI4vB5NU3xD/0hxp0XDIfS1LJceKUBjKS7LEEiLzFyayX7M+zmTaj0joI4Xn8JXYhIDR3g55PO5GEOi2By36lTCsgcZ9q1DTHH5VSlEkJxbTmWSFp5Ho4vtzqayF4R+xuuc1L2Bv649sddybTaw2EkNgN3iTiFr6wOUwRTkg4fTAgMBAAECgYEArCMmr+Cu/rMxfh6lN5Jz9PPiemlT/GompP97BiFJVkYmEglRHD2Ianm6IlXT1aYnGHnpjSgawNyrIb8htYpX145yVoOHCePQQ57/wrT+tKuRq7f9hie0u2O6xshlOay4uUHPN8zL9yC2yz3SwoacnJvHquLFgTjCVu6ILaERm8kCQQDlP8qgiL/GNUHTuze/X8Hbgf2fljG3FOIbmLVJWhn/BJAfWzv9/1a5kEOWX7t9kD7mch5r8b7Dtmhu9qTAKdo/AkEA2OKWkdSTusHflqXU0P7C3+vBD4b8Z+Qsjck/QC7/QpIH84A99qAIMuHeqHuBIGU3GesKiPHkGEpTpeOzPzmlbQJARhtuEg3/59Odn+yfLc1Q8ZodP9KkvYKLazkWJ6qATLbOhGhYPmL52KG/qZr5MXsNYVgA6a3yUtPTuCuBUqr57QJBAKCi0pqYNAKy7YOKt6FDz9pBpvB1LiVUnps1Xx+Or4kC19jGNx6fUPM+z8dCElWIIdOUfm0Hm8VR57qKd4xwidECQQCsHOwYLvOO65MtfHYc7bW+0Qi7IxlVKj95ue1amMg6syETGUpPVqSRlgQMBRJmrPCeAwTfDZrUfJDwI7N7y2Bz',
              jwkPubKey: {"alg":"RSA-OAEP-256","e":"AQAB","ext":true,"key_ops":["encrypt"],"kty":"RSA","n":"wji8Hk1TfEP_SHGnRcMh9LUslx4pQGMpLssQSIvMXJrJfsz7OZNqPSOgjhefwldiEgNHeDnk87kYQ6LYHLfqVMKyBxn2rUNMcflVKUSQnFtOZZIWnkeji-3OprIXhH7G65zUvYG_rj2x13JtNrDYSQ2A3eJOIWvrA5TBFOSDh9M"},
              jwkPrvKey: {"alg":"RSA-OAEP-256","d":"rCMmr-Cu_rMxfh6lN5Jz9PPiemlT_GompP97BiFJVkYmEglRHD2Ianm6IlXT1aYnGHnpjSgawNyrIb8htYpX145yVoOHCePQQ57_wrT-tKuRq7f9hie0u2O6xshlOay4uUHPN8zL9yC2yz3SwoacnJvHquLFgTjCVu6ILaERm8k","dp":"RhtuEg3_59Odn-yfLc1Q8ZodP9KkvYKLazkWJ6qATLbOhGhYPmL52KG_qZr5MXsNYVgA6a3yUtPTuCuBUqr57Q","dq":"oKLSmpg0ArLtg4q3oUPP2kGm8HUuJVSemzVfH46viQLX2MY3Hp9Q8z7Px0ISVYgh05R-bQebxVHnuop3jHCJ0Q","e":"AQAB","ext":true,"key_ops":["decrypt"],"kty":"RSA","n":"wji8Hk1TfEP_SHGnRcMh9LUslx4pQGMpLssQSIvMXJrJfsz7OZNqPSOgjhefwldiEgNHeDnk87kYQ6LYHLfqVMKyBxn2rUNMcflVKUSQnFtOZZIWnkeji-3OprIXhH7G65zUvYG_rj2x13JtNrDYSQ2A3eJOIWvrA5TBFOSDh9M","p":"5T_KoIi_xjVB07s3v1_B24H9n5YxtxTiG5i1SVoZ_wSQH1s7_f9WuZBDll-7fZA-5nIea_G-w7ZobvakwCnaPw","q":"2OKWkdSTusHflqXU0P7C3-vBD4b8Z-Qsjck_QC7_QpIH84A99qAIMuHeqHuBIGU3GesKiPHkGEpTpeOzPzmlbQ","qi":"rBzsGC7zjuuTLXx2HO21vtEIuyMZVSo_ebntWpjIOrMhExlKT1akkZYEDAUSZqzwngME3w2a1HyQ8COze8tgcw"} },
            { modLen: 2048,
              spkiPubKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAviC6FqJnk6d6ycPAnIZ6vdqUxN9HGN9ApNYnul9h3P4vR0ApmiDDSQSRi4WVC0aOAtrEQ/lLRfs4ggsU46sYEDtRhW4WMfjEd3XXzKJuy4jYXgu3ODNlnnaXNzN1lHcmGhG3oZQTT628/MWwCiZEwTZIJXpAHkyMjQNhEYjLpdIMqhXEicpk38rB+WpemllcTJYf/cYu/k9LMTAm9PiP3ANQZyYrDCluyIN+wN8P35W/eLNonyZANLUdIdMMJPa9sbDLez0jmHdeJtpeGXn8juWPsI/S3yQDJtW+LqitRrmjGD4RvRMdfyd/WmQ98HeDd++GxAkRVpLqtx3pPNknoQIDAQAB',
              pkcsPrvKey: 'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC+ILoWomeTp3rJw8Cchnq92pTE30cY30Ck1ie6X2Hc/i9HQCmaIMNJBJGLhZULRo4C2sRD+UtF+ziCCxTjqxgQO1GFbhYx+MR3ddfMom7LiNheC7c4M2Wedpc3M3WUdyYaEbehlBNPrbz8xbAKJkTBNkglekAeTIyNA2ERiMul0gyqFcSJymTfysH5al6aWVxMlh/9xi7+T0sxMCb0+I/cA1BnJisMKW7Ig37A3w/flb94s2ifJkA0tR0h0wwk9r2xsMt7PSOYd14m2l4ZefyO5Y+wj9LfJAMm1b4uqK1GuaMYPhG9Ex1/J39aZD3wd4N374bECRFWkuq3Hek82SehAgMBAAECggEATMD3ftW3TLNq7XL6GfZsLKLGNigREqhl92WBCkshPc7blT8AzHj3fU272ABoZ/HmuJ5KZ0qHqcu+RzlkCHj0sPDRezUy/p936OYI5VKZuc8X0feW0rhlCLDFYQKEMBhdqF6IrFET7rGrvrur0p0aWomoXIDay6CJiQ/ZKvP3iovsaDEshaDuwT+jai9HPdDGckWCxx/DfbzgdzXwdN8+6NMfwn0apZb6DU/J0FZ8hjGGJCMfhrjTJnXvoq/VJt34ii0xj3ldPi+DVxDCAAaYmPpVX2YWvnpHVbz0/DQLt6+hJtUTSf9nBJ8MNCNSWNYmJ1ANHsJYNxZiKJGSIdcx9QKBgQDhPnIj6wVqso1srEfY32wOhtbQx0ls97OAt0v5Qtyosy+4UuiH8dZw7Vv7+1mbg6kYmO5h0Y3nPd3rcfXoGqIi6vutCTsiXI378/9pYXjl/bGdqHR9996dB3uhW7LyRk23Cw3hRt4hp0NYZZCvD4RnOutAD3acx9PBlwCkY6WfywKBgQDYFsY7Wi5WG31m2v03i+ugluzYNdVz24vkIuFO1BWngnIwYmBc/jCWQt00/njHuzRMywf/etUL+oRE6EVVmC48KX4/mUseuK1n+ra36B4cQE1IoB+Hmb8CemG4fcL/42PVni6cZc8aq8v0OQ6Yy4+LJphiPFKuNpKPrEjc0SNQwwKBgCJi2x/6oCAS1B5UCr/kE+X/1cmXsvDsu72ZvgJ2n6Mtf8p+9brTQ66HvfQxAhQIvIbhyfqq+CPmQPvRsP3XGwuDnhpjf2CWiqJ9NG/NDpzl5vivn+EfNx/35XrTufTcoL6h9GOA4yZ2F4TmNHlVQBxWVVW5Rp1WsFAskk+GWuGTAoGBAJCctS81S/s+TG8QMRQCZL9FId4UMPRnQjh8C1Ko5pEC4I5218yEJFn7B3UWtBfetcKKwaB9QKTSk8BVvUjqHk8O6bmASmCV8R68D5oGEliPw+jNmCw0fTsYUduY9m4vbDmiscji7XYI8OZRZO5mlHroamSbwC1swiq6fsygfcTnAoGBAJEpnjZHT7gvS8xLdpL8ckEmY6AaZfOTEJ6thqRROeB7ZSLhXyOm9IIQgiFD8vtlhUvee2Fc0Ypvoym3B5mFXpESja/uRr8cYS31gTZtSWX74xpWCer7ldR+JhxjmVcGgj+oIOyuAzlcgSKlhRrYxjVziY6FAfuu0xPSB4TfvgSC',
              jwkPubKey: {"alg":"RSA-OAEP-256","e":"AQAB","ext":true,"key_ops":["encrypt"],"kty":"RSA","n":"viC6FqJnk6d6ycPAnIZ6vdqUxN9HGN9ApNYnul9h3P4vR0ApmiDDSQSRi4WVC0aOAtrEQ_lLRfs4ggsU46sYEDtRhW4WMfjEd3XXzKJuy4jYXgu3ODNlnnaXNzN1lHcmGhG3oZQTT628_MWwCiZEwTZIJXpAHkyMjQNhEYjLpdIMqhXEicpk38rB-WpemllcTJYf_cYu_k9LMTAm9PiP3ANQZyYrDCluyIN-wN8P35W_eLNonyZANLUdIdMMJPa9sbDLez0jmHdeJtpeGXn8juWPsI_S3yQDJtW-LqitRrmjGD4RvRMdfyd_WmQ98HeDd--GxAkRVpLqtx3pPNknoQ"},
              jwkPrvKey: {"alg":"RSA-OAEP-256","d":"TMD3ftW3TLNq7XL6GfZsLKLGNigREqhl92WBCkshPc7blT8AzHj3fU272ABoZ_HmuJ5KZ0qHqcu-RzlkCHj0sPDRezUy_p936OYI5VKZuc8X0feW0rhlCLDFYQKEMBhdqF6IrFET7rGrvrur0p0aWomoXIDay6CJiQ_ZKvP3iovsaDEshaDuwT-jai9HPdDGckWCxx_DfbzgdzXwdN8-6NMfwn0apZb6DU_J0FZ8hjGGJCMfhrjTJnXvoq_VJt34ii0xj3ldPi-DVxDCAAaYmPpVX2YWvnpHVbz0_DQLt6-hJtUTSf9nBJ8MNCNSWNYmJ1ANHsJYNxZiKJGSIdcx9Q","dp":"ImLbH_qgIBLUHlQKv-QT5f_VyZey8Oy7vZm-Anafoy1_yn71utNDroe99DECFAi8huHJ-qr4I-ZA-9Gw_dcbC4OeGmN_YJaKon00b80OnOXm-K-f4R83H_fletO59NygvqH0Y4DjJnYXhOY0eVVAHFZVVblGnVawUCyST4Za4ZM","dq":"kJy1LzVL-z5MbxAxFAJkv0Uh3hQw9GdCOHwLUqjmkQLgjnbXzIQkWfsHdRa0F961worBoH1ApNKTwFW9SOoeTw7puYBKYJXxHrwPmgYSWI_D6M2YLDR9OxhR25j2bi9sOaKxyOLtdgjw5lFk7maUeuhqZJvALWzCKrp-zKB9xOc","e":"AQAB","ext":true,"key_ops":["decrypt"],"kty":"RSA","n":"viC6FqJnk6d6ycPAnIZ6vdqUxN9HGN9ApNYnul9h3P4vR0ApmiDDSQSRi4WVC0aOAtrEQ_lLRfs4ggsU46sYEDtRhW4WMfjEd3XXzKJuy4jYXgu3ODNlnnaXNzN1lHcmGhG3oZQTT628_MWwCiZEwTZIJXpAHkyMjQNhEYjLpdIMqhXEicpk38rB-WpemllcTJYf_cYu_k9LMTAm9PiP3ANQZyYrDCluyIN-wN8P35W_eLNonyZANLUdIdMMJPa9sbDLez0jmHdeJtpeGXn8juWPsI_S3yQDJtW-LqitRrmjGD4RvRMdfyd_WmQ98HeDd--GxAkRVpLqtx3pPNknoQ","p":"4T5yI-sFarKNbKxH2N9sDobW0MdJbPezgLdL-ULcqLMvuFLoh_HWcO1b-_tZm4OpGJjuYdGN5z3d63H16BqiIur7rQk7IlyN-_P_aWF45f2xnah0fffenQd7oVuy8kZNtwsN4UbeIadDWGWQrw-EZzrrQA92nMfTwZcApGOln8s","q":"2BbGO1ouVht9Ztr9N4vroJbs2DXVc9uL5CLhTtQVp4JyMGJgXP4wlkLdNP54x7s0TMsH_3rVC_qEROhFVZguPCl-P5lLHritZ_q2t-geHEBNSKAfh5m_AnphuH3C_-Nj1Z4unGXPGqvL9DkOmMuPiyaYYjxSrjaSj6xI3NEjUMM","qi":"kSmeNkdPuC9LzEt2kvxyQSZjoBpl85MQnq2GpFE54HtlIuFfI6b0ghCCIUPy-2WFS957YVzRim-jKbcHmYVekRKNr-5GvxxhLfWBNm1JZfvjGlYJ6vuV1H4mHGOZVwaCP6gg7K4DOVyBIqWFGtjGNXOJjoUB-67TE9IHhN--BII"} },
            { modLen: 3072,
              spkiPubKey: 'MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAuUuAFGMt/JC59sGxLP7p+vgE2vgZ7DAeFGHM7kaaYwNKN8KI5E3HZepkVaWpxSRzqoqYIDpeRqmBF0k0omvytfRofeilFTCmIXuZIJO1BfAc2v2tb6LRivyCCJpJdd5QHm56eFJeP5PltjQfEclrjO+icMc5W+5SdH+/bukyJTjwyU4rpzfoRcGgHJbFdIGLT1YyKyIvjd2tuhFEV6TOvySsAUykq6uScFoOpp5FouOX+A7ZFbW7pCsIf9a+MU2ZQS4im09tzeBPqFpctg4Rr7IQs77xHSeGPvcuuR+RIk5/ny3y2NJC1K3l/Q9TQrvWj4iQ681w3ipXedv6kwPa9r860hE/HTIiR08FedmkhbtLANx8Kkk6rmKxYCTL7kcH6l/cPE4M/s3EarvID7S1l44NOpfJCorNWlNN+RV2oVMJAvGy0sr/x0Y/Ba42s6GLkvbviOqHkhvUgrPXI57hq2TnkSKCxGACoJA9tOg1I90hb3R2odIFXNkloYnBUsKjAgMBAAE=',
              pkcsPrvKey: '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',
              jwkPubKey: {"alg":"RSA-OAEP-256","e":"AQAB","ext":true,"key_ops":["encrypt"],"kty":"RSA","n":"uUuAFGMt_JC59sGxLP7p-vgE2vgZ7DAeFGHM7kaaYwNKN8KI5E3HZepkVaWpxSRzqoqYIDpeRqmBF0k0omvytfRofeilFTCmIXuZIJO1BfAc2v2tb6LRivyCCJpJdd5QHm56eFJeP5PltjQfEclrjO-icMc5W-5SdH-_bukyJTjwyU4rpzfoRcGgHJbFdIGLT1YyKyIvjd2tuhFEV6TOvySsAUykq6uScFoOpp5FouOX-A7ZFbW7pCsIf9a-MU2ZQS4im09tzeBPqFpctg4Rr7IQs77xHSeGPvcuuR-RIk5_ny3y2NJC1K3l_Q9TQrvWj4iQ681w3ipXedv6kwPa9r860hE_HTIiR08FedmkhbtLANx8Kkk6rmKxYCTL7kcH6l_cPE4M_s3EarvID7S1l44NOpfJCorNWlNN-RV2oVMJAvGy0sr_x0Y_Ba42s6GLkvbviOqHkhvUgrPXI57hq2TnkSKCxGACoJA9tOg1I90hb3R2odIFXNkloYnBUsKj"},
              jwkPrvKey: {"alg":"RSA-OAEP-256","d":"mzzPz0TQcDroMf1QbLwcVzCXEMSvIxuk97qAMFhiW_WTpmeKvIsVNSNMcBzcwNerOAPpM4bzGQxFpLamLDV4IEzlhib0t24MoRyly480KAdkhWvmdBLarzCh2Ic6aE-anAQ_ArMvVuCXoTDFnuc6_dZ-JzIhsg1Wc1fbkBY69shfoqelFvFV3as8cvc18kfd6Nf0LAL2CErik3KH6U2gEK-uk2Aczd5uT0aD47_R44f998qowPGChSWwdzn4GeJBxleQ3dfNgAGhaVKvQuOLv8J51jXvE0Taew1IQCH7AVsbmjvnDYRQGuBRV8enObLHw8rn39XWmDFpWIvaiQqoyU3J5hEzjmxQbsG_CyfiOejxtTs1si40vvoPrmqtjZAOOyqHPhbwj8dJj2iLNJ3DTtnFLM5tg1A3MJKp_A2gLBOi6PwtmqSULcB0GZ53HIWsDuEDsQUjckyNewR4zuTrhgFTllZxYiynPvzcX4sQsoJ8DkCJJGFTFqddWChwRUzB","dp":"6XMsJgXRKRilrOFwlQxq00CeWjzOQ8DSzZXIlEd5Yvpn134lVqP92tqHlRv-o7x2QfprljNxp4quqJuvFEd9MXUo1jxkGrsTk4wQZ1kF_YRalwAXiPl_lhs9I-2GbvlU2T033NE0J7UE5wPcFcu8gZXqGG2w3O9yOTO6yfrj7OGhc_qsnF3pF-nMHVxDv978KQ7M5s-U6Fubmb20EhnpgX3WeHwhrflfZgeXfD_wPNzf9mfHnGNk39gLVRPnBfKB","dq":"IB_IrB1SHuVDxz-MdOj6b06qe2UzX5rt_3rBGXQ7jxwGhsIk9mVEGs_ExmElPxscqKG5k3ej5pTu0Y0HSQ_gvACpfo2B7y9nVpM1ExlZfIwfCXH5JmSezDO9hJ389SuZJMSfGtBpXkcW8i5i61yUBib2LEGx-kT-6scA7gK8jqVEPHL63XZKLc9ZekSf3Vg4y1KB6mSfGEsTmLYqEoNwKVwxGJ16tWxSoEIBN1FSAK3pvotnL2HHfE9ECR8u_r2B","e":"AQAB","ext":true,"key_ops":["decrypt"],"kty":"RSA","n":"uUuAFGMt_JC59sGxLP7p-vgE2vgZ7DAeFGHM7kaaYwNKN8KI5E3HZepkVaWpxSRzqoqYIDpeRqmBF0k0omvytfRofeilFTCmIXuZIJO1BfAc2v2tb6LRivyCCJpJdd5QHm56eFJeP5PltjQfEclrjO-icMc5W-5SdH-_bukyJTjwyU4rpzfoRcGgHJbFdIGLT1YyKyIvjd2tuhFEV6TOvySsAUykq6uScFoOpp5FouOX-A7ZFbW7pCsIf9a-MU2ZQS4im09tzeBPqFpctg4Rr7IQs77xHSeGPvcuuR-RIk5_ny3y2NJC1K3l_Q9TQrvWj4iQ681w3ipXedv6kwPa9r860hE_HTIiR08FedmkhbtLANx8Kkk6rmKxYCTL7kcH6l_cPE4M_s3EarvID7S1l44NOpfJCorNWlNN-RV2oVMJAvGy0sr_x0Y_Ba42s6GLkvbviOqHkhvUgrPXI57hq2TnkSKCxGACoJA9tOg1I90hb3R2odIFXNkloYnBUsKj","p":"8bcXqDGpvBedZsqJqxYAYw4tqm5uyfZBSa1CLRq3b4OJx3dQ2CzdDQp-QFaI7dxiWw1isCRFOKw5J2L5uQml5IHJ8m6BQiHWYn1tcQZlTOtjxFBuTj2j6dj97QSSXS8gLBO91yOH3b1l1yM3PP18TPPAgtBo4EreQWIN9-8aC6uses6xJQ7GwOrrlHuapUj6mMOl4Q2zm4EuSmQ60n_aUbIvEjHN9DPS1CdVsan72HMLd55oEs-ZxLYk8FYuLetT","q":"xD7TbAC4tAGXTWUTYzU_ASvzQjxLtkVuZiaTeh0jA87htoTEEc6dr-nYcGRPa95M1QInEWawu61G-r8c-LnKcvTeMQm_ZWarkqx4zP3Rj4BfM8I1pvx_KRrEcm8kiadhj1B2WJEvgN63fMlVLyQQ3VO36mzFrbJUwVWc2bdT1s8BwlHhd1dsuKOWFnsu0WwBnJ5iDMFrZ4VXtu0qxz4bad0fnRQfwlDAutAeYpDtJfzP_72z5XzSteZtZXIpkTFx","qi":"2V8GBEvcw2ZGeHSUQIJgfhpjmoUUn-CxQi9ADDSzptjhGT9WQEm8KHw8WdnV9vPQ_QudDcLe-4XFGyMgIDot8ByMWpVd5kRBk1RZCQYV89CXQ7YEK4WQGzU_Kqe5k3I-LHHyE3vN7XuZkeRWgSa6b_MghrXU8YH-bYIKo3yDluYZz75kJj_C0ld0HzW5NZ-v69gskjB8faq7dG-9OmT7eHHS50j8E6XmNYsVlZAQShtNeh6xYgD6n8mfEeXeGgmR"} },
            { modLen: 4096,
              spkiPubKey: 'MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAosZ8eDUKrI6AXDBF+Yd2RhlqEDyfCM8/tvIxsYVLMjUGq5RZLqeYixOuMi7cGZkxKVWhldOW3VSZE+MjVPLUg4rBVfYr9uD1XHByxi+xESgiZqJEXsoU7JwQqGAQamTpPbqo6ZN2+BnqHIJtSGVQwOe3IBcJecg223cUgCESPpl/X70MVNsHj8JriFAI7pXph+I/KAdGSJIoZ76+/BapR9wwBM42+8kTVSB08jodNWLOTm0SmgCUQe68m5hN2cdZ3Dimph/M5JwWCTC5mxt7vV//7xNNNYaM5w/t7X6tySXSxJBftKbH4C4TqGuVHZmo1u78g8/uljoFet5185as67JexTWvzunGOMNSOUT+4FZAq+y0Jr5QfNhyGuN3j+jPTcJv0zT/EWbXpMOu1Z61wsbn5PlAH2+p0O3SZ6aBEU3VqIcoY7lix2e6ZZVSzfwCKsLxvElctwaKbFrwBrAJmIWjQhqrjztWGW7QKUFR/Uw6wV5mr7ijtDzgUX8PqEoFWVpsFKfE5TYygGFrxgLy/GzRyg+37+BItaM2EBN4yxuQtV6PT60GXMFhbNIMgssh70orjO9wERGiBM1cKyJSfC8SQXTRXSSDHLQOeVwgDqcHie+Gh01adkvyz7I+LLnjxHGJb76yM39PoYuYj5jZN1sQyEnEmQDvcqTGijhse50CAwEAAQ==',
              pkcsPrvKey: '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',
              jwkPubKey: {"alg":"RSA-OAEP-256","e":"AQAB","ext":true,"key_ops":["encrypt"],"kty":"RSA","n":"osZ8eDUKrI6AXDBF-Yd2RhlqEDyfCM8_tvIxsYVLMjUGq5RZLqeYixOuMi7cGZkxKVWhldOW3VSZE-MjVPLUg4rBVfYr9uD1XHByxi-xESgiZqJEXsoU7JwQqGAQamTpPbqo6ZN2-BnqHIJtSGVQwOe3IBcJecg223cUgCESPpl_X70MVNsHj8JriFAI7pXph-I_KAdGSJIoZ76-_BapR9wwBM42-8kTVSB08jodNWLOTm0SmgCUQe68m5hN2cdZ3Dimph_M5JwWCTC5mxt7vV__7xNNNYaM5w_t7X6tySXSxJBftKbH4C4TqGuVHZmo1u78g8_uljoFet5185as67JexTWvzunGOMNSOUT-4FZAq-y0Jr5QfNhyGuN3j-jPTcJv0zT_EWbXpMOu1Z61wsbn5PlAH2-p0O3SZ6aBEU3VqIcoY7lix2e6ZZVSzfwCKsLxvElctwaKbFrwBrAJmIWjQhqrjztWGW7QKUFR_Uw6wV5mr7ijtDzgUX8PqEoFWVpsFKfE5TYygGFrxgLy_GzRyg-37-BItaM2EBN4yxuQtV6PT60GXMFhbNIMgssh70orjO9wERGiBM1cKyJSfC8SQXTRXSSDHLQOeVwgDqcHie-Gh01adkvyz7I-LLnjxHGJb76yM39PoYuYj5jZN1sQyEnEmQDvcqTGijhse50"},
              jwkPrvKey: {"alg":"RSA-OAEP-256","d":"X19KyKfTZD1uahWCumyrWPFFGtRaHjBIVancgfXmlDlLejbGdjaOg1Z5pz-biIggrOZWcdHywCX0NxnLttc8uwIOu44sPk-ak5nzNbn11macmWQUCTIARo4-mFkI5T3RB3EZzRmxCDcu25tz6ooqYYyi2HOHAsrTkY1Y_qXh6bnITtEWq5VV_d5dYRX71ccSeKKQfKSegqDYecjiYBPrBYa2v-oPf0FWSXdl7hMsz_3fgUYZMxCJzYPTEJuRabuLJCQxSH6zb3Mg2iupZH8a5l-cNnkpsOXwIuZLcu7ZZlT6BF_PI6rgy7JTpEe8rAY_Tgfi_lxTn8PJOC-mDp5HHDcJkhnO6RfFo0h1TnA8aWBccFj1sGFvf230l2vl6bWWLEYe4CKwt6OUqbvuFkAzR2MG3bRJZOe4nAiJ1-PeHirNb9Kctbi3ru8E7fcgHp4evzD1tByxWglA176IJN6_hfHMCZXL_bBi1uJUBWmqLauE5lerquEhmw8mICW9BCois3DlnbUTxPvsUf21U7NGMdsW_gpk0RLQIsxLHeWryvE5_PfbmDG405OCSFidAWCNT16PjzfYstnEjeHHt0GmLMnpBwtG6WlPVActLmSC9CB7_d6UTVY6hcPb5y7VmAUeFvnLz13cW9_cDDSy-bbWN_YAqeCwQSqYf-wq_lpepAE","dp":"UrZ-DkqNRN2JtEjEFAwFthcNtq11LlVEfdQIUc5lcnFbhP2awu96Wh8HSGCgj5QbZ-B4yyMIYYoZhGaX_yURgVzv-ENZpEQqVXwG1LlWZlH33yyYBO53GhbWz4lnFp3ygPcqk3-ja1UyCFzyk1xndnM-ewaG7xg1jag1fBR54pUugzE8x3Xo6cf4E8pHBcWwHeOX9xjW-O7uxkac0RYquvGsKTpQEIMvpYIbRnuEsQ-7cT7cmzOoeUqOWTlathYJFj430cPzFEEEmYZz711jQJCxRpDf3dP8ME9UlIfpVZfzPkzmkVHW4r6UCDISrypBXwK57pOFfnZXGZsblzUJnQ","dq":"nGRRKT3AypqmUdeLABinNFc9CSEZulpKsNfxz01fafqURTA1STyOy0Eerj-7nzTR3NvnZXjI_s6rZbG-nja1DZE-D3meEIdwZ4G6XO03djvHFhuWOn0Cdw5iIjy_r98bVUiDOtNliUWaUZRAbziP_fEqKwvbyN5Zs3SIPpAHu_KlCZ-ht2aGMn3v7JSadj9x6dFNmcWTaukzgG0HQ3FGCc25CTawqkjTft1FbpiFV7EuSmWItI9wn67tnOd224IgeRBSI2Sz_uTc9yYiGWnrXUI1kUrkcY11ds0wa471RYS8uJGU9hIDDWefTUZazclr-gH-RO330yzbKXZ9MTOaAQ","e":"AQAB","ext":true,"key_ops":["decrypt"],"kty":"RSA","n":"osZ8eDUKrI6AXDBF-Yd2RhlqEDyfCM8_tvIxsYVLMjUGq5RZLqeYixOuMi7cGZkxKVWhldOW3VSZE-MjVPLUg4rBVfYr9uD1XHByxi-xESgiZqJEXsoU7JwQqGAQamTpPbqo6ZN2-BnqHIJtSGVQwOe3IBcJecg223cUgCESPpl_X70MVNsHj8JriFAI7pXph-I_KAdGSJIoZ76-_BapR9wwBM42-8kTVSB08jodNWLOTm0SmgCUQe68m5hN2cdZ3Dimph_M5JwWCTC5mxt7vV__7xNNNYaM5w_t7X6tySXSxJBftKbH4C4TqGuVHZmo1u78g8_uljoFet5185as67JexTWvzunGOMNSOUT-4FZAq-y0Jr5QfNhyGuN3j-jPTcJv0zT_EWbXpMOu1Z61wsbn5PlAH2-p0O3SZ6aBEU3VqIcoY7lix2e6ZZVSzfwCKsLxvElctwaKbFrwBrAJmIWjQhqrjztWGW7QKUFR_Uw6wV5mr7ijtDzgUX8PqEoFWVpsFKfE5TYygGFrxgLy_GzRyg-37-BItaM2EBN4yxuQtV6PT60GXMFhbNIMgssh70orjO9wERGiBM1cKyJSfC8SQXTRXSSDHLQOeVwgDqcHie-Gh01adkvyz7I-LLnjxHGJb76yM39PoYuYj5jZN1sQyEnEmQDvcqTGijhse50","p":"1VcBwDr0mV9TsVFSAo9jK8jJPR4iJxifOAiGa9oRzRu8j3ECvF4nxdHZhuprGePMqCIZ46WPd1M4srHACkmTUDuoj0MW7_LEbAeUZ51gGoWEoYeiRqbn1dMiXsTqWUGcWHoQ7tgZ-8t2L_e6SG4hQbUR_KUhspgKdOjt5dguvUNDRpL0xfGYpVKBD302WyWrgV9R2PoWKcAkACzYc7FpbX2twWCk27Y2B7HnuYrRRGJrWchsgp3-3GowTFOVPORLhrpue_FCKmrdqMJ3KtKeGV5khceVWfCGUzx4ViCkZtHqHod6v34nPRSSPSMe7uRJ4kd7lcg0VIyVoslf2SgOnQ","q":"w1MNHI3XPHF2HQvq7DNvxhLWkBrdpGY9vUsEQp5GdqNOxNMofV6ipt4QaJbvBuuGWvIi4QWQWyF33n_SLg99qhvTZL3dBWcv5Jy1wDSN4v9zRWqFaSZXOtSTkjTVvpntCKxkl_i-DePbsv01ogD9zB3lihqNraPYjPauLm0YywArsbEelYpL-YCaIyDoCloZUP2lStzyuQnvUrboPlTqHC17gulD3b1s6xMTqP6kuOlX-o37ipoXeLlLuZPjj_3zbcNEaAuWrb26c11pcalwE7ycZ9unMSgHb68q3fk7h_UBOdybuTJXyuUQVASHGVwvW9WxrvAv7tmi43Ldd7wRAQ","qi":"Di2Kfv6XKuzKZKOVInQWZINOE1e9RVRZkDyeC-Mu5xxL8vifycvKQcwDL-2MFyNu6HaAg070-zh7n2DEnY7yzvntUX6_Uwk741KmJeHCR6sjJe1h9K5OihXvjGqbXNqadiVebXTQBJRvbKz9DH1OFcyXWWf_JlB_fxqMbYwq_mdlPd12-wd1gPPlVsdfFFUvxUoaYoqynzJS_GAu0Pl5XKMRgXo7K2i6q8trxxf86MwEPey_MS8wUxc6oV6QdAzgM_VHk2OrBSQ1H6oWIBDwUlarXwDMA7zlYwVHdJ56MgeWvX5QGu6-ZXntcT-NZYY0Xv_2v3r7BSKXSq_fIpoHLA"} },
        ];

        vectors.forEach( function ( v ) {
            describe( v.modLen + " bits", function () {
                var keyAlg = extend( normalizeAlg(alg), { modulusLength: v.modLen, publicExponent: x2b('10001') } );

                it( "spki publicKey", function ( done ) {
                    crypto.subtle.importKey( "spki", s2b( atob(v.spkiPubKey) ), alg, true, [ pubUse ] )
                        .then( function ( key ) {
                            expect(key).toEqual(jasmine.any(CryptoKey));
                            expect(key.type).toBe('public');
                            expect(key.extractable).toBe(true);
                            expect(key.algorithm).toEqual(keyAlg);
                            expect(key.usages).toEqual([pubUse]);
                        })
                        .catch(fail)
                        .then(done);
                });

                it( "pkcs8 privateKey", function ( done ) {
                    crypto.subtle.importKey( "pkcs8", s2b( atob(v.pkcsPrvKey) ), alg, false, [ prvUse ] )
                        .then( function ( key ) {
                            expect(key).toEqual(jasmine.any(CryptoKey));
                            expect(key.type).toBe('private');
                            expect(key.extractable).toEqual(false);
                            expect(key.algorithm).toEqual(keyAlg);
                            expect(key.usages).toEqual([prvUse]);
                        })
                        .catch(fail)
                        .then(done);
                });

                it( "jwk publicKey", function ( done ) {
                    crypto.subtle.importKey( "jwk", v.jwkPubKey, alg, true, [ pubUse ] )
                        .then( function ( key ) {
                            expect(key).toEqual(jasmine.any(CryptoKey));
                            expect(key.type).toBe('public');
                            expect(key.extractable).toBe(true);
                            expect(key.algorithm).toEqual(keyAlg);
                            expect(key.usages).toEqual([pubUse]);
                        })
                        .catch(fail)
                        .then(done);
                });

                it( "jwk privateKey", function ( done ) {
                    crypto.subtle.importKey( "jwk", v.jwkPrvKey, alg, false, [ prvUse ] )
                        .then( function ( key ) {
                            expect(key).toEqual(jasmine.any(CryptoKey));
                            expect(key.type).toBe('private');
                            expect(key.extractable).toEqual(false);
                            expect(key.algorithm).toEqual(keyAlg);
                            expect(key.usages).toEqual([prvUse]);
                        })
                        .catch(fail)
                        .then(done);
                });
            });
        });
    });

    describe( "decrypt", function () {
        var vectors = [
            { modLen: 1024,
              jwkPrvKey: {"alg":"RSA-OAEP-256","d":"rCMmr-Cu_rMxfh6lN5Jz9PPiemlT_GompP97BiFJVkYmEglRHD2Ianm6IlXT1aYnGHnpjSgawNyrIb8htYpX145yVoOHCePQQ57_wrT-tKuRq7f9hie0u2O6xshlOay4uUHPN8zL9yC2yz3SwoacnJvHquLFgTjCVu6ILaERm8k","dp":"RhtuEg3_59Odn-yfLc1Q8ZodP9KkvYKLazkWJ6qATLbOhGhYPmL52KG_qZr5MXsNYVgA6a3yUtPTuCuBUqr57Q","dq":"oKLSmpg0ArLtg4q3oUPP2kGm8HUuJVSemzVfH46viQLX2MY3Hp9Q8z7Px0ISVYgh05R-bQebxVHnuop3jHCJ0Q","e":"AQAB","ext":true,"key_ops":["decrypt"],"kty":"RSA","n":"wji8Hk1TfEP_SHGnRcMh9LUslx4pQGMpLssQSIvMXJrJfsz7OZNqPSOgjhefwldiEgNHeDnk87kYQ6LYHLfqVMKyBxn2rUNMcflVKUSQnFtOZZIWnkeji-3OprIXhH7G65zUvYG_rj2x13JtNrDYSQ2A3eJOIWvrA5TBFOSDh9M","p":"5T_KoIi_xjVB07s3v1_B24H9n5YxtxTiG5i1SVoZ_wSQH1s7_f9WuZBDll-7fZA-5nIea_G-w7ZobvakwCnaPw","q":"2OKWkdSTusHflqXU0P7C3-vBD4b8Z-Qsjck_QC7_QpIH84A99qAIMuHeqHuBIGU3GesKiPHkGEpTpeOzPzmlbQ","qi":"rBzsGC7zjuuTLXx2HO21vtEIuyMZVSo_ebntWpjIOrMhExlKT1akkZYEDAUSZqzwngME3w2a1HyQ8COze8tgcw"},
              subvectors: [
                { text: "",
                  ciphertext: 'IvNeJAcxG6f+4A1kZ2uRA0hyi5Jm4RBrhvkLtYPVeUQZ/dEzS1QA/S8ar4KbdRuKuOVeAzTJ7NP86Nd+73yCZr1LY/NF4exPciBQLkms89DGQkWAmq1owvW5YKarJWxpxtcYlDJnylrKmKDV/BJcWj2urI9uK+qgc65gLgasHRg=' },
                { text: "Hello World!",
                  ciphertext: 'YivS33H5siq8hxvOhFHd7KZl3+JwTJ8/gqM/6CnWYrFS9RKNlyNtQ4H5diiKlee0zt1pg2LJpZN9PUJ4IZBSBMxeVjGPQaG/DjFaYztFV9A6Io+84bu3LF3JeovIzCIPSOw6T9e0I+cycj8mobRSMSqNCFxAiO43dWwgEHzfZI4=' },
              ] },
            { modLen: 2048,
              jwkPrvKey: {"alg":"RSA-OAEP-256","d":"TMD3ftW3TLNq7XL6GfZsLKLGNigREqhl92WBCkshPc7blT8AzHj3fU272ABoZ_HmuJ5KZ0qHqcu-RzlkCHj0sPDRezUy_p936OYI5VKZuc8X0feW0rhlCLDFYQKEMBhdqF6IrFET7rGrvrur0p0aWomoXIDay6CJiQ_ZKvP3iovsaDEshaDuwT-jai9HPdDGckWCxx_DfbzgdzXwdN8-6NMfwn0apZb6DU_J0FZ8hjGGJCMfhrjTJnXvoq_VJt34ii0xj3ldPi-DVxDCAAaYmPpVX2YWvnpHVbz0_DQLt6-hJtUTSf9nBJ8MNCNSWNYmJ1ANHsJYNxZiKJGSIdcx9Q","dp":"ImLbH_qgIBLUHlQKv-QT5f_VyZey8Oy7vZm-Anafoy1_yn71utNDroe99DECFAi8huHJ-qr4I-ZA-9Gw_dcbC4OeGmN_YJaKon00b80OnOXm-K-f4R83H_fletO59NygvqH0Y4DjJnYXhOY0eVVAHFZVVblGnVawUCyST4Za4ZM","dq":"kJy1LzVL-z5MbxAxFAJkv0Uh3hQw9GdCOHwLUqjmkQLgjnbXzIQkWfsHdRa0F961worBoH1ApNKTwFW9SOoeTw7puYBKYJXxHrwPmgYSWI_D6M2YLDR9OxhR25j2bi9sOaKxyOLtdgjw5lFk7maUeuhqZJvALWzCKrp-zKB9xOc","e":"AQAB","ext":true,"key_ops":["decrypt"],"kty":"RSA","n":"viC6FqJnk6d6ycPAnIZ6vdqUxN9HGN9ApNYnul9h3P4vR0ApmiDDSQSRi4WVC0aOAtrEQ_lLRfs4ggsU46sYEDtRhW4WMfjEd3XXzKJuy4jYXgu3ODNlnnaXNzN1lHcmGhG3oZQTT628_MWwCiZEwTZIJXpAHkyMjQNhEYjLpdIMqhXEicpk38rB-WpemllcTJYf_cYu_k9LMTAm9PiP3ANQZyYrDCluyIN-wN8P35W_eLNonyZANLUdIdMMJPa9sbDLez0jmHdeJtpeGXn8juWPsI_S3yQDJtW-LqitRrmjGD4RvRMdfyd_WmQ98HeDd--GxAkRVpLqtx3pPNknoQ","p":"4T5yI-sFarKNbKxH2N9sDobW0MdJbPezgLdL-ULcqLMvuFLoh_HWcO1b-_tZm4OpGJjuYdGN5z3d63H16BqiIur7rQk7IlyN-_P_aWF45f2xnah0fffenQd7oVuy8kZNtwsN4UbeIadDWGWQrw-EZzrrQA92nMfTwZcApGOln8s","q":"2BbGO1ouVht9Ztr9N4vroJbs2DXVc9uL5CLhTtQVp4JyMGJgXP4wlkLdNP54x7s0TMsH_3rVC_qEROhFVZguPCl-P5lLHritZ_q2t-geHEBNSKAfh5m_AnphuH3C_-Nj1Z4unGXPGqvL9DkOmMuPiyaYYjxSrjaSj6xI3NEjUMM","qi":"kSmeNkdPuC9LzEt2kvxyQSZjoBpl85MQnq2GpFE54HtlIuFfI6b0ghCCIUPy-2WFS957YVzRim-jKbcHmYVekRKNr-5GvxxhLfWBNm1JZfvjGlYJ6vuV1H4mHGOZVwaCP6gg7K4DOVyBIqWFGtjGNXOJjoUB-67TE9IHhN--BII"},
              subvectors: [
                { text: "",
                  ciphertext: 'W2ndc5Y0coQobSi5n3foHj2CZ74yrsYnpV/7YthrWnDm8yehKFPvj2mjlEYguUmQQdKLStcKwmdzgfItB+8EFBRyHzXOwezfer0hO0znctF6+vJ3ZYzcyvJH+01INQcx4/LMDaZOU8LTlI+Vjb2DUgSktGgoj872TLjYPo5d01X9L5a2UZq9b5kdC8Fsp1/UZ2QySVbhbxBssCoWF2Ra4CyQAFFq9e8XnKokYmYgaXQihpAa9dYVEl3kuF1TJL9yzmIRJ/dzHVrZITmXlyRCQhqtGE+WpXYhOrTJWoiPIChqof1PRzNsCFfEbjZ2omhnigdHuwvDJa6Z46JmLCHNow==' },
                { text: "Hello World!",
                  ciphertext: 'Aih/ZciL0YYP/6yoJj/5c0fWY6FaiFYo/wwNjZ+JmxhVfF34PealEz98LnhWZMXLPEJlseNRZWOtJbof6L4iyEavhYQq8Yp7gG2Xr1D2+8VsLQBb5N3s7skuC8C+YPhm/IMFSFzneZf/DlwvSD4Ew+MhZy+NtRlHsyq9h+zoaQtBodYRjRf98NIHbRk88DelXh3bQ4yO/TysvLQWHoJ2CUGi4/VCXTgyOa/z6bqgaIQ/JitdGP8/+la07lTE4/xi58r76PpyYjDbuF8hrsAROh0X1lcaqf8Iqkve7r1Ec5WPe/L5DsBFStmsfiG4Vkb3RB5g9JLmIN0qMswQSaH2zQ==' },
              ] },
            { modLen: 3072,
              jwkPrvKey: {"alg":"RSA-OAEP-256","d":"mzzPz0TQcDroMf1QbLwcVzCXEMSvIxuk97qAMFhiW_WTpmeKvIsVNSNMcBzcwNerOAPpM4bzGQxFpLamLDV4IEzlhib0t24MoRyly480KAdkhWvmdBLarzCh2Ic6aE-anAQ_ArMvVuCXoTDFnuc6_dZ-JzIhsg1Wc1fbkBY69shfoqelFvFV3as8cvc18kfd6Nf0LAL2CErik3KH6U2gEK-uk2Aczd5uT0aD47_R44f998qowPGChSWwdzn4GeJBxleQ3dfNgAGhaVKvQuOLv8J51jXvE0Taew1IQCH7AVsbmjvnDYRQGuBRV8enObLHw8rn39XWmDFpWIvaiQqoyU3J5hEzjmxQbsG_CyfiOejxtTs1si40vvoPrmqtjZAOOyqHPhbwj8dJj2iLNJ3DTtnFLM5tg1A3MJKp_A2gLBOi6PwtmqSULcB0GZ53HIWsDuEDsQUjckyNewR4zuTrhgFTllZxYiynPvzcX4sQsoJ8DkCJJGFTFqddWChwRUzB","dp":"6XMsJgXRKRilrOFwlQxq00CeWjzOQ8DSzZXIlEd5Yvpn134lVqP92tqHlRv-o7x2QfprljNxp4quqJuvFEd9MXUo1jxkGrsTk4wQZ1kF_YRalwAXiPl_lhs9I-2GbvlU2T033NE0J7UE5wPcFcu8gZXqGG2w3O9yOTO6yfrj7OGhc_qsnF3pF-nMHVxDv978KQ7M5s-U6Fubmb20EhnpgX3WeHwhrflfZgeXfD_wPNzf9mfHnGNk39gLVRPnBfKB","dq":"IB_IrB1SHuVDxz-MdOj6b06qe2UzX5rt_3rBGXQ7jxwGhsIk9mVEGs_ExmElPxscqKG5k3ej5pTu0Y0HSQ_gvACpfo2B7y9nVpM1ExlZfIwfCXH5JmSezDO9hJ389SuZJMSfGtBpXkcW8i5i61yUBib2LEGx-kT-6scA7gK8jqVEPHL63XZKLc9ZekSf3Vg4y1KB6mSfGEsTmLYqEoNwKVwxGJ16tWxSoEIBN1FSAK3pvotnL2HHfE9ECR8u_r2B","e":"AQAB","ext":true,"key_ops":["decrypt"],"kty":"RSA","n":"uUuAFGMt_JC59sGxLP7p-vgE2vgZ7DAeFGHM7kaaYwNKN8KI5E3HZepkVaWpxSRzqoqYIDpeRqmBF0k0omvytfRofeilFTCmIXuZIJO1BfAc2v2tb6LRivyCCJpJdd5QHm56eFJeP5PltjQfEclrjO-icMc5W-5SdH-_bukyJTjwyU4rpzfoRcGgHJbFdIGLT1YyKyIvjd2tuhFEV6TOvySsAUykq6uScFoOpp5FouOX-A7ZFbW7pCsIf9a-MU2ZQS4im09tzeBPqFpctg4Rr7IQs77xHSeGPvcuuR-RIk5_ny3y2NJC1K3l_Q9TQrvWj4iQ681w3ipXedv6kwPa9r860hE_HTIiR08FedmkhbtLANx8Kkk6rmKxYCTL7kcH6l_cPE4M_s3EarvID7S1l44NOpfJCorNWlNN-RV2oVMJAvGy0sr_x0Y_Ba42s6GLkvbviOqHkhvUgrPXI57hq2TnkSKCxGACoJA9tOg1I90hb3R2odIFXNkloYnBUsKj","p":"8bcXqDGpvBedZsqJqxYAYw4tqm5uyfZBSa1CLRq3b4OJx3dQ2CzdDQp-QFaI7dxiWw1isCRFOKw5J2L5uQml5IHJ8m6BQiHWYn1tcQZlTOtjxFBuTj2j6dj97QSSXS8gLBO91yOH3b1l1yM3PP18TPPAgtBo4EreQWIN9-8aC6uses6xJQ7GwOrrlHuapUj6mMOl4Q2zm4EuSmQ60n_aUbIvEjHN9DPS1CdVsan72HMLd55oEs-ZxLYk8FYuLetT","q":"xD7TbAC4tAGXTWUTYzU_ASvzQjxLtkVuZiaTeh0jA87htoTEEc6dr-nYcGRPa95M1QInEWawu61G-r8c-LnKcvTeMQm_ZWarkqx4zP3Rj4BfM8I1pvx_KRrEcm8kiadhj1B2WJEvgN63fMlVLyQQ3VO36mzFrbJUwVWc2bdT1s8BwlHhd1dsuKOWFnsu0WwBnJ5iDMFrZ4VXtu0qxz4bad0fnRQfwlDAutAeYpDtJfzP_72z5XzSteZtZXIpkTFx","qi":"2V8GBEvcw2ZGeHSUQIJgfhpjmoUUn-CxQi9ADDSzptjhGT9WQEm8KHw8WdnV9vPQ_QudDcLe-4XFGyMgIDot8ByMWpVd5kRBk1RZCQYV89CXQ7YEK4WQGzU_Kqe5k3I-LHHyE3vN7XuZkeRWgSa6b_MghrXU8YH-bYIKo3yDluYZz75kJj_C0ld0HzW5NZ-v69gskjB8faq7dG-9OmT7eHHS50j8E6XmNYsVlZAQShtNeh6xYgD6n8mfEeXeGgmR"},
              subvectors: [
                { text: "",
                  ciphertext: 'Lfao8rG4gi0wBxHAwqrjy7WbmLvBWsgt7MsHApwc7/uZwEt2Y6ZFRjPbjXKV7ZoMKM+h37jjlnp8ajk9GnNB2vuTYBzVH15NHkB/iNVm5MkA6Gxl0Ix6FwhrKzTnBcLEViO32jm5kNHWb57PQCyGC5swooWGVdHM+vyDlpeyiYgQ/wCteiQLuuXe9fdM3mcO/6u4FlpipohDuTslRcm2niOkv8VYhBio9Ytmcms+WkvymTMPzFsbsscDYhrjmYkHVGOKMizGWCrh99n9G1MkwkEJt6Aj0LzXn0N50PfBVaLxHt4lKHnw/QdoBHtF7Y5S2KeF1kBhimi3MrLr9ZEK903aivKwINUYFieEuasgDufPLIv10Pm4S4rXJBLeTUtNigTUmrd4sTA59ocp5bwlaoF6gBHxsOyjt0zM2OmmesNONMIQ/IL4smVotOdfHggsEUmws9JAehVZVRzb79T68uarYt9rnrbl99ssNdcZYv/RGSjh131TV/1/3jltEwrJ' },
                { text: "Hello World!",
                  ciphertext: 'uCHyQf7FMt7nPWocl2clkeORUD2SYwmcU86OLxLUddGKAffAwt7K7LamuS/zkHfNHSUdzifuVuHXwyW7YXq15rCEtZyWnqqr9lct9uThXbpUSwUp6xTtsaR46k+7S6jGDJ/hzIM+JwkPv1nif+oUheZwDbZBXwaWuQ9hFf5Z1R0pEBEzQqcpZbYXVTNW3o978OKkxgNhF2SkjqveBUSqjXxraUsGzNu7A4vE4Kd7xRla/QbFZHRcfzRPcRyaamf4mxuQQYTKnsEshZluWlHaoQHKLA4uPKfbiKB9jAbS0mc1pazGQcUNGZYro3jXhSTybxukJHZYHDVBgULFtxrEOumpfA8Fj4ufL+EWHRTCxZd32CB78tgpfjbXgFJFUh7Sm0djX2Xssob4T8IkyPODehukJnqXi4wYHvZlZlhcge5lStJmSlbckr39GeHobbkzNgopfHzsIUc397m+JNxp/XOGaFaso/kqos6Ncgm8HE3v0QzRF9ZPovoVU5q3TYUi' },
              ] },
            { modLen: 4096,
              jwkPrvKey: {"alg":"RSA-OAEP-256","d":"X19KyKfTZD1uahWCumyrWPFFGtRaHjBIVancgfXmlDlLejbGdjaOg1Z5pz-biIggrOZWcdHywCX0NxnLttc8uwIOu44sPk-ak5nzNbn11macmWQUCTIARo4-mFkI5T3RB3EZzRmxCDcu25tz6ooqYYyi2HOHAsrTkY1Y_qXh6bnITtEWq5VV_d5dYRX71ccSeKKQfKSegqDYecjiYBPrBYa2v-oPf0FWSXdl7hMsz_3fgUYZMxCJzYPTEJuRabuLJCQxSH6zb3Mg2iupZH8a5l-cNnkpsOXwIuZLcu7ZZlT6BF_PI6rgy7JTpEe8rAY_Tgfi_lxTn8PJOC-mDp5HHDcJkhnO6RfFo0h1TnA8aWBccFj1sGFvf230l2vl6bWWLEYe4CKwt6OUqbvuFkAzR2MG3bRJZOe4nAiJ1-PeHirNb9Kctbi3ru8E7fcgHp4evzD1tByxWglA176IJN6_hfHMCZXL_bBi1uJUBWmqLauE5lerquEhmw8mICW9BCois3DlnbUTxPvsUf21U7NGMdsW_gpk0RLQIsxLHeWryvE5_PfbmDG405OCSFidAWCNT16PjzfYstnEjeHHt0GmLMnpBwtG6WlPVActLmSC9CB7_d6UTVY6hcPb5y7VmAUeFvnLz13cW9_cDDSy-bbWN_YAqeCwQSqYf-wq_lpepAE","dp":"UrZ-DkqNRN2JtEjEFAwFthcNtq11LlVEfdQIUc5lcnFbhP2awu96Wh8HSGCgj5QbZ-B4yyMIYYoZhGaX_yURgVzv-ENZpEQqVXwG1LlWZlH33yyYBO53GhbWz4lnFp3ygPcqk3-ja1UyCFzyk1xndnM-ewaG7xg1jag1fBR54pUugzE8x3Xo6cf4E8pHBcWwHeOX9xjW-O7uxkac0RYquvGsKTpQEIMvpYIbRnuEsQ-7cT7cmzOoeUqOWTlathYJFj430cPzFEEEmYZz711jQJCxRpDf3dP8ME9UlIfpVZfzPkzmkVHW4r6UCDISrypBXwK57pOFfnZXGZsblzUJnQ","dq":"nGRRKT3AypqmUdeLABinNFc9CSEZulpKsNfxz01fafqURTA1STyOy0Eerj-7nzTR3NvnZXjI_s6rZbG-nja1DZE-D3meEIdwZ4G6XO03djvHFhuWOn0Cdw5iIjy_r98bVUiDOtNliUWaUZRAbziP_fEqKwvbyN5Zs3SIPpAHu_KlCZ-ht2aGMn3v7JSadj9x6dFNmcWTaukzgG0HQ3FGCc25CTawqkjTft1FbpiFV7EuSmWItI9wn67tnOd224IgeRBSI2Sz_uTc9yYiGWnrXUI1kUrkcY11ds0wa471RYS8uJGU9hIDDWefTUZazclr-gH-RO330yzbKXZ9MTOaAQ","e":"AQAB","ext":true,"key_ops":["decrypt"],"kty":"RSA","n":"osZ8eDUKrI6AXDBF-Yd2RhlqEDyfCM8_tvIxsYVLMjUGq5RZLqeYixOuMi7cGZkxKVWhldOW3VSZE-MjVPLUg4rBVfYr9uD1XHByxi-xESgiZqJEXsoU7JwQqGAQamTpPbqo6ZN2-BnqHIJtSGVQwOe3IBcJecg223cUgCESPpl_X70MVNsHj8JriFAI7pXph-I_KAdGSJIoZ76-_BapR9wwBM42-8kTVSB08jodNWLOTm0SmgCUQe68m5hN2cdZ3Dimph_M5JwWCTC5mxt7vV__7xNNNYaM5w_t7X6tySXSxJBftKbH4C4TqGuVHZmo1u78g8_uljoFet5185as67JexTWvzunGOMNSOUT-4FZAq-y0Jr5QfNhyGuN3j-jPTcJv0zT_EWbXpMOu1Z61wsbn5PlAH2-p0O3SZ6aBEU3VqIcoY7lix2e6ZZVSzfwCKsLxvElctwaKbFrwBrAJmIWjQhqrjztWGW7QKUFR_Uw6wV5mr7ijtDzgUX8PqEoFWVpsFKfE5TYygGFrxgLy_GzRyg-37-BItaM2EBN4yxuQtV6PT60GXMFhbNIMgssh70orjO9wERGiBM1cKyJSfC8SQXTRXSSDHLQOeVwgDqcHie-Gh01adkvyz7I-LLnjxHGJb76yM39PoYuYj5jZN1sQyEnEmQDvcqTGijhse50","p":"1VcBwDr0mV9TsVFSAo9jK8jJPR4iJxifOAiGa9oRzRu8j3ECvF4nxdHZhuprGePMqCIZ46WPd1M4srHACkmTUDuoj0MW7_LEbAeUZ51gGoWEoYeiRqbn1dMiXsTqWUGcWHoQ7tgZ-8t2L_e6SG4hQbUR_KUhspgKdOjt5dguvUNDRpL0xfGYpVKBD302WyWrgV9R2PoWKcAkACzYc7FpbX2twWCk27Y2B7HnuYrRRGJrWchsgp3-3GowTFOVPORLhrpue_FCKmrdqMJ3KtKeGV5khceVWfCGUzx4ViCkZtHqHod6v34nPRSSPSMe7uRJ4kd7lcg0VIyVoslf2SgOnQ","q":"w1MNHI3XPHF2HQvq7DNvxhLWkBrdpGY9vUsEQp5GdqNOxNMofV6ipt4QaJbvBuuGWvIi4QWQWyF33n_SLg99qhvTZL3dBWcv5Jy1wDSN4v9zRWqFaSZXOtSTkjTVvpntCKxkl_i-DePbsv01ogD9zB3lihqNraPYjPauLm0YywArsbEelYpL-YCaIyDoCloZUP2lStzyuQnvUrboPlTqHC17gulD3b1s6xMTqP6kuOlX-o37ipoXeLlLuZPjj_3zbcNEaAuWrb26c11pcalwE7ycZ9unMSgHb68q3fk7h_UBOdybuTJXyuUQVASHGVwvW9WxrvAv7tmi43Ldd7wRAQ","qi":"Di2Kfv6XKuzKZKOVInQWZINOE1e9RVRZkDyeC-Mu5xxL8vifycvKQcwDL-2MFyNu6HaAg070-zh7n2DEnY7yzvntUX6_Uwk741KmJeHCR6sjJe1h9K5OihXvjGqbXNqadiVebXTQBJRvbKz9DH1OFcyXWWf_JlB_fxqMbYwq_mdlPd12-wd1gPPlVsdfFFUvxUoaYoqynzJS_GAu0Pl5XKMRgXo7K2i6q8trxxf86MwEPey_MS8wUxc6oV6QdAzgM_VHk2OrBSQ1H6oWIBDwUlarXwDMA7zlYwVHdJ56MgeWvX5QGu6-ZXntcT-NZYY0Xv_2v3r7BSKXSq_fIpoHLA"},
              subvectors: [
                { text: "",
                  ciphertext: 'bx06qbSPTFt+zNZfBQVAZPDKq6E7stZZBRSP0LpUHzJGNK7uS5RtpaxwhnGMxW0/UixRpqRVEoX5DcGeI5hRb044fA84dey2zQAHnsm2QjTdkMBc6ntrk6cZjQ950nl+mEIUGBkefE7FGmaqKKLr0j/RTXmL5Yx1VWjnBf7P6h+9RYd+tpWrClz4idDv7Jy2ldAsju6yVHEZhc/9KnV9sKYi9RGrGEtrW/Yywv2wKpg90LTk+9eS/rl+35AFu+gR9hGAbi0et5Sn5cwiv0xVUt4G2iCkirLRJaR0oGJz5QoEHpaJVgOb2/SAD23W3yv7OPqOcybr2HFwVoG0IbeVwtmnWPW7omzmCEjBrvpHbRZBJpPlAhKtgJGSWXg0vgjOOSwvleczgTDhu6O1npbqFtlNi/XMpKoNsdnSEFNwx/os+40bQMOJCJ2tHFFSps2B1xtWQPPY9sh044YszPnz4/nOs/nGHrhJOxCGPoxsGH8ezAl4eb3LclZ1VUqNr5vFWpF7zGP1AiYCPnJXZFC2++bd9r7ov662XV6lfxv8CFxAF4MIRRsCaHCgZIF/P2LkU1zzvxjjoH/gqusY03nZqahmiuXMHvfJbOjUw62ch1RT4o70NvAQb6YCgnd6z9HdC1QjDemK0wX/H499UtlLk956Ofr4iWodEiEYJOHA7tE=' },
                { text: "Hello World!",
                  ciphertext: 'dzQ3NlKfoqKcX3E9GQWMjrh7lh2ef4Hu+4/yyxcfPF3rQMQWQkJon0lrASwLq2sFx75eP+bvMDbK6FykOyCAavqpFegYhyFO2t7vBpDhsx+w0LUMeW0OYOELiNUtF9Bs2JlLj/hW4ZDQWQHS1flfStsqmngNMrcbjJn2iTXONZRwuROpx4KRudNNdb9yYYgiHUswLwsd6RqyPsa5AqPuDEFjJSvH4jqdAYsmtCshsYUFogHDdYgC1a1Fm0PfxFoCNbcD9a++yhDcT+aK3UZl69c/M6Saz4iHZx7aMFTXlbpEdFDgM8D1SHphRG4Pxiu2MD1iQIavukXzQdAgXoLScA4eGp24P0G4lWGbQ0HILtc4dvmUINbOKlMXCw80kh7us2ifoqj9kefs/EHTAxym5EFk9hehcpajG5i89xSvAc1+oyT14zb1Zz57MFH7A4gOKolQ8mqd2a8fHkag4e+N9YYBm+bE3Cyu/ihf07+KGsMv/u4UwKO4TYufqfBO4I+DTj9fjKECTu72tDYVkK7xoTYw4kSpTUEDp78TYV8szGcVNJ0jr3o5YXM2sZA6L0zssorpR9TBxMvbXHrEXKVbnPBv8P30r526MVPNkiYTlPoRNlS2rAwbeiv5DA9jgU53X0geb7JLchOKLwLv03JofbsW0CIh5nVUldsNvpWU+Jw=' },
              ] },
        ];

        vectors.forEach( function ( v ) {
            describe( v.modLen + " bits", function ( done ) {
                v.subvectors.forEach( function ( sv ) {
                    it( "'" + sv.ciphertext + "'", function ( done ) {
                        crypto.subtle.importKey( "jwk", v.jwkPrvKey, alg, true, [ prvUse ] )
                            .then( function ( key ) {
                                return crypto.subtle.decrypt( alg, key, s2b( atob(sv.ciphertext) ) );
                            })
                            .then( function ( plaintext ) {
                                expect( b2s(plaintext) ).toBe(sv.text);
                            })
                            .catch(fail)
                            .then(done);
                    });
                });
            });
        });
    });
});

/*

var vectors = [
    { modLen: 1024,
      spkiPubKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDCOLweTVN8Q/9IcadFwyH0tSyXHilAYykuyxBIi8xcmsl+zPs5k2o9I6COF5/CV2ISA0d4OeTzuRhDotgct+pUwrIHGfatQ0xx+VUpRJCcW05lkhaeR6OL7c6msheEfsbrnNS9gb+uPbHXcm02sNhJDYDd4k4ha+sDlMEU5IOH0wIDAQAB',
      pkcsPrvKey: 'MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAMI4vB5NU3xD/0hxp0XDIfS1LJceKUBjKS7LEEiLzFyayX7M+zmTaj0joI4Xn8JXYhIDR3g55PO5GEOi2By36lTCsgcZ9q1DTHH5VSlEkJxbTmWSFp5Ho4vtzqayF4R+xuuc1L2Bv649sddybTaw2EkNgN3iTiFr6wOUwRTkg4fTAgMBAAECgYEArCMmr+Cu/rMxfh6lN5Jz9PPiemlT/GompP97BiFJVkYmEglRHD2Ianm6IlXT1aYnGHnpjSgawNyrIb8htYpX145yVoOHCePQQ57/wrT+tKuRq7f9hie0u2O6xshlOay4uUHPN8zL9yC2yz3SwoacnJvHquLFgTjCVu6ILaERm8kCQQDlP8qgiL/GNUHTuze/X8Hbgf2fljG3FOIbmLVJWhn/BJAfWzv9/1a5kEOWX7t9kD7mch5r8b7Dtmhu9qTAKdo/AkEA2OKWkdSTusHflqXU0P7C3+vBD4b8Z+Qsjck/QC7/QpIH84A99qAIMuHeqHuBIGU3GesKiPHkGEpTpeOzPzmlbQJARhtuEg3/59Odn+yfLc1Q8ZodP9KkvYKLazkWJ6qATLbOhGhYPmL52KG/qZr5MXsNYVgA6a3yUtPTuCuBUqr57QJBAKCi0pqYNAKy7YOKt6FDz9pBpvB1LiVUnps1Xx+Or4kC19jGNx6fUPM+z8dCElWIIdOUfm0Hm8VR57qKd4xwidECQQCsHOwYLvOO65MtfHYc7bW+0Qi7IxlVKj95ue1amMg6syETGUpPVqSRlgQMBRJmrPCeAwTfDZrUfJDwI7N7y2Bz',
      jwkPubKey: {"alg":"RSA-OAEP-256","e":"AQAB","ext":true,"key_ops":["encrypt"],"kty":"RSA","n":"wji8Hk1TfEP_SHGnRcMh9LUslx4pQGMpLssQSIvMXJrJfsz7OZNqPSOgjhefwldiEgNHeDnk87kYQ6LYHLfqVMKyBxn2rUNMcflVKUSQnFtOZZIWnkeji-3OprIXhH7G65zUvYG_rj2x13JtNrDYSQ2A3eJOIWvrA5TBFOSDh9M"},
      jwkPrvKey: {"alg":"RSA-OAEP-256","d":"rCMmr-Cu_rMxfh6lN5Jz9PPiemlT_GompP97BiFJVkYmEglRHD2Ianm6IlXT1aYnGHnpjSgawNyrIb8htYpX145yVoOHCePQQ57_wrT-tKuRq7f9hie0u2O6xshlOay4uUHPN8zL9yC2yz3SwoacnJvHquLFgTjCVu6ILaERm8k","dp":"RhtuEg3_59Odn-yfLc1Q8ZodP9KkvYKLazkWJ6qATLbOhGhYPmL52KG_qZr5MXsNYVgA6a3yUtPTuCuBUqr57Q","dq":"oKLSmpg0ArLtg4q3oUPP2kGm8HUuJVSemzVfH46viQLX2MY3Hp9Q8z7Px0ISVYgh05R-bQebxVHnuop3jHCJ0Q","e":"AQAB","ext":true,"key_ops":["decrypt"],"kty":"RSA","n":"wji8Hk1TfEP_SHGnRcMh9LUslx4pQGMpLssQSIvMXJrJfsz7OZNqPSOgjhefwldiEgNHeDnk87kYQ6LYHLfqVMKyBxn2rUNMcflVKUSQnFtOZZIWnkeji-3OprIXhH7G65zUvYG_rj2x13JtNrDYSQ2A3eJOIWvrA5TBFOSDh9M","p":"5T_KoIi_xjVB07s3v1_B24H9n5YxtxTiG5i1SVoZ_wSQH1s7_f9WuZBDll-7fZA-5nIea_G-w7ZobvakwCnaPw","q":"2OKWkdSTusHflqXU0P7C3-vBD4b8Z-Qsjck_QC7_QpIH84A99qAIMuHeqHuBIGU3GesKiPHkGEpTpeOzPzmlbQ","qi":"rBzsGC7zjuuTLXx2HO21vtEIuyMZVSo_ebntWpjIOrMhExlKT1akkZYEDAUSZqzwngME3w2a1HyQ8COze8tgcw"},
      subvectors: [
        { text: "",
          ciphertext: 'IvNeJAcxG6f+4A1kZ2uRA0hyi5Jm4RBrhvkLtYPVeUQZ/dEzS1QA/S8ar4KbdRuKuOVeAzTJ7NP86Nd+73yCZr1LY/NF4exPciBQLkms89DGQkWAmq1owvW5YKarJWxpxtcYlDJnylrKmKDV/BJcWj2urI9uK+qgc65gLgasHRg=' },
        { text: "Hello World!",
          ciphertext: 'YivS33H5siq8hxvOhFHd7KZl3+JwTJ8/gqM/6CnWYrFS9RKNlyNtQ4H5diiKlee0zt1pg2LJpZN9PUJ4IZBSBMxeVjGPQaG/DjFaYztFV9A6Io+84bu3LF3JeovIzCIPSOw6T9e0I+cycj8mobRSMSqNCFxAiO43dWwgEHzfZI4=' },
      ] },
    { modLen: 2048,
      spkiPubKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAviC6FqJnk6d6ycPAnIZ6vdqUxN9HGN9ApNYnul9h3P4vR0ApmiDDSQSRi4WVC0aOAtrEQ/lLRfs4ggsU46sYEDtRhW4WMfjEd3XXzKJuy4jYXgu3ODNlnnaXNzN1lHcmGhG3oZQTT628/MWwCiZEwTZIJXpAHkyMjQNhEYjLpdIMqhXEicpk38rB+WpemllcTJYf/cYu/k9LMTAm9PiP3ANQZyYrDCluyIN+wN8P35W/eLNonyZANLUdIdMMJPa9sbDLez0jmHdeJtpeGXn8juWPsI/S3yQDJtW+LqitRrmjGD4RvRMdfyd/WmQ98HeDd++GxAkRVpLqtx3pPNknoQIDAQAB',
      pkcsPrvKey: 'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC+ILoWomeTp3rJw8Cchnq92pTE30cY30Ck1ie6X2Hc/i9HQCmaIMNJBJGLhZULRo4C2sRD+UtF+ziCCxTjqxgQO1GFbhYx+MR3ddfMom7LiNheC7c4M2Wedpc3M3WUdyYaEbehlBNPrbz8xbAKJkTBNkglekAeTIyNA2ERiMul0gyqFcSJymTfysH5al6aWVxMlh/9xi7+T0sxMCb0+I/cA1BnJisMKW7Ig37A3w/flb94s2ifJkA0tR0h0wwk9r2xsMt7PSOYd14m2l4ZefyO5Y+wj9LfJAMm1b4uqK1GuaMYPhG9Ex1/J39aZD3wd4N374bECRFWkuq3Hek82SehAgMBAAECggEATMD3ftW3TLNq7XL6GfZsLKLGNigREqhl92WBCkshPc7blT8AzHj3fU272ABoZ/HmuJ5KZ0qHqcu+RzlkCHj0sPDRezUy/p936OYI5VKZuc8X0feW0rhlCLDFYQKEMBhdqF6IrFET7rGrvrur0p0aWomoXIDay6CJiQ/ZKvP3iovsaDEshaDuwT+jai9HPdDGckWCxx/DfbzgdzXwdN8+6NMfwn0apZb6DU/J0FZ8hjGGJCMfhrjTJnXvoq/VJt34ii0xj3ldPi+DVxDCAAaYmPpVX2YWvnpHVbz0/DQLt6+hJtUTSf9nBJ8MNCNSWNYmJ1ANHsJYNxZiKJGSIdcx9QKBgQDhPnIj6wVqso1srEfY32wOhtbQx0ls97OAt0v5Qtyosy+4UuiH8dZw7Vv7+1mbg6kYmO5h0Y3nPd3rcfXoGqIi6vutCTsiXI378/9pYXjl/bGdqHR9996dB3uhW7LyRk23Cw3hRt4hp0NYZZCvD4RnOutAD3acx9PBlwCkY6WfywKBgQDYFsY7Wi5WG31m2v03i+ugluzYNdVz24vkIuFO1BWngnIwYmBc/jCWQt00/njHuzRMywf/etUL+oRE6EVVmC48KX4/mUseuK1n+ra36B4cQE1IoB+Hmb8CemG4fcL/42PVni6cZc8aq8v0OQ6Yy4+LJphiPFKuNpKPrEjc0SNQwwKBgCJi2x/6oCAS1B5UCr/kE+X/1cmXsvDsu72ZvgJ2n6Mtf8p+9brTQ66HvfQxAhQIvIbhyfqq+CPmQPvRsP3XGwuDnhpjf2CWiqJ9NG/NDpzl5vivn+EfNx/35XrTufTcoL6h9GOA4yZ2F4TmNHlVQBxWVVW5Rp1WsFAskk+GWuGTAoGBAJCctS81S/s+TG8QMRQCZL9FId4UMPRnQjh8C1Ko5pEC4I5218yEJFn7B3UWtBfetcKKwaB9QKTSk8BVvUjqHk8O6bmASmCV8R68D5oGEliPw+jNmCw0fTsYUduY9m4vbDmiscji7XYI8OZRZO5mlHroamSbwC1swiq6fsygfcTnAoGBAJEpnjZHT7gvS8xLdpL8ckEmY6AaZfOTEJ6thqRROeB7ZSLhXyOm9IIQgiFD8vtlhUvee2Fc0Ypvoym3B5mFXpESja/uRr8cYS31gTZtSWX74xpWCer7ldR+JhxjmVcGgj+oIOyuAzlcgSKlhRrYxjVziY6FAfuu0xPSB4TfvgSC',
      jwkPubKey: {"alg":"RSA-OAEP-256","e":"AQAB","ext":true,"key_ops":["encrypt"],"kty":"RSA","n":"viC6FqJnk6d6ycPAnIZ6vdqUxN9HGN9ApNYnul9h3P4vR0ApmiDDSQSRi4WVC0aOAtrEQ_lLRfs4ggsU46sYEDtRhW4WMfjEd3XXzKJuy4jYXgu3ODNlnnaXNzN1lHcmGhG3oZQTT628_MWwCiZEwTZIJXpAHkyMjQNhEYjLpdIMqhXEicpk38rB-WpemllcTJYf_cYu_k9LMTAm9PiP3ANQZyYrDCluyIN-wN8P35W_eLNonyZANLUdIdMMJPa9sbDLez0jmHdeJtpeGXn8juWPsI_S3yQDJtW-LqitRrmjGD4RvRMdfyd_WmQ98HeDd--GxAkRVpLqtx3pPNknoQ"},
      jwkPrvKey: {"alg":"RSA-OAEP-256","d":"TMD3ftW3TLNq7XL6GfZsLKLGNigREqhl92WBCkshPc7blT8AzHj3fU272ABoZ_HmuJ5KZ0qHqcu-RzlkCHj0sPDRezUy_p936OYI5VKZuc8X0feW0rhlCLDFYQKEMBhdqF6IrFET7rGrvrur0p0aWomoXIDay6CJiQ_ZKvP3iovsaDEshaDuwT-jai9HPdDGckWCxx_DfbzgdzXwdN8-6NMfwn0apZb6DU_J0FZ8hjGGJCMfhrjTJnXvoq_VJt34ii0xj3ldPi-DVxDCAAaYmPpVX2YWvnpHVbz0_DQLt6-hJtUTSf9nBJ8MNCNSWNYmJ1ANHsJYNxZiKJGSIdcx9Q","dp":"ImLbH_qgIBLUHlQKv-QT5f_VyZey8Oy7vZm-Anafoy1_yn71utNDroe99DECFAi8huHJ-qr4I-ZA-9Gw_dcbC4OeGmN_YJaKon00b80OnOXm-K-f4R83H_fletO59NygvqH0Y4DjJnYXhOY0eVVAHFZVVblGnVawUCyST4Za4ZM","dq":"kJy1LzVL-z5MbxAxFAJkv0Uh3hQw9GdCOHwLUqjmkQLgjnbXzIQkWfsHdRa0F961worBoH1ApNKTwFW9SOoeTw7puYBKYJXxHrwPmgYSWI_D6M2YLDR9OxhR25j2bi9sOaKxyOLtdgjw5lFk7maUeuhqZJvALWzCKrp-zKB9xOc","e":"AQAB","ext":true,"key_ops":["decrypt"],"kty":"RSA","n":"viC6FqJnk6d6ycPAnIZ6vdqUxN9HGN9ApNYnul9h3P4vR0ApmiDDSQSRi4WVC0aOAtrEQ_lLRfs4ggsU46sYEDtRhW4WMfjEd3XXzKJuy4jYXgu3ODNlnnaXNzN1lHcmGhG3oZQTT628_MWwCiZEwTZIJXpAHkyMjQNhEYjLpdIMqhXEicpk38rB-WpemllcTJYf_cYu_k9LMTAm9PiP3ANQZyYrDCluyIN-wN8P35W_eLNonyZANLUdIdMMJPa9sbDLez0jmHdeJtpeGXn8juWPsI_S3yQDJtW-LqitRrmjGD4RvRMdfyd_WmQ98HeDd--GxAkRVpLqtx3pPNknoQ","p":"4T5yI-sFarKNbKxH2N9sDobW0MdJbPezgLdL-ULcqLMvuFLoh_HWcO1b-_tZm4OpGJjuYdGN5z3d63H16BqiIur7rQk7IlyN-_P_aWF45f2xnah0fffenQd7oVuy8kZNtwsN4UbeIadDWGWQrw-EZzrrQA92nMfTwZcApGOln8s","q":"2BbGO1ouVht9Ztr9N4vroJbs2DXVc9uL5CLhTtQVp4JyMGJgXP4wlkLdNP54x7s0TMsH_3rVC_qEROhFVZguPCl-P5lLHritZ_q2t-geHEBNSKAfh5m_AnphuH3C_-Nj1Z4unGXPGqvL9DkOmMuPiyaYYjxSrjaSj6xI3NEjUMM","qi":"kSmeNkdPuC9LzEt2kvxyQSZjoBpl85MQnq2GpFE54HtlIuFfI6b0ghCCIUPy-2WFS957YVzRim-jKbcHmYVekRKNr-5GvxxhLfWBNm1JZfvjGlYJ6vuV1H4mHGOZVwaCP6gg7K4DOVyBIqWFGtjGNXOJjoUB-67TE9IHhN--BII"},
      subvectors: [
        { text: "",
          ciphertext: 'W2ndc5Y0coQobSi5n3foHj2CZ74yrsYnpV/7YthrWnDm8yehKFPvj2mjlEYguUmQQdKLStcKwmdzgfItB+8EFBRyHzXOwezfer0hO0znctF6+vJ3ZYzcyvJH+01INQcx4/LMDaZOU8LTlI+Vjb2DUgSktGgoj872TLjYPo5d01X9L5a2UZq9b5kdC8Fsp1/UZ2QySVbhbxBssCoWF2Ra4CyQAFFq9e8XnKokYmYgaXQihpAa9dYVEl3kuF1TJL9yzmIRJ/dzHVrZITmXlyRCQhqtGE+WpXYhOrTJWoiPIChqof1PRzNsCFfEbjZ2omhnigdHuwvDJa6Z46JmLCHNow==' },
        { text: "Hello World!",
          ciphertext: 'Aih/ZciL0YYP/6yoJj/5c0fWY6FaiFYo/wwNjZ+JmxhVfF34PealEz98LnhWZMXLPEJlseNRZWOtJbof6L4iyEavhYQq8Yp7gG2Xr1D2+8VsLQBb5N3s7skuC8C+YPhm/IMFSFzneZf/DlwvSD4Ew+MhZy+NtRlHsyq9h+zoaQtBodYRjRf98NIHbRk88DelXh3bQ4yO/TysvLQWHoJ2CUGi4/VCXTgyOa/z6bqgaIQ/JitdGP8/+la07lTE4/xi58r76PpyYjDbuF8hrsAROh0X1lcaqf8Iqkve7r1Ec5WPe/L5DsBFStmsfiG4Vkb3RB5g9JLmIN0qMswQSaH2zQ==' },
      ] },
    { modLen: 3072,
      spkiPubKey: 'MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAuUuAFGMt/JC59sGxLP7p+vgE2vgZ7DAeFGHM7kaaYwNKN8KI5E3HZepkVaWpxSRzqoqYIDpeRqmBF0k0omvytfRofeilFTCmIXuZIJO1BfAc2v2tb6LRivyCCJpJdd5QHm56eFJeP5PltjQfEclrjO+icMc5W+5SdH+/bukyJTjwyU4rpzfoRcGgHJbFdIGLT1YyKyIvjd2tuhFEV6TOvySsAUykq6uScFoOpp5FouOX+A7ZFbW7pCsIf9a+MU2ZQS4im09tzeBPqFpctg4Rr7IQs77xHSeGPvcuuR+RIk5/ny3y2NJC1K3l/Q9TQrvWj4iQ681w3ipXedv6kwPa9r860hE/HTIiR08FedmkhbtLANx8Kkk6rmKxYCTL7kcH6l/cPE4M/s3EarvID7S1l44NOpfJCorNWlNN+RV2oVMJAvGy0sr/x0Y/Ba42s6GLkvbviOqHkhvUgrPXI57hq2TnkSKCxGACoJA9tOg1I90hb3R2odIFXNkloYnBUsKjAgMBAAE=',
      pkcsPrvKey: '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',
      jwkPubKey: {"alg":"RSA-OAEP-256","e":"AQAB","ext":true,"key_ops":["encrypt"],"kty":"RSA","n":"uUuAFGMt_JC59sGxLP7p-vgE2vgZ7DAeFGHM7kaaYwNKN8KI5E3HZepkVaWpxSRzqoqYIDpeRqmBF0k0omvytfRofeilFTCmIXuZIJO1BfAc2v2tb6LRivyCCJpJdd5QHm56eFJeP5PltjQfEclrjO-icMc5W-5SdH-_bukyJTjwyU4rpzfoRcGgHJbFdIGLT1YyKyIvjd2tuhFEV6TOvySsAUykq6uScFoOpp5FouOX-A7ZFbW7pCsIf9a-MU2ZQS4im09tzeBPqFpctg4Rr7IQs77xHSeGPvcuuR-RIk5_ny3y2NJC1K3l_Q9TQrvWj4iQ681w3ipXedv6kwPa9r860hE_HTIiR08FedmkhbtLANx8Kkk6rmKxYCTL7kcH6l_cPE4M_s3EarvID7S1l44NOpfJCorNWlNN-RV2oVMJAvGy0sr_x0Y_Ba42s6GLkvbviOqHkhvUgrPXI57hq2TnkSKCxGACoJA9tOg1I90hb3R2odIFXNkloYnBUsKj"},
      jwkPrvKey: {"alg":"RSA-OAEP-256","d":"mzzPz0TQcDroMf1QbLwcVzCXEMSvIxuk97qAMFhiW_WTpmeKvIsVNSNMcBzcwNerOAPpM4bzGQxFpLamLDV4IEzlhib0t24MoRyly480KAdkhWvmdBLarzCh2Ic6aE-anAQ_ArMvVuCXoTDFnuc6_dZ-JzIhsg1Wc1fbkBY69shfoqelFvFV3as8cvc18kfd6Nf0LAL2CErik3KH6U2gEK-uk2Aczd5uT0aD47_R44f998qowPGChSWwdzn4GeJBxleQ3dfNgAGhaVKvQuOLv8J51jXvE0Taew1IQCH7AVsbmjvnDYRQGuBRV8enObLHw8rn39XWmDFpWIvaiQqoyU3J5hEzjmxQbsG_CyfiOejxtTs1si40vvoPrmqtjZAOOyqHPhbwj8dJj2iLNJ3DTtnFLM5tg1A3MJKp_A2gLBOi6PwtmqSULcB0GZ53HIWsDuEDsQUjckyNewR4zuTrhgFTllZxYiynPvzcX4sQsoJ8DkCJJGFTFqddWChwRUzB","dp":"6XMsJgXRKRilrOFwlQxq00CeWjzOQ8DSzZXIlEd5Yvpn134lVqP92tqHlRv-o7x2QfprljNxp4quqJuvFEd9MXUo1jxkGrsTk4wQZ1kF_YRalwAXiPl_lhs9I-2GbvlU2T033NE0J7UE5wPcFcu8gZXqGG2w3O9yOTO6yfrj7OGhc_qsnF3pF-nMHVxDv978KQ7M5s-U6Fubmb20EhnpgX3WeHwhrflfZgeXfD_wPNzf9mfHnGNk39gLVRPnBfKB","dq":"IB_IrB1SHuVDxz-MdOj6b06qe2UzX5rt_3rBGXQ7jxwGhsIk9mVEGs_ExmElPxscqKG5k3ej5pTu0Y0HSQ_gvACpfo2B7y9nVpM1ExlZfIwfCXH5JmSezDO9hJ389SuZJMSfGtBpXkcW8i5i61yUBib2LEGx-kT-6scA7gK8jqVEPHL63XZKLc9ZekSf3Vg4y1KB6mSfGEsTmLYqEoNwKVwxGJ16tWxSoEIBN1FSAK3pvotnL2HHfE9ECR8u_r2B","e":"AQAB","ext":true,"key_ops":["decrypt"],"kty":"RSA","n":"uUuAFGMt_JC59sGxLP7p-vgE2vgZ7DAeFGHM7kaaYwNKN8KI5E3HZepkVaWpxSRzqoqYIDpeRqmBF0k0omvytfRofeilFTCmIXuZIJO1BfAc2v2tb6LRivyCCJpJdd5QHm56eFJeP5PltjQfEclrjO-icMc5W-5SdH-_bukyJTjwyU4rpzfoRcGgHJbFdIGLT1YyKyIvjd2tuhFEV6TOvySsAUykq6uScFoOpp5FouOX-A7ZFbW7pCsIf9a-MU2ZQS4im09tzeBPqFpctg4Rr7IQs77xHSeGPvcuuR-RIk5_ny3y2NJC1K3l_Q9TQrvWj4iQ681w3ipXedv6kwPa9r860hE_HTIiR08FedmkhbtLANx8Kkk6rmKxYCTL7kcH6l_cPE4M_s3EarvID7S1l44NOpfJCorNWlNN-RV2oVMJAvGy0sr_x0Y_Ba42s6GLkvbviOqHkhvUgrPXI57hq2TnkSKCxGACoJA9tOg1I90hb3R2odIFXNkloYnBUsKj","p":"8bcXqDGpvBedZsqJqxYAYw4tqm5uyfZBSa1CLRq3b4OJx3dQ2CzdDQp-QFaI7dxiWw1isCRFOKw5J2L5uQml5IHJ8m6BQiHWYn1tcQZlTOtjxFBuTj2j6dj97QSSXS8gLBO91yOH3b1l1yM3PP18TPPAgtBo4EreQWIN9-8aC6uses6xJQ7GwOrrlHuapUj6mMOl4Q2zm4EuSmQ60n_aUbIvEjHN9DPS1CdVsan72HMLd55oEs-ZxLYk8FYuLetT","q":"xD7TbAC4tAGXTWUTYzU_ASvzQjxLtkVuZiaTeh0jA87htoTEEc6dr-nYcGRPa95M1QInEWawu61G-r8c-LnKcvTeMQm_ZWarkqx4zP3Rj4BfM8I1pvx_KRrEcm8kiadhj1B2WJEvgN63fMlVLyQQ3VO36mzFrbJUwVWc2bdT1s8BwlHhd1dsuKOWFnsu0WwBnJ5iDMFrZ4VXtu0qxz4bad0fnRQfwlDAutAeYpDtJfzP_72z5XzSteZtZXIpkTFx","qi":"2V8GBEvcw2ZGeHSUQIJgfhpjmoUUn-CxQi9ADDSzptjhGT9WQEm8KHw8WdnV9vPQ_QudDcLe-4XFGyMgIDot8ByMWpVd5kRBk1RZCQYV89CXQ7YEK4WQGzU_Kqe5k3I-LHHyE3vN7XuZkeRWgSa6b_MghrXU8YH-bYIKo3yDluYZz75kJj_C0ld0HzW5NZ-v69gskjB8faq7dG-9OmT7eHHS50j8E6XmNYsVlZAQShtNeh6xYgD6n8mfEeXeGgmR"},
      subvectors: [
        { text: "",
          ciphertext: 'Lfao8rG4gi0wBxHAwqrjy7WbmLvBWsgt7MsHApwc7/uZwEt2Y6ZFRjPbjXKV7ZoMKM+h37jjlnp8ajk9GnNB2vuTYBzVH15NHkB/iNVm5MkA6Gxl0Ix6FwhrKzTnBcLEViO32jm5kNHWb57PQCyGC5swooWGVdHM+vyDlpeyiYgQ/wCteiQLuuXe9fdM3mcO/6u4FlpipohDuTslRcm2niOkv8VYhBio9Ytmcms+WkvymTMPzFsbsscDYhrjmYkHVGOKMizGWCrh99n9G1MkwkEJt6Aj0LzXn0N50PfBVaLxHt4lKHnw/QdoBHtF7Y5S2KeF1kBhimi3MrLr9ZEK903aivKwINUYFieEuasgDufPLIv10Pm4S4rXJBLeTUtNigTUmrd4sTA59ocp5bwlaoF6gBHxsOyjt0zM2OmmesNONMIQ/IL4smVotOdfHggsEUmws9JAehVZVRzb79T68uarYt9rnrbl99ssNdcZYv/RGSjh131TV/1/3jltEwrJ' },
        { text: "Hello World!",
          ciphertext: 'uCHyQf7FMt7nPWocl2clkeORUD2SYwmcU86OLxLUddGKAffAwt7K7LamuS/zkHfNHSUdzifuVuHXwyW7YXq15rCEtZyWnqqr9lct9uThXbpUSwUp6xTtsaR46k+7S6jGDJ/hzIM+JwkPv1nif+oUheZwDbZBXwaWuQ9hFf5Z1R0pEBEzQqcpZbYXVTNW3o978OKkxgNhF2SkjqveBUSqjXxraUsGzNu7A4vE4Kd7xRla/QbFZHRcfzRPcRyaamf4mxuQQYTKnsEshZluWlHaoQHKLA4uPKfbiKB9jAbS0mc1pazGQcUNGZYro3jXhSTybxukJHZYHDVBgULFtxrEOumpfA8Fj4ufL+EWHRTCxZd32CB78tgpfjbXgFJFUh7Sm0djX2Xssob4T8IkyPODehukJnqXi4wYHvZlZlhcge5lStJmSlbckr39GeHobbkzNgopfHzsIUc397m+JNxp/XOGaFaso/kqos6Ncgm8HE3v0QzRF9ZPovoVU5q3TYUi' },
      ] },
    { modLen: 4096,
      spkiPubKey: 'MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAosZ8eDUKrI6AXDBF+Yd2RhlqEDyfCM8/tvIxsYVLMjUGq5RZLqeYixOuMi7cGZkxKVWhldOW3VSZE+MjVPLUg4rBVfYr9uD1XHByxi+xESgiZqJEXsoU7JwQqGAQamTpPbqo6ZN2+BnqHIJtSGVQwOe3IBcJecg223cUgCESPpl/X70MVNsHj8JriFAI7pXph+I/KAdGSJIoZ76+/BapR9wwBM42+8kTVSB08jodNWLOTm0SmgCUQe68m5hN2cdZ3Dimph/M5JwWCTC5mxt7vV//7xNNNYaM5w/t7X6tySXSxJBftKbH4C4TqGuVHZmo1u78g8/uljoFet5185as67JexTWvzunGOMNSOUT+4FZAq+y0Jr5QfNhyGuN3j+jPTcJv0zT/EWbXpMOu1Z61wsbn5PlAH2+p0O3SZ6aBEU3VqIcoY7lix2e6ZZVSzfwCKsLxvElctwaKbFrwBrAJmIWjQhqrjztWGW7QKUFR/Uw6wV5mr7ijtDzgUX8PqEoFWVpsFKfE5TYygGFrxgLy/GzRyg+37+BItaM2EBN4yxuQtV6PT60GXMFhbNIMgssh70orjO9wERGiBM1cKyJSfC8SQXTRXSSDHLQOeVwgDqcHie+Gh01adkvyz7I+LLnjxHGJb76yM39PoYuYj5jZN1sQyEnEmQDvcqTGijhse50CAwEAAQ==',
      pkcsPrvKey: '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',
      jwkPubKey: {"alg":"RSA-OAEP-256","e":"AQAB","ext":true,"key_ops":["encrypt"],"kty":"RSA","n":"osZ8eDUKrI6AXDBF-Yd2RhlqEDyfCM8_tvIxsYVLMjUGq5RZLqeYixOuMi7cGZkxKVWhldOW3VSZE-MjVPLUg4rBVfYr9uD1XHByxi-xESgiZqJEXsoU7JwQqGAQamTpPbqo6ZN2-BnqHIJtSGVQwOe3IBcJecg223cUgCESPpl_X70MVNsHj8JriFAI7pXph-I_KAdGSJIoZ76-_BapR9wwBM42-8kTVSB08jodNWLOTm0SmgCUQe68m5hN2cdZ3Dimph_M5JwWCTC5mxt7vV__7xNNNYaM5w_t7X6tySXSxJBftKbH4C4TqGuVHZmo1u78g8_uljoFet5185as67JexTWvzunGOMNSOUT-4FZAq-y0Jr5QfNhyGuN3j-jPTcJv0zT_EWbXpMOu1Z61wsbn5PlAH2-p0O3SZ6aBEU3VqIcoY7lix2e6ZZVSzfwCKsLxvElctwaKbFrwBrAJmIWjQhqrjztWGW7QKUFR_Uw6wV5mr7ijtDzgUX8PqEoFWVpsFKfE5TYygGFrxgLy_GzRyg-37-BItaM2EBN4yxuQtV6PT60GXMFhbNIMgssh70orjO9wERGiBM1cKyJSfC8SQXTRXSSDHLQOeVwgDqcHie-Gh01adkvyz7I-LLnjxHGJb76yM39PoYuYj5jZN1sQyEnEmQDvcqTGijhse50"},
      jwkPrvKey: {"alg":"RSA-OAEP-256","d":"X19KyKfTZD1uahWCumyrWPFFGtRaHjBIVancgfXmlDlLejbGdjaOg1Z5pz-biIggrOZWcdHywCX0NxnLttc8uwIOu44sPk-ak5nzNbn11macmWQUCTIARo4-mFkI5T3RB3EZzRmxCDcu25tz6ooqYYyi2HOHAsrTkY1Y_qXh6bnITtEWq5VV_d5dYRX71ccSeKKQfKSegqDYecjiYBPrBYa2v-oPf0FWSXdl7hMsz_3fgUYZMxCJzYPTEJuRabuLJCQxSH6zb3Mg2iupZH8a5l-cNnkpsOXwIuZLcu7ZZlT6BF_PI6rgy7JTpEe8rAY_Tgfi_lxTn8PJOC-mDp5HHDcJkhnO6RfFo0h1TnA8aWBccFj1sGFvf230l2vl6bWWLEYe4CKwt6OUqbvuFkAzR2MG3bRJZOe4nAiJ1-PeHirNb9Kctbi3ru8E7fcgHp4evzD1tByxWglA176IJN6_hfHMCZXL_bBi1uJUBWmqLauE5lerquEhmw8mICW9BCois3DlnbUTxPvsUf21U7NGMdsW_gpk0RLQIsxLHeWryvE5_PfbmDG405OCSFidAWCNT16PjzfYstnEjeHHt0GmLMnpBwtG6WlPVActLmSC9CB7_d6UTVY6hcPb5y7VmAUeFvnLz13cW9_cDDSy-bbWN_YAqeCwQSqYf-wq_lpepAE","dp":"UrZ-DkqNRN2JtEjEFAwFthcNtq11LlVEfdQIUc5lcnFbhP2awu96Wh8HSGCgj5QbZ-B4yyMIYYoZhGaX_yURgVzv-ENZpEQqVXwG1LlWZlH33yyYBO53GhbWz4lnFp3ygPcqk3-ja1UyCFzyk1xndnM-ewaG7xg1jag1fBR54pUugzE8x3Xo6cf4E8pHBcWwHeOX9xjW-O7uxkac0RYquvGsKTpQEIMvpYIbRnuEsQ-7cT7cmzOoeUqOWTlathYJFj430cPzFEEEmYZz711jQJCxRpDf3dP8ME9UlIfpVZfzPkzmkVHW4r6UCDISrypBXwK57pOFfnZXGZsblzUJnQ","dq":"nGRRKT3AypqmUdeLABinNFc9CSEZulpKsNfxz01fafqURTA1STyOy0Eerj-7nzTR3NvnZXjI_s6rZbG-nja1DZE-D3meEIdwZ4G6XO03djvHFhuWOn0Cdw5iIjy_r98bVUiDOtNliUWaUZRAbziP_fEqKwvbyN5Zs3SIPpAHu_KlCZ-ht2aGMn3v7JSadj9x6dFNmcWTaukzgG0HQ3FGCc25CTawqkjTft1FbpiFV7EuSmWItI9wn67tnOd224IgeRBSI2Sz_uTc9yYiGWnrXUI1kUrkcY11ds0wa471RYS8uJGU9hIDDWefTUZazclr-gH-RO330yzbKXZ9MTOaAQ","e":"AQAB","ext":true,"key_ops":["decrypt"],"kty":"RSA","n":"osZ8eDUKrI6AXDBF-Yd2RhlqEDyfCM8_tvIxsYVLMjUGq5RZLqeYixOuMi7cGZkxKVWhldOW3VSZE-MjVPLUg4rBVfYr9uD1XHByxi-xESgiZqJEXsoU7JwQqGAQamTpPbqo6ZN2-BnqHIJtSGVQwOe3IBcJecg223cUgCESPpl_X70MVNsHj8JriFAI7pXph-I_KAdGSJIoZ76-_BapR9wwBM42-8kTVSB08jodNWLOTm0SmgCUQe68m5hN2cdZ3Dimph_M5JwWCTC5mxt7vV__7xNNNYaM5w_t7X6tySXSxJBftKbH4C4TqGuVHZmo1u78g8_uljoFet5185as67JexTWvzunGOMNSOUT-4FZAq-y0Jr5QfNhyGuN3j-jPTcJv0zT_EWbXpMOu1Z61wsbn5PlAH2-p0O3SZ6aBEU3VqIcoY7lix2e6ZZVSzfwCKsLxvElctwaKbFrwBrAJmIWjQhqrjztWGW7QKUFR_Uw6wV5mr7ijtDzgUX8PqEoFWVpsFKfE5TYygGFrxgLy_GzRyg-37-BItaM2EBN4yxuQtV6PT60GXMFhbNIMgssh70orjO9wERGiBM1cKyJSfC8SQXTRXSSDHLQOeVwgDqcHie-Gh01adkvyz7I-LLnjxHGJb76yM39PoYuYj5jZN1sQyEnEmQDvcqTGijhse50","p":"1VcBwDr0mV9TsVFSAo9jK8jJPR4iJxifOAiGa9oRzRu8j3ECvF4nxdHZhuprGePMqCIZ46WPd1M4srHACkmTUDuoj0MW7_LEbAeUZ51gGoWEoYeiRqbn1dMiXsTqWUGcWHoQ7tgZ-8t2L_e6SG4hQbUR_KUhspgKdOjt5dguvUNDRpL0xfGYpVKBD302WyWrgV9R2PoWKcAkACzYc7FpbX2twWCk27Y2B7HnuYrRRGJrWchsgp3-3GowTFOVPORLhrpue_FCKmrdqMJ3KtKeGV5khceVWfCGUzx4ViCkZtHqHod6v34nPRSSPSMe7uRJ4kd7lcg0VIyVoslf2SgOnQ","q":"w1MNHI3XPHF2HQvq7DNvxhLWkBrdpGY9vUsEQp5GdqNOxNMofV6ipt4QaJbvBuuGWvIi4QWQWyF33n_SLg99qhvTZL3dBWcv5Jy1wDSN4v9zRWqFaSZXOtSTkjTVvpntCKxkl_i-DePbsv01ogD9zB3lihqNraPYjPauLm0YywArsbEelYpL-YCaIyDoCloZUP2lStzyuQnvUrboPlTqHC17gulD3b1s6xMTqP6kuOlX-o37ipoXeLlLuZPjj_3zbcNEaAuWrb26c11pcalwE7ycZ9unMSgHb68q3fk7h_UBOdybuTJXyuUQVASHGVwvW9WxrvAv7tmi43Ldd7wRAQ","qi":"Di2Kfv6XKuzKZKOVInQWZINOE1e9RVRZkDyeC-Mu5xxL8vifycvKQcwDL-2MFyNu6HaAg070-zh7n2DEnY7yzvntUX6_Uwk741KmJeHCR6sjJe1h9K5OihXvjGqbXNqadiVebXTQBJRvbKz9DH1OFcyXWWf_JlB_fxqMbYwq_mdlPd12-wd1gPPlVsdfFFUvxUoaYoqynzJS_GAu0Pl5XKMRgXo7K2i6q8trxxf86MwEPey_MS8wUxc6oV6QdAzgM_VHk2OrBSQ1H6oWIBDwUlarXwDMA7zlYwVHdJ56MgeWvX5QGu6-ZXntcT-NZYY0Xv_2v3r7BSKXSq_fIpoHLA"},
      subvectors: [
        { text: "",
          ciphertext: 'bx06qbSPTFt+zNZfBQVAZPDKq6E7stZZBRSP0LpUHzJGNK7uS5RtpaxwhnGMxW0/UixRpqRVEoX5DcGeI5hRb044fA84dey2zQAHnsm2QjTdkMBc6ntrk6cZjQ950nl+mEIUGBkefE7FGmaqKKLr0j/RTXmL5Yx1VWjnBf7P6h+9RYd+tpWrClz4idDv7Jy2ldAsju6yVHEZhc/9KnV9sKYi9RGrGEtrW/Yywv2wKpg90LTk+9eS/rl+35AFu+gR9hGAbi0et5Sn5cwiv0xVUt4G2iCkirLRJaR0oGJz5QoEHpaJVgOb2/SAD23W3yv7OPqOcybr2HFwVoG0IbeVwtmnWPW7omzmCEjBrvpHbRZBJpPlAhKtgJGSWXg0vgjOOSwvleczgTDhu6O1npbqFtlNi/XMpKoNsdnSEFNwx/os+40bQMOJCJ2tHFFSps2B1xtWQPPY9sh044YszPnz4/nOs/nGHrhJOxCGPoxsGH8ezAl4eb3LclZ1VUqNr5vFWpF7zGP1AiYCPnJXZFC2++bd9r7ov662XV6lfxv8CFxAF4MIRRsCaHCgZIF/P2LkU1zzvxjjoH/gqusY03nZqahmiuXMHvfJbOjUw62ch1RT4o70NvAQb6YCgnd6z9HdC1QjDemK0wX/H499UtlLk956Ofr4iWodEiEYJOHA7tE=' },
        { text: "Hello World!",
          ciphertext: 'dzQ3NlKfoqKcX3E9GQWMjrh7lh2ef4Hu+4/yyxcfPF3rQMQWQkJon0lrASwLq2sFx75eP+bvMDbK6FykOyCAavqpFegYhyFO2t7vBpDhsx+w0LUMeW0OYOELiNUtF9Bs2JlLj/hW4ZDQWQHS1flfStsqmngNMrcbjJn2iTXONZRwuROpx4KRudNNdb9yYYgiHUswLwsd6RqyPsa5AqPuDEFjJSvH4jqdAYsmtCshsYUFogHDdYgC1a1Fm0PfxFoCNbcD9a++yhDcT+aK3UZl69c/M6Saz4iHZx7aMFTXlbpEdFDgM8D1SHphRG4Pxiu2MD1iQIavukXzQdAgXoLScA4eGp24P0G4lWGbQ0HILtc4dvmUINbOKlMXCw80kh7us2ifoqj9kefs/EHTAxym5EFk9hehcpajG5i89xSvAc1+oyT14zb1Zz57MFH7A4gOKolQ8mqd2a8fHkag4e+N9YYBm+bE3Cyu/ihf07+KGsMv/u4UwKO4TYufqfBO4I+DTj9fjKECTu72tDYVkK7xoTYw4kSpTUEDp78TYV8szGcVNJ0jr3o5YXM2sZA6L0zssorpR9TBxMvbXHrEXKVbnPBv8P30r526MVPNkiYTlPoRNlS2rAwbeiv5DA9jgU53X0geb7JLchOKLwLv03JofbsW0CIh5nVUldsNvpWU+Jw=' },
      ] },
];

*/
