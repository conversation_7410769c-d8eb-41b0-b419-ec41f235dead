"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1470],{68334:function(Q,B,s){s.r(B),s.d(B,{default:function(){return W}});var h=s(15009),S=s.n(h),G=s(13769),R=s.n(G),U=s(99289),T=s.n(U),K=s(5574),F=s.n(K),Z=s(84226);function J(m){return a.apply(this,arguments)}function a(){return a=T()(S()().mark(function m(o){return S()().wrap(function(v){for(;;)switch(v.prev=v.next){case 0:return v.abrupt("return",(0,Z.request)("/api/user/register",{method:"POST",data:o}));case 1:case"end":return v.stop()}},m)})),a.apply(this,arguments)}var I=s(10915),C=s(47019),d=s(34041),y=s(9361),r=s(2453),j=s(38703),i=s(55102),u=s(55241),f=s(78957),g=s(83622),p=s(67294),O=s(28846),w=(0,O.kc)(function(m){var o=m.token;return{main:{width:"368px",margin:"auto",paddingTop:"10%",h3:{marginBottom:"20px",fontSize:"16px"}},password:{marginBottom:"24px",".ant-form-item-explain":{display:"none"}},getCaptcha:{display:"block",width:"100%"},lang:{width:42,height:42,lineHeight:"42px",position:"fixed",right:16,borderRadius:o.borderRadius,":hover":{backgroundColor:o.colorBgTextHover}},footer:{width:"100%",display:"flex",alignItems:"center",justifyContent:"space-between"},submit:{width:"50%"},success:{transition:"color 0.3s",color:o.colorSuccess},warning:{transition:"color 0.3s",color:o.colorWarning},error:{transition:"color 0.3s",color:o.colorError},"progress-pass > .progress":{".ant-progress-bg":{backgroundColor:o.colorWarning}}}}),D=w,e=s(85893),V=["confirm"],t=C.Z.Item,A=d.Z.Option,N={ok:"success",pass:"normal",poor:"exception"},W=function(){var m=D(),o=m.styles,z=(0,Z.useIntl)(),v=y.Z.useToken(),c=v.token,l=C.Z.useForm(),P=F()(l,1),E=P[0],H=(0,p.useState)(0),L=F()(H,2),k=L[0],Ee=L[1],oe=(0,p.useState)(!1),q=F()(oe,2),X=q[0],_=q[1],ue=(0,p.useState)("86"),ee=F()(ue,2),le=ee[0],ie=ee[1],ce=(0,p.useState)(!1),se=F()(ce,2),de=se[0],fe=se[1],pe=!1,re,he=(0,p.useState)(!0),te=F()(he,2),Fe=te[0],ge=te[1],me={ok:(0,e.jsx)("div",{className:o.success,children:(0,e.jsx)("span",{children:"\u5F3A\u5EA6\uFF1A\u5F3A"})}),pass:(0,e.jsx)("div",{className:o.warning,children:(0,e.jsx)("span",{children:"\u5F3A\u5EA6\uFF1A\u4E2D"})}),poor:(0,e.jsx)("div",{className:o.error,children:(0,e.jsx)("span",{children:"\u5F3A\u5EA6\uFF1A\u592A\u77ED"})})};(0,p.useEffect)(function(){return function(){clearInterval(re)}},[re]);var ne=function(){var n=E.getFieldValue("password");return n&&n.length>9?"ok":n&&n.length>5?"pass":"poor"},ve=function(){var M=T()(S()().mark(function n(x){var b,ae,Y;return S()().wrap(function($){for(;;)switch($.prev=$.next){case 0:return $.prev=0,b=x.confirm,ae=R()(x,V),$.next=4,J(ae);case 4:Y=$.sent,console.log("Register response:",Y.data),Y.success===!0?(r.ZP.success("\u6CE8\u518C\u6210\u529F\uFF01"),Z.history.push({pathname:"/user/register-result?account=".concat(x.email)})):r.ZP.error(Y.message||"\u6CE8\u518C\u5931\u8D25,\u8BF7\u91CD\u8BD5\uFF01"),$.next=13;break;case 9:$.prev=9,$.t0=$.catch(0),r.ZP.error("\u6CE8\u518C\u5931\u8D25,\u8BF7\u91CD\u8BD5\uFF01"),console.error("\u6CE8\u518C\u5931\u8D25:",$.t0);case 13:case"end":return $.stop()}},n,null,[[0,9]])}));return function(x){return M.apply(this,arguments)}}(),xe=function(n,x){var b=Promise;return x&&x!==E.getFieldValue("password")?b.reject("\u4E24\u6B21\u8F93\u5165\u7684\u5BC6\u7801\u4E0D\u5339\u914D!"):b.resolve()},ye=function(n,x){var b=Promise;return x?(X||_(!!x),fe(!de),x.length<6?b.reject(""):(x&&pe&&E.validateFields(["confirm"]),b.resolve())):(_(!!x),b.reject("\u8BF7\u8F93\u5165\u5BC6\u7801!"))},je=function(n){ie(n)},Ce=function(){var n=E.getFieldValue("password"),x=ne();return n&&n.length?(0,e.jsx)("div",{children:(0,e.jsx)(j.Z,{status:N[x],strokeWidth:6,percent:n.length*10>100?100:n.length*10,showInfo:!1})}):null},Oe=function(n){ge(n==="admin"),E.setFieldsValue({access:n})};return(0,e.jsx)(I._Y,{hashed:!1,children:(0,e.jsxs)("div",{className:o.main,children:[(0,e.jsxs)("h3",{children:[" ",z.formatMessage({id:"pages.login.registerAccount",defaultMessage:"\u6CE8\u518C\u8D26\u6237"})]}),(0,e.jsxs)(C.Z,{form:E,name:"UserRegister",onFinish:ve,children:[(0,e.jsx)(t,{name:"username",rules:[{type:"string",message:"\u8BF7\u4EE5\u5B57\u6BCD\u5F00\u5934\uFF01"},{required:!0,message:"\u8BF7\u8F93\u5165\u7528\u6237\u540D\uFF01"},{}],children:(0,e.jsx)(i.Z,{placeholder:"\u7528\u6237\u540D"})}),(0,e.jsx)(t,{name:"email",rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u90AE\u7BB1\u5730\u5740!"},{type:"email",message:"\u90AE\u7BB1\u5730\u5740\u683C\u5F0F\u9519\u8BEF!"}],children:(0,e.jsx)(i.Z,{size:"large",placeholder:"\u90AE\u7BB1"})}),(0,e.jsx)(u.Z,{getPopupContainer:function(n){return n&&n.parentNode?n.parentNode:n},content:X&&(0,e.jsxs)("div",{style:{padding:"4px 0"},children:[me[ne()],Ce(),(0,e.jsx)("div",{style:{marginTop:10},children:(0,e.jsx)("span",{children:"\u8BF7\u81F3\u5C11\u8F93\u5165 6 \u4E2A\u5B57\u7B26\u3002\u8BF7\u4E0D\u8981\u4F7F\u7528\u5BB9\u6613\u88AB\u731C\u5230\u7684\u5BC6\u7801\u3002"})})]}),overlayStyle:{width:240},placement:"right",open:X,children:(0,e.jsx)(t,{name:"password",className:E.getFieldValue("password")&&E.getFieldValue("password").length>0&&o.password,rules:[{validator:ye}],children:(0,e.jsx)(i.Z,{size:"large",type:"password",placeholder:"\u81F3\u5C116\u4F4D\u5BC6\u7801\uFF0C\u533A\u5206\u5927\u5C0F\u5199"})})}),(0,e.jsx)(t,{name:"confirm",rules:[{required:!0,message:"\u786E\u8BA4\u5BC6\u7801"},{validator:xe}],children:(0,e.jsx)(i.Z,{size:"large",type:"password",placeholder:"\u786E\u8BA4\u5BC6\u7801"})}),(0,e.jsx)(t,{name:"phone",rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7!"},{pattern:/^\d{11}$/,message:"\u624B\u673A\u53F7\u683C\u5F0F\u9519\u8BEF!"}],children:(0,e.jsxs)(f.Z.Compact,{style:{width:"100%"},children:[(0,e.jsxs)(d.Z,{size:"large",value:le,onChange:je,style:{width:"30%"},children:[(0,e.jsx)(A,{value:"86",children:"+86"}),(0,e.jsx)(A,{value:"87",children:"+87"})]}),(0,e.jsx)(i.Z,{size:"large",placeholder:"\u624B\u673A\u53F7"})]})}),(0,e.jsx)(t,{label:"\u7528\u6237\u8EAB\u4EFD\uFF1A",name:"access",rules:[{required:!0,message:"\u8BF7\u9009\u62E9\u662F\u5426\u4E3A\u7BA1\u7406\u5458\uFF01"}],children:(0,e.jsxs)(d.Z,{size:"large",onChange:Oe,defaultValue:"admin",children:[(0,e.jsx)(A,{value:"admin",children:"\u7BA1\u7406\u5458"}),(0,e.jsx)(A,{value:"user",children:"\u666E\u901A\u7528\u6237"})]})}),(0,e.jsx)(t,{children:(0,e.jsxs)("div",{className:o.footer,children:[(0,e.jsx)(g.ZP,{size:"large",className:o.submit,type:"primary",htmlType:"submit",children:(0,e.jsx)("span",{children:"\u6CE8\u518C"})}),(0,e.jsx)(Z.Link,{to:"/user/login",children:(0,e.jsx)("span",{children:"\u4F7F\u7528\u5DF2\u6709\u8D26\u6237\u767B\u5F55"})})]})})]})]})})}},99134:function(Q,B,s){var h=s(67294);const S=(0,h.createContext)({});B.Z=S},21584:function(Q,B,s){var h=s(67294),S=s(93967),G=s.n(S),R=s(53124),U=s(99134),T=s(6999),K=function(a,I){var C={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&I.indexOf(d)<0&&(C[d]=a[d]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var y=0,d=Object.getOwnPropertySymbols(a);y<d.length;y++)I.indexOf(d[y])<0&&Object.prototype.propertyIsEnumerable.call(a,d[y])&&(C[d[y]]=a[d[y]]);return C};function F(a){return typeof a=="number"?`${a} ${a} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(a)?`0 0 ${a}`:a}const Z=["xs","sm","md","lg","xl","xxl"],J=h.forwardRef((a,I)=>{const{getPrefixCls:C,direction:d}=h.useContext(R.E_),{gutter:y,wrap:r}=h.useContext(U.Z),{prefixCls:j,span:i,order:u,offset:f,push:g,pull:p,className:O,children:w,flex:D,style:e}=a,V=K(a,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),t=C("col",j),[A,N,W]=(0,T.cG)(t),m={};let o={};Z.forEach(c=>{let l={};const P=a[c];typeof P=="number"?l.span=P:typeof P=="object"&&(l=P||{}),delete V[c],o=Object.assign(Object.assign({},o),{[`${t}-${c}-${l.span}`]:l.span!==void 0,[`${t}-${c}-order-${l.order}`]:l.order||l.order===0,[`${t}-${c}-offset-${l.offset}`]:l.offset||l.offset===0,[`${t}-${c}-push-${l.push}`]:l.push||l.push===0,[`${t}-${c}-pull-${l.pull}`]:l.pull||l.pull===0,[`${t}-rtl`]:d==="rtl"}),l.flex&&(o[`${t}-${c}-flex`]=!0,m[`--${t}-${c}-flex`]=F(l.flex))});const z=G()(t,{[`${t}-${i}`]:i!==void 0,[`${t}-order-${u}`]:u,[`${t}-offset-${f}`]:f,[`${t}-push-${g}`]:g,[`${t}-pull-${p}`]:p},O,o,N,W),v={};if(y&&y[0]>0){const c=y[0]/2;v.paddingLeft=c,v.paddingRight=c}return D&&(v.flex=F(D),r===!1&&!v.minWidth&&(v.minWidth=0)),A(h.createElement("div",Object.assign({},V,{style:Object.assign(Object.assign(Object.assign({},v),e),m),className:z,ref:I}),w))});B.Z=J},17621:function(Q,B,s){s.d(B,{Z:function(){return y}});var h=s(67294),S=s(93967),G=s.n(S),R=s(74443),U=s(53124),T=s(25378);function K(r,j){const i=[void 0,void 0],u=Array.isArray(r)?r:[r,void 0],f=j||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return u.forEach((g,p)=>{if(typeof g=="object"&&g!==null)for(let O=0;O<R.c4.length;O++){const w=R.c4[O];if(f[w]&&g[w]!==void 0){i[p]=g[w];break}}else i[p]=g}),i}var F=s(99134),Z=s(6999),J=function(r,j){var i={};for(var u in r)Object.prototype.hasOwnProperty.call(r,u)&&j.indexOf(u)<0&&(i[u]=r[u]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var f=0,u=Object.getOwnPropertySymbols(r);f<u.length;f++)j.indexOf(u[f])<0&&Object.prototype.propertyIsEnumerable.call(r,u[f])&&(i[u[f]]=r[u[f]]);return i};const a=null,I=null;function C(r,j){const[i,u]=h.useState(typeof r=="string"?r:""),f=()=>{if(typeof r=="string"&&u(r),typeof r=="object")for(let g=0;g<R.c4.length;g++){const p=R.c4[g];if(!j||!j[p])continue;const O=r[p];if(O!==void 0){u(O);return}}};return h.useEffect(()=>{f()},[JSON.stringify(r),j]),i}var y=h.forwardRef((r,j)=>{const{prefixCls:i,justify:u,align:f,className:g,style:p,children:O,gutter:w=0,wrap:D}=r,e=J(r,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:V,direction:t}=h.useContext(U.E_),A=(0,T.Z)(!0,null),N=C(f,A),W=C(u,A),m=V("row",i),[o,z,v]=(0,Z.VM)(m),c=K(w,A),l=G()(m,{[`${m}-no-wrap`]:D===!1,[`${m}-${W}`]:W,[`${m}-${N}`]:N,[`${m}-rtl`]:t==="rtl"},g,z,v),P={},E=c[0]!=null&&c[0]>0?c[0]/-2:void 0;E&&(P.marginLeft=E,P.marginRight=E);const[H,L]=c;P.rowGap=L;const k=h.useMemo(()=>({gutter:[H,L],wrap:D}),[H,L,D]);return o(h.createElement(F.Z.Provider,{value:k},h.createElement("div",Object.assign({},e,{className:l,style:Object.assign(Object.assign({},P),p),ref:j}),O)))})}}]);
