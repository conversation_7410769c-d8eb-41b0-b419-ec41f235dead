"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8340],{83832:function(oe,x,e){e.d(x,{S:function(){return j}});var r=e(1413),p=e(91),O=e(74330),N=e(67294),T=e(85893),K=["isLoading","pastDelay","timedOut","error","retry"],j=function(D){var Z=D.isLoading,u=D.pastDelay,t=D.timedOut,a=D.error,n=D.retry,i=(0,p.Z)(D,K);return(0,T.jsx)("div",{style:{paddingBlockStart:100,textAlign:"center"},children:(0,T.jsx)(O.Z,(0,r.Z)({size:"large"},i))})}},76509:function(oe,x,e){e.d(x,{X:function(){return p}});var r=e(67294),p=(0,r.createContext)({})},87646:function(oe,x,e){e.r(x),e.d(x,{blue:function(){return R},blueDark:function(){return z},cyan:function(){return G},cyanDark:function(){return S},geekblue:function(){return Q},geekblueDark:function(){return _},generate:function(){return a},gold:function(){return C},goldDark:function(){return d},gray:function(){return pe},green:function(){return k},greenDark:function(){return g},grey:function(){return te},greyDark:function(){return L},lime:function(){return h},limeDark:function(){return c},magenta:function(){return B},magentaDark:function(){return F},orange:function(){return E},orangeDark:function(){return s},presetDarkPalettes:function(){return ie},presetPalettes:function(){return le},presetPrimaryColors:function(){return n},purple:function(){return Y},purpleDark:function(){return w},red:function(){return i},redDark:function(){return se},volcano:function(){return o},volcanoDark:function(){return H},yellow:function(){return v},yellowDark:function(){return f}});var r=e(15063),p=2,O=.16,N=.05,T=.05,K=.15,j=5,U=4,D=[{index:7,amount:15},{index:6,amount:25},{index:5,amount:30},{index:5,amount:45},{index:5,amount:65},{index:5,amount:85},{index:4,amount:90},{index:3,amount:95},{index:2,amount:97},{index:1,amount:98}];function Z(y,b,P){var l;return Math.round(y.h)>=60&&Math.round(y.h)<=240?l=P?Math.round(y.h)-p*b:Math.round(y.h)+p*b:l=P?Math.round(y.h)+p*b:Math.round(y.h)-p*b,l<0?l+=360:l>=360&&(l-=360),l}function u(y,b,P){if(y.h===0&&y.s===0)return y.s;var l;return P?l=y.s-O*b:b===U?l=y.s+O:l=y.s+N*b,l>1&&(l=1),P&&b===j&&l>.1&&(l=.1),l<.06&&(l=.06),Math.round(l*100)/100}function t(y,b,P){var l;return P?l=y.v+T*b:l=y.v-K*b,l=Math.max(0,Math.min(1,l)),Math.round(l*100)/100}function a(y){for(var b=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},P=[],l=new r.t(y),A=l.toHsv(),$=j;$>0;$-=1){var m=new r.t({h:Z(A,$,!0),s:u(A,$,!0),v:t(A,$,!0)});P.push(m)}P.push(l);for(var I=1;I<=U;I+=1){var J=new r.t({h:Z(A,I),s:u(A,I),v:t(A,I)});P.push(J)}return b.theme==="dark"?D.map(function(M){var q=M.index,re=M.amount;return new r.t(b.backgroundColor||"#141414").mix(P[q],re).toHexString()}):P.map(function(M){return M.toHexString()})}var n={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1677FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},i=["#fff1f0","#ffccc7","#ffa39e","#ff7875","#ff4d4f","#f5222d","#cf1322","#a8071a","#820014","#5c0011"];i.primary=i[5];var o=["#fff2e8","#ffd8bf","#ffbb96","#ff9c6e","#ff7a45","#fa541c","#d4380d","#ad2102","#871400","#610b00"];o.primary=o[5];var E=["#fff7e6","#ffe7ba","#ffd591","#ffc069","#ffa940","#fa8c16","#d46b08","#ad4e00","#873800","#612500"];E.primary=E[5];var C=["#fffbe6","#fff1b8","#ffe58f","#ffd666","#ffc53d","#faad14","#d48806","#ad6800","#874d00","#613400"];C.primary=C[5];var v=["#feffe6","#ffffb8","#fffb8f","#fff566","#ffec3d","#fadb14","#d4b106","#ad8b00","#876800","#614700"];v.primary=v[5];var h=["#fcffe6","#f4ffb8","#eaff8f","#d3f261","#bae637","#a0d911","#7cb305","#5b8c00","#3f6600","#254000"];h.primary=h[5];var k=["#f6ffed","#d9f7be","#b7eb8f","#95de64","#73d13d","#52c41a","#389e0d","#237804","#135200","#092b00"];k.primary=k[5];var G=["#e6fffb","#b5f5ec","#87e8de","#5cdbd3","#36cfc9","#13c2c2","#08979c","#006d75","#00474f","#002329"];G.primary=G[5];var R=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];R.primary=R[5];var Q=["#f0f5ff","#d6e4ff","#adc6ff","#85a5ff","#597ef7","#2f54eb","#1d39c4","#10239e","#061178","#030852"];Q.primary=Q[5];var Y=["#f9f0ff","#efdbff","#d3adf7","#b37feb","#9254de","#722ed1","#531dab","#391085","#22075e","#120338"];Y.primary=Y[5];var B=["#fff0f6","#ffd6e7","#ffadd2","#ff85c0","#f759ab","#eb2f96","#c41d7f","#9e1068","#780650","#520339"];B.primary=B[5];var te=["#a6a6a6","#999999","#8c8c8c","#808080","#737373","#666666","#404040","#1a1a1a","#000000","#000000"];te.primary=te[5];var pe=te,le={red:i,volcano:o,orange:E,gold:C,yellow:v,lime:h,green:k,cyan:G,blue:R,geekblue:Q,purple:Y,magenta:B,grey:te},se=["#2a1215","#431418","#58181c","#791a1f","#a61d24","#d32029","#e84749","#f37370","#f89f9a","#fac8c3"];se.primary=se[5];var H=["#2b1611","#441d12","#592716","#7c3118","#aa3e19","#d84a1b","#e87040","#f3956a","#f8b692","#fad4bc"];H.primary=H[5];var s=["#2b1d11","#442a11","#593815","#7c4a15","#aa6215","#d87a16","#e89a3c","#f3b765","#f8cf8d","#fae3b7"];s.primary=s[5];var d=["#2b2111","#443111","#594214","#7c5914","#aa7714","#d89614","#e8b339","#f3cc62","#f8df8b","#faedb5"];d.primary=d[5];var f=["#2b2611","#443b11","#595014","#7c6e14","#aa9514","#d8bd14","#e8d639","#f3ea62","#f8f48b","#fafab5"];f.primary=f[5];var c=["#1f2611","#2e3c10","#3e4f13","#536d13","#6f9412","#8bbb11","#a9d134","#c9e75d","#e4f88b","#f0fab5"];c.primary=c[5];var g=["#162312","#1d3712","#274916","#306317","#3c8618","#49aa19","#6abe39","#8fd460","#b2e58b","#d5f2bb"];g.primary=g[5];var S=["#112123","#113536","#144848","#146262","#138585","#13a8a8","#33bcb7","#58d1c9","#84e2d8","#b2f1e8"];S.primary=S[5];var z=["#111a2c","#112545","#15325b","#15417e","#1554ad","#1668dc","#3c89e8","#65a9f3","#8dc5f8","#b7dcfa"];z.primary=z[5];var _=["#131629","#161d40","#1c2755","#203175","#263ea0","#2b4acb","#5273e0","#7f9ef3","#a8c1f8","#d2e0fa"];_.primary=_[5];var w=["#1a1325","#24163a","#301c4d","#3e2069","#51258f","#642ab5","#854eca","#ab7ae0","#cda8f0","#ebd7fa"];w.primary=w[5];var F=["#291321","#40162f","#551c3b","#75204f","#a02669","#cb2b83","#e0529c","#f37fb7","#f8a8cc","#fad2e3"];F.primary=F[5];var L=["#151515","#1f1f1f","#2d2d2d","#393939","#494949","#5a5a5a","#6a6a6a","#7b7b7b","#888888","#969696"];L.primary=L[5];var ie={red:se,volcano:H,orange:s,gold:d,yellow:f,lime:c,green:g,cyan:S,blue:z,geekblue:_,purple:w,magenta:F,grey:L}},1977:function(oe,x,e){e.d(x,{n:function(){return Z}});var r=e(97685),p=e(71002),O=/^[v^~<>=]*?(\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+))?(?:-([\da-z\-]+(?:\.[\da-z\-]+)*))?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i,N=function(t){return t==="*"||t==="x"||t==="X"},T=function(t){var a=parseInt(t,10);return isNaN(a)?t:a},K=function(t,a){return(0,p.Z)(t)!==(0,p.Z)(a)?[String(t),String(a)]:[t,a]},j=function(t,a){if(N(t)||N(a))return 0;var n=K(T(t),T(a)),i=(0,r.Z)(n,2),o=i[0],E=i[1];return o>E?1:o<E?-1:0},U=function(t,a){for(var n=0;n<Math.max(t.length,a.length);n++){var i=j(t[n]||"0",a[n]||"0");if(i!==0)return i}return 0},D=function(t){var a,n=t.match(O);return n==null||(a=n.shift)===null||a===void 0||a.call(n),n},Z=function(t,a){var n=D(t),i=D(a),o=n.pop(),E=i.pop(),C=U(n,i);return C!==0?C:o||E?o?-1:1:0}},12044:function(oe,x,e){e.d(x,{j:function(){return O}});var r=e(34155),p=typeof r!="undefined"&&r.versions!=null&&r.versions.node!=null,O=function(){return typeof window!="undefined"&&typeof window.document!="undefined"&&typeof window.matchMedia!="undefined"&&!p}},81643:function(oe,x,e){e.d(x,{Z:function(){return r}});const r=p=>p?typeof p=="function"?p():p:null},85357:function(oe,x,e){e.d(x,{Z:function(){return H}});var r=e(67294),p=e(93967),O=e.n(p),N=e(9220),T=e(42550),K=e(74443),j=e(53124),U=e(35792),D=e(98675),Z=e(25378),t=r.createContext({}),a=e(11568),n=e(14747),i=e(83559),o=e(83262);const E=s=>{const{antCls:d,componentCls:f,iconCls:c,avatarBg:g,avatarColor:S,containerSize:z,containerSizeLG:_,containerSizeSM:w,textFontSize:F,textFontSizeLG:L,textFontSizeSM:ie,borderRadius:y,borderRadiusLG:b,borderRadiusSM:P,lineWidth:l,lineType:A}=s,$=(m,I,J)=>({width:m,height:m,borderRadius:"50%",[`&${f}-square`]:{borderRadius:J},[`&${f}-icon`]:{fontSize:I,[`> ${c}`]:{margin:0}}});return{[f]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,n.Wf)(s)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:S,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:g,border:`${(0,a.bf)(l)} ${A} transparent`,"&-image":{background:"transparent"},[`${d}-image-img`]:{display:"block"}}),$(z,F,y)),{"&-lg":Object.assign({},$(_,L,b)),"&-sm":Object.assign({},$(w,ie,P)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},C=s=>{const{componentCls:d,groupBorderColor:f,groupOverlapping:c,groupSpace:g}=s;return{[`${d}-group`]:{display:"inline-flex",[d]:{borderColor:f},"> *:not(:first-child)":{marginInlineStart:c}},[`${d}-group-popover`]:{[`${d} + ${d}`]:{marginInlineStart:g}}}},v=s=>{const{controlHeight:d,controlHeightLG:f,controlHeightSM:c,fontSize:g,fontSizeLG:S,fontSizeXL:z,fontSizeHeading3:_,marginXS:w,marginXXS:F,colorBorderBg:L}=s;return{containerSize:d,containerSizeLG:f,containerSizeSM:c,textFontSize:Math.round((S+z)/2),textFontSizeLG:_,textFontSizeSM:g,groupSpace:F,groupOverlapping:-w,groupBorderColor:L}};var h=(0,i.I$)("Avatar",s=>{const{colorTextLightSolid:d,colorTextPlaceholder:f}=s,c=(0,o.IX)(s,{avatarBg:f,avatarColor:d});return[E(c),C(c)]},v),k=function(s,d){var f={};for(var c in s)Object.prototype.hasOwnProperty.call(s,c)&&d.indexOf(c)<0&&(f[c]=s[c]);if(s!=null&&typeof Object.getOwnPropertySymbols=="function")for(var g=0,c=Object.getOwnPropertySymbols(s);g<c.length;g++)d.indexOf(c[g])<0&&Object.prototype.propertyIsEnumerable.call(s,c[g])&&(f[c[g]]=s[c[g]]);return f},R=r.forwardRef((s,d)=>{const{prefixCls:f,shape:c,size:g,src:S,srcSet:z,icon:_,className:w,rootClassName:F,style:L,alt:ie,draggable:y,children:b,crossOrigin:P,gap:l=4,onError:A}=s,$=k(s,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","style","alt","draggable","children","crossOrigin","gap","onError"]),[m,I]=r.useState(1),[J,M]=r.useState(!1),[q,re]=r.useState(!0),me=r.useRef(null),ce=r.useRef(null),fe=(0,T.sQ)(d,me),{getPrefixCls:de,avatar:ne}=r.useContext(j.E_),ee=r.useContext(t),ue=()=>{if(!ce.current||!me.current)return;const V=ce.current.offsetWidth,W=me.current.offsetWidth;V!==0&&W!==0&&l*2<W&&I(W-l*2<V?(W-l*2)/V:1)};r.useEffect(()=>{M(!0)},[]),r.useEffect(()=>{re(!0),I(1)},[S]),r.useEffect(ue,[l]);const be=()=>{(A==null?void 0:A())!==!1&&re(!1)},X=(0,D.Z)(V=>{var W,ge;return(ge=(W=g!=null?g:ee==null?void 0:ee.size)!==null&&W!==void 0?W:V)!==null&&ge!==void 0?ge:"default"}),Ee=Object.keys(typeof X=="object"?X||{}:{}).some(V=>["xs","sm","md","lg","xl","xxl"].includes(V)),ye=(0,Z.Z)(Ee),Oe=r.useMemo(()=>{if(typeof X!="object")return{};const V=K.c4.find(ge=>ye[ge]),W=X[V];return W?{width:W,height:W,fontSize:W&&(_||b)?W/2:18}:{}},[ye,X]),ae=de("avatar",f),Ce=(0,U.Z)(ae),[Pe,xe,De]=h(ae,Ce),Se=O()({[`${ae}-lg`]:X==="large",[`${ae}-sm`]:X==="small"}),he=r.isValidElement(S),_e=c||(ee==null?void 0:ee.shape)||"circle",Me=O()(ae,Se,ne==null?void 0:ne.className,`${ae}-${_e}`,{[`${ae}-image`]:he||S&&q,[`${ae}-icon`]:!!_},De,Ce,w,F,xe),Be=typeof X=="number"?{width:X,height:X,fontSize:_?X/2:18}:{};let ve;if(typeof S=="string"&&q)ve=r.createElement("img",{src:S,draggable:y,srcSet:z,onError:be,alt:ie,crossOrigin:P});else if(he)ve=S;else if(_)ve=_;else if(J||m!==1){const V=`scale(${m})`,W={msTransform:V,WebkitTransform:V,transform:V};ve=r.createElement(N.Z,{onResize:ue},r.createElement("span",{className:`${ae}-string`,ref:ce,style:Object.assign({},W)},b))}else ve=r.createElement("span",{className:`${ae}-string`,style:{opacity:0},ref:ce},b);return Pe(r.createElement("span",Object.assign({},$,{style:Object.assign(Object.assign(Object.assign(Object.assign({},Be),Oe),ne==null?void 0:ne.style),L),className:Me,ref:fe}),ve))}),Q=e(50344),Y=e(96159),B=e(55241);const te=s=>{const{size:d,shape:f}=r.useContext(t),c=r.useMemo(()=>({size:s.size||d,shape:s.shape||f}),[s.size,s.shape,d,f]);return r.createElement(t.Provider,{value:c},s.children)};var le=s=>{var d,f,c,g;const{getPrefixCls:S,direction:z}=r.useContext(j.E_),{prefixCls:_,className:w,rootClassName:F,style:L,maxCount:ie,maxStyle:y,size:b,shape:P,maxPopoverPlacement:l,maxPopoverTrigger:A,children:$,max:m}=s,I=S("avatar",_),J=`${I}-group`,M=(0,U.Z)(I),[q,re,me]=h(I,M),ce=O()(J,{[`${J}-rtl`]:z==="rtl"},me,M,w,F,re),fe=(0,Q.Z)($).map((ee,ue)=>(0,Y.Tm)(ee,{key:`avatar-key-${ue}`})),de=(m==null?void 0:m.count)||ie,ne=fe.length;if(de&&de<ne){const ee=fe.slice(0,de),ue=fe.slice(de,ne),be=(m==null?void 0:m.style)||y,X=((d=m==null?void 0:m.popover)===null||d===void 0?void 0:d.trigger)||A||"hover",Ee=((f=m==null?void 0:m.popover)===null||f===void 0?void 0:f.placement)||l||"top",ye=Object.assign(Object.assign({content:ue},m==null?void 0:m.popover),{classNames:{root:O()(`${J}-popover`,(g=(c=m==null?void 0:m.popover)===null||c===void 0?void 0:c.classNames)===null||g===void 0?void 0:g.root)},placement:Ee,trigger:X});return ee.push(r.createElement(B.Z,Object.assign({key:"avatar-popover-key",destroyOnHidden:!0},ye),r.createElement(R,{style:be},`+${ne-de}`))),q(r.createElement(te,{shape:P,size:b},r.createElement("div",{className:ce,style:L},ee)))}return q(r.createElement(te,{shape:P,size:b},r.createElement("div",{className:ce,style:L},fe)))};const se=R;se.Group=le;var H=se},66330:function(oe,x,e){e.d(x,{aV:function(){return D}});var r=e(67294),p=e(93967),O=e.n(p),N=e(92419),T=e(81643),K=e(53124),j=e(20136),U=function(t,a){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&a.indexOf(i)<0&&(n[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,i=Object.getOwnPropertySymbols(t);o<i.length;o++)a.indexOf(i[o])<0&&Object.prototype.propertyIsEnumerable.call(t,i[o])&&(n[i[o]]=t[i[o]]);return n};const D=({title:t,content:a,prefixCls:n})=>!t&&!a?null:r.createElement(r.Fragment,null,t&&r.createElement("div",{className:`${n}-title`},t),a&&r.createElement("div",{className:`${n}-inner-content`},a)),Z=t=>{const{hashId:a,prefixCls:n,className:i,style:o,placement:E="top",title:C,content:v,children:h}=t,k=(0,T.Z)(C),G=(0,T.Z)(v),R=O()(a,n,`${n}-pure`,`${n}-placement-${E}`,i);return r.createElement("div",{className:R,style:o},r.createElement("div",{className:`${n}-arrow`}),r.createElement(N.G,Object.assign({},t,{className:a,prefixCls:n}),h||r.createElement(D,{prefixCls:n,title:k,content:G})))},u=t=>{const{prefixCls:a,className:n}=t,i=U(t,["prefixCls","className"]),{getPrefixCls:o}=r.useContext(K.E_),E=o("popover",a),[C,v,h]=(0,j.Z)(E);return C(r.createElement(Z,Object.assign({},i,{prefixCls:E,hashId:v,className:O()(n,h)})))};x.ZP=u},55241:function(oe,x,e){var r=e(67294),p=e(93967),O=e.n(p),N=e(21770),T=e(15105),K=e(81643),j=e(33603),U=e(96159),D=e(83062),Z=e(66330),u=e(53124),t=e(20136),a=function(o,E){var C={};for(var v in o)Object.prototype.hasOwnProperty.call(o,v)&&E.indexOf(v)<0&&(C[v]=o[v]);if(o!=null&&typeof Object.getOwnPropertySymbols=="function")for(var h=0,v=Object.getOwnPropertySymbols(o);h<v.length;h++)E.indexOf(v[h])<0&&Object.prototype.propertyIsEnumerable.call(o,v[h])&&(C[v[h]]=o[v[h]]);return C};const i=r.forwardRef((o,E)=>{var C,v;const{prefixCls:h,title:k,content:G,overlayClassName:R,placement:Q="top",trigger:Y="hover",children:B,mouseEnterDelay:te=.1,mouseLeaveDelay:pe=.1,onOpenChange:le,overlayStyle:se={},styles:H,classNames:s}=o,d=a(o,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:f,className:c,style:g,classNames:S,styles:z}=(0,u.dj)("popover"),_=f("popover",h),[w,F,L]=(0,t.Z)(_),ie=f(),y=O()(R,F,L,c,S.root,s==null?void 0:s.root),b=O()(S.body,s==null?void 0:s.body),[P,l]=(0,N.Z)(!1,{value:(C=o.open)!==null&&C!==void 0?C:o.visible,defaultValue:(v=o.defaultOpen)!==null&&v!==void 0?v:o.defaultVisible}),A=(M,q)=>{l(M,!0),le==null||le(M,q)},$=M=>{M.keyCode===T.Z.ESC&&A(!1,M)},m=M=>{A(M)},I=(0,K.Z)(k),J=(0,K.Z)(G);return w(r.createElement(D.Z,Object.assign({placement:Q,trigger:Y,mouseEnterDelay:te,mouseLeaveDelay:pe},d,{prefixCls:_,classNames:{root:y,body:b},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},z.root),g),se),H==null?void 0:H.root),body:Object.assign(Object.assign({},z.body),H==null?void 0:H.body)},ref:E,open:P,onOpenChange:m,overlay:I||J?r.createElement(Z.aV,{prefixCls:_,title:I,content:J}):null,transitionName:(0,j.m)(ie,"zoom-big",d.transitionName),"data-popover-inject":!0}),(0,U.Tm)(B,{onKeyDown:M=>{var q,re;r.isValidElement(B)&&((re=B==null?void 0:(q=B.props).onKeyDown)===null||re===void 0||re.call(q,M)),$(M)}})))});i._InternalPanelDoNotUseOrYouWillBeFired=Z.ZP,x.Z=i},20136:function(oe,x,e){var r=e(14747),p=e(50438),O=e(97414),N=e(79511),T=e(8796),K=e(83559),j=e(83262);const U=u=>{const{componentCls:t,popoverColor:a,titleMinWidth:n,fontWeightStrong:i,innerPadding:o,boxShadowSecondary:E,colorTextHeading:C,borderRadiusLG:v,zIndexPopup:h,titleMarginBottom:k,colorBgElevated:G,popoverBg:R,titleBorderBottom:Q,innerContentPadding:Y,titlePadding:B}=u;return[{[t]:Object.assign(Object.assign({},(0,r.Wf)(u)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:h,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":G,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${t}-content`]:{position:"relative"},[`${t}-inner`]:{backgroundColor:R,backgroundClip:"padding-box",borderRadius:v,boxShadow:E,padding:o},[`${t}-title`]:{minWidth:n,marginBottom:k,color:C,fontWeight:i,borderBottom:Q,padding:B},[`${t}-inner-content`]:{color:a,padding:Y}})},(0,O.ZP)(u,"var(--antd-arrow-background-color)"),{[`${t}-pure`]:{position:"relative",maxWidth:"none",margin:u.sizePopupArrow,display:"inline-block",[`${t}-content`]:{display:"inline-block"}}}]},D=u=>{const{componentCls:t}=u;return{[t]:T.i.map(a=>{const n=u[`${a}6`];return{[`&${t}-${a}`]:{"--antd-arrow-background-color":n,[`${t}-inner`]:{backgroundColor:n},[`${t}-arrow`]:{background:"transparent"}}}})}},Z=u=>{const{lineWidth:t,controlHeight:a,fontHeight:n,padding:i,wireframe:o,zIndexPopupBase:E,borderRadiusLG:C,marginXS:v,lineType:h,colorSplit:k,paddingSM:G}=u,R=a-n,Q=R/2,Y=R/2-t,B=i;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:E+30},(0,N.w)(u)),(0,O.wZ)({contentRadius:C,limitVerticalRadius:!0})),{innerPadding:o?0:12,titleMarginBottom:o?0:v,titlePadding:o?`${Q}px ${B}px ${Y}px`:0,titleBorderBottom:o?`${t}px ${h} ${k}`:"none",innerContentPadding:o?`${G}px ${B}px`:0})};x.Z=(0,K.I$)("Popover",u=>{const{colorBgElevated:t,colorText:a}=u,n=(0,j.IX)(u,{popoverBg:t,popoverColor:a});return[U(n),D(n),(0,p._y)(n,"zoom-big")]},Z,{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]})}}]);
