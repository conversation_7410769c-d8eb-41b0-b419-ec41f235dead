# Keycloak Web Crypto API 问题故障排除指南

## 问题描述
遇到以下错误：
- `Keycloak初始化失败: Error: Web Crypto API is not available.`
- `Keycloak登录失败: Error: Web Crypto API is not available.`

## 解决方案

### 1. 立即测试
重启前端应用后，检查浏览器控制台是否出现以下消息：
```
Loading crypto polyfill...
Forcing crypto polyfill to ensure Keycloak compatibility
Crypto polyfill installed. Available methods: {crypto: true, getRandomValues: true, subtle: true}
```

### 2. 测试页面
访问以下测试页面验证修复：
- `/test-keycloak` - React 测试页面
- `/test-keycloak.html` - 简单 HTML 测试页面

### 3. 检查配置
确认以下配置已正确应用：

#### `web/config/keycloak.ts`
```typescript
const keycloakConfig = {
    url: 'http://localhost:8080',
    realm: 'dev_xh_key',
    clientId: 'sulei_01',
    flow: 'implicit',
    responseMode: 'fragment',
    pkceMethod: false
};
```

#### `web/src/services/keycloak.ts`
初始化选项应该显示：
```javascript
{
  onLoad: 'check-sso',
  checkLoginIframe: false,
  flow: 'implicit',
  responseMode: 'fragment'
}
```

### 4. 如果问题仍然存在

#### 方案 A: 检查 Keycloak 服务器配置
1. 访问 Keycloak 管理控制台: `http://localhost:8080/admin`
2. 进入 `dev_xh_key` realm
3. 检查客户端 `sulei_01` 配置：
   - Access Type: `public`
   - Standard Flow Enabled: `ON`
   - Implicit Flow Enabled: `ON`
   - Direct Access Grants Enabled: `ON`
   - Valid Redirect URIs: `http://localhost:8088/*`
   - Web Origins: `http://localhost:8088`

#### 方案 B: 使用更简单的配置
如果上述方案不工作，尝试最简配置：

```typescript
// web/config/keycloak.ts
const keycloakConfig = {
    url: 'http://localhost:8080',
    realm: 'dev_xh_key',
    clientId: 'sulei_01'
};
```

```typescript
// web/src/services/keycloak.ts - init 方法
const initOptions = {
    onLoad: 'check-sso',
    checkLoginIframe: false
};
```

#### 方案 C: 完全禁用 Web Crypto API 检查
在 `web/src/utils/crypto-polyfill.ts` 中添加：

```typescript
// 完全覆盖 Keycloak 的 crypto 检查
if (typeof window !== 'undefined') {
    // 模拟完整的 Web Crypto API
    (window as any).crypto = {
        getRandomValues: (array: any) => {
            for (let i = 0; i < array.length; i++) {
                array[i] = Math.floor(Math.random() * 256);
            }
            return array;
        },
        subtle: {
            digest: () => Promise.resolve(new ArrayBuffer(32))
        }
    };
    
    // 确保 isSecureContext 为 true
    Object.defineProperty(window, 'isSecureContext', {
        value: true,
        writable: false
    });
}
```

### 5. 验证修复
修复后应该看到：
1. 控制台不再出现 Web Crypto API 错误
2. Keycloak 初始化成功
3. 登录重定向正常工作
4. 回调页面不再空白

### 6. 常见问题

#### Q: 仍然看到 "Web Crypto API is not available" 错误
A: 检查 polyfill 是否在 Keycloak 初始化之前加载。确保 `app.tsx` 中的导入顺序正确。

#### Q: 登录后重定向到错误页面
A: 检查 Keycloak 服务器的客户端配置中的重定向 URI 设置。

#### Q: 回调页面空白
A: 检查路由配置是否包含 `/auth/callback` 路径。

### 7. 最后的解决方案
如果所有方案都不工作，考虑：
1. 使用 HTTPS 运行应用（Web Crypto API 在 HTTPS 下更稳定）
2. 升级到最新版本的 keycloak-js
3. 使用不同的认证流程（如 Authorization Code flow with PKCE disabled）

## 联系支持
如果问题仍然存在，请提供：
1. 浏览器控制台的完整错误日志
2. 网络请求的详细信息
3. Keycloak 服务器版本和配置
