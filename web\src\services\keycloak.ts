import keycloak from '../../config/keycloak';

export interface KeycloakService {
  login: () => Promise<void>;
  logout: () => Promise<void>;
  isAuthenticated: () => boolean;
  getToken: () => string | undefined;
  getUserInfo: () => any;
  init: (isCallback?: boolean) => Promise<boolean>;
  handleCallback: () => Promise<boolean>;
}

class KeycloakServiceImpl implements KeycloakService {
  private initialized = false;

  async init(isCallback: boolean = false): Promise<boolean> {
    // 检查是否已经初始化且认证状态有效
    if (this.initialized && keycloak.authenticated) {
      return true;
    }

    // 如果已经初始化但未认证，重置状态
    if (this.initialized && !keycloak.authenticated) {
      this.initialized = false;
    }

    try {
      let initOptions;

      if (isCallback) {
        // 如果是回调页面，使用不同的初始化选项
        initOptions = {
          onLoad: 'login-required',
          checkLoginIframe: false,
          flow: 'implicit',  // 使用 implicit flow 避免 Web Crypto API 问题
          responseMode: 'fragment'
        };
      } else {
        // 正常页面的初始化选项
        initOptions = {
          onLoad: 'check-sso',
          checkLoginIframe: false,
          flow: 'implicit',  // 使用 implicit flow 避免 Web Crypto API 问题
          responseMode: 'fragment'
        };
      }

      console.log('Keycloak初始化选项:', initOptions);
      const authenticated = await keycloak.init(initOptions);

      this.initialized = true;
      console.log('Keycloak初始化完成，认证状态:', authenticated);

      // 设置token刷新
      if (authenticated) {
        this.setupTokenRefresh();
      }

      return authenticated;
    } catch (error) {
      console.error('Keycloak初始化失败:', error);
      this.initialized = false;
      return false;
    }
  }

  async login(targetPath?: string): Promise<void> {
    try {
      // 确保Keycloak已初始化
      if (!this.initialized) {
        await this.init();
      }

      // 保存目标路径到 sessionStorage
      const redirectParam = targetPath || '/Console/projects';
      sessionStorage.setItem('auth_redirect_path', redirectParam);

      console.log('登录后重定向到:', redirectParam);

      // 确保keycloak实例存在
      if (!keycloak) {
        throw new Error('Keycloak实例未初始化');
      }

      // 使用简化的登录选项，让 Keycloak 使用默认的重定向 URI
      await keycloak.login({
        prompt: 'login'
      });
    } catch (error) {
      console.error('Keycloak登录失败:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      console.log('开始Keycloak登出...');

      // 获取当前token
      const token = this.getToken();

      // 如果token不存在，直接清除本地状态并跳转
      if (!token) {
        console.log('未找到有效token，直接跳转到登录页');
        keycloak.clearToken();
        window.location.replace('/#/user/login');
        return;
      }

      try {
        // 调用后端登出接口
        const response = await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        const result = await response.json();

        if (result.success && result.logoutUrl) {
          console.log('后端登出成功，重定向到Keycloak登出页面:', result.logoutUrl);
          // 清除本地token
          keycloak.clearToken();
          // 重定向到Keycloak登出页面
          window.location.href = result.logoutUrl;
        } else {
          throw new Error('后端登出接口返回异常');
        }
      } catch (error) {
        console.warn('后端登出失败，使用前端直接登出:', error);
        // 如果后端接口失败，直接使用Keycloak前端登出
        await keycloak.logout({
          redirectUri: window.location.origin + '/#/user/login',
        });
      }
    } catch (error) {
      console.error('登出过程中出现错误:', error);
      // 如果所有方法都失败，强制清除本地状态并跳转
      keycloak.clearToken();
      window.location.replace('/#/user/login');
    }
  }

  isAuthenticated(): boolean {
    return keycloak.authenticated || false;
  }

  getToken(): string | undefined {
    return keycloak.token;
  }

  getUserInfo(): any {
    if (!keycloak.authenticated) {
      return null;
    }

    try {
      return {
        id: keycloak.tokenParsed?.sub,
        username: keycloak.tokenParsed?.preferred_username,
        email: keycloak.tokenParsed?.email,
        name: keycloak.tokenParsed?.name,
        roles: keycloak.tokenParsed?.realm_access?.roles || [],
      };
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }

  async handleCallback(): Promise<boolean> {
    try {
      console.log('处理Keycloak回调...');

      // 如果已经初始化且认证成功，直接返回
      if (this.initialized && keycloak.authenticated) {
        console.log('Keycloak已认证，跳过回调处理');
        return true;
      }

      // 使用回调模式初始化
      const authenticated = await this.init(true);

      if (authenticated) {
        console.log('Keycloak回调处理成功');
        return true;
      } else {
        console.log('Keycloak回调处理失败：未认证');
        return false;
      }
    } catch (error) {
      console.error('Keycloak回调处理失败:', error);
      return false;
    }
  }

  private setupTokenRefresh(): void {
    // 每30秒检查一次token是否需要刷新
    setInterval(() => {
      keycloak.updateToken(70).then((refreshed) => {
        if (refreshed) {
          console.log('Token已刷新');
        }
      }).catch(() => {
        console.log('Token刷新失败');
        this.logout();
      });
    }, 30000);
  }
}

export const keycloakService = new KeycloakServiceImpl();
