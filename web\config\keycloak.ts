import Keycloak from 'keycloak-js';

const keycloakConfig = {
    // 根据环境选择 Keycloak 服务器地址
    url: 'http://localhost:8080',     // 开发环境 Keycloak 地址
    realm: 'dev_xh_key',
    clientId: 'sulei_01'
};

let keycloakInstance: Keycloak | null = null;

const getKeycloak = () => {
    if (!keycloakInstance) {
        keycloakInstance = new Keycloak(keycloakConfig);
    }
    return keycloakInstance;
};

export default getKeycloak();