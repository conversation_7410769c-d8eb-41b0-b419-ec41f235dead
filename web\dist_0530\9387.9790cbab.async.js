"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9387],{47046:function(X,y){var t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"};y.Z=t},63783:function(X,y,t){var h=t(1413),w=t(67294),P=t(36688),z=t(91146),k=function(D,H){return w.createElement(z.Z,(0,h.Z)((0,h.Z)({},D),{},{ref:H,icon:P.Z}))},$=w.forwardRef(k);y.Z=$},88484:function(X,y,t){t.d(y,{Z:function(){return D}});var h=t(1413),w=t(67294),P={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"upload",theme:"outlined"},z=P,k=t(91146),$=function(Z,L){return w.createElement(k.Z,(0,h.Z)((0,h.Z)({},Z),{},{ref:L,icon:z}))},W=w.forwardRef($),D=W},96074:function(X,y,t){t.d(y,{Z:function(){return q}});var h=t(67294),w=t(93967),P=t.n(w),z=t(53124),k=t(98675),$=t(11568),W=t(14747),D=t(83559),H=t(83262);const Z=r=>{const{componentCls:d}=r;return{[d]:{"&-horizontal":{[`&${d}`]:{"&-sm":{marginBlock:r.marginXS},"&-md":{marginBlock:r.margin}}}}}},L=r=>{const{componentCls:d,sizePaddingEdgeHorizontal:S,colorSplit:u,lineWidth:m,textPaddingInline:A,orientationMargin:M,verticalMarginInline:E}=r;return{[d]:Object.assign(Object.assign({},(0,W.Wf)(r)),{borderBlockStart:`${(0,$.bf)(m)} solid ${u}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:E,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${(0,$.bf)(m)} solid ${u}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${(0,$.bf)(r.marginLG)} 0`},[`&-horizontal${d}-with-text`]:{display:"flex",alignItems:"center",margin:`${(0,$.bf)(r.dividerHorizontalWithTextGutterMargin)} 0`,color:r.colorTextHeading,fontWeight:500,fontSize:r.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${u}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${(0,$.bf)(m)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${d}-with-text-start`]:{"&::before":{width:`calc(${M} * 100%)`},"&::after":{width:`calc(100% - ${M} * 100%)`}},[`&-horizontal${d}-with-text-end`]:{"&::before":{width:`calc(100% - ${M} * 100%)`},"&::after":{width:`calc(${M} * 100%)`}},[`${d}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:A},"&-dashed":{background:"none",borderColor:u,borderStyle:"dashed",borderWidth:`${(0,$.bf)(m)} 0 0`},[`&-horizontal${d}-with-text${d}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${d}-dashed`]:{borderInlineStartWidth:m,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:u,borderStyle:"dotted",borderWidth:`${(0,$.bf)(m)} 0 0`},[`&-horizontal${d}-with-text${d}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${d}-dotted`]:{borderInlineStartWidth:m,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${d}-with-text`]:{color:r.colorText,fontWeight:"normal",fontSize:r.fontSize},[`&-horizontal${d}-with-text-start${d}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${d}-inner-text`]:{paddingInlineStart:S}},[`&-horizontal${d}-with-text-end${d}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${d}-inner-text`]:{paddingInlineEnd:S}}})}},K=r=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:r.marginXS});var F=(0,D.I$)("Divider",r=>{const d=(0,H.IX)(r,{dividerHorizontalWithTextGutterMargin:r.margin,sizePaddingEdgeHorizontal:0});return[L(d),Z(d)]},K,{unitless:{orientationMargin:!0}}),Y=function(r,d){var S={};for(var u in r)Object.prototype.hasOwnProperty.call(r,u)&&d.indexOf(u)<0&&(S[u]=r[u]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var m=0,u=Object.getOwnPropertySymbols(r);m<u.length;m++)d.indexOf(u[m])<0&&Object.prototype.propertyIsEnumerable.call(r,u[m])&&(S[u[m]]=r[u[m]]);return S};const J={small:"sm",middle:"md"};var q=r=>{const{getPrefixCls:d,direction:S,className:u,style:m}=(0,z.dj)("divider"),{prefixCls:A,type:M="horizontal",orientation:E="center",orientationMargin:C,className:_,rootClassName:nn,children:R,dashed:en,variant:tn="solid",plain:G,style:an,size:n}=r,e=Y(r,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),i=d("divider",A),[o,s,g]=F(i),f=(0,k.Z)(n),l=J[f],a=!!R,c=h.useMemo(()=>E==="left"?S==="rtl"?"end":"start":E==="right"?S==="rtl"?"start":"end":E,[S,E]),v=c==="start"&&C!=null,b=c==="end"&&C!=null,N=P()(i,u,s,g,`${i}-${M}`,{[`${i}-with-text`]:a,[`${i}-with-text-${c}`]:a,[`${i}-dashed`]:!!en,[`${i}-${tn}`]:tn!=="solid",[`${i}-plain`]:!!G,[`${i}-rtl`]:S==="rtl",[`${i}-no-default-orientation-margin-start`]:v,[`${i}-no-default-orientation-margin-end`]:b,[`${i}-${l}`]:!!l},_,nn),I=h.useMemo(()=>typeof C=="number"?C:/^\d+$/.test(C)?Number(C):C,[C]),j={marginInlineStart:v?I:void 0,marginInlineEnd:b?I:void 0};return o(h.createElement("div",Object.assign({className:N,style:Object.assign(Object.assign({},m),an)},e,{role:"separator"}),R&&M!=="vertical"&&h.createElement("span",{className:`${i}-inner-text`,style:j},R)))}},72269:function(X,y,t){t.d(y,{Z:function(){return an}});var h=t(67294),w=t(19267),P=t(93967),z=t.n(P),k=t(87462),$=t(4942),W=t(97685),D=t(91),H=t(21770),Z=t(15105),L=["prefixCls","className","checked","defaultChecked","disabled","loadingIcon","checkedChildren","unCheckedChildren","onClick","onChange","onKeyDown"],K=h.forwardRef(function(n,e){var i,o=n.prefixCls,s=o===void 0?"rc-switch":o,g=n.className,f=n.checked,l=n.defaultChecked,a=n.disabled,c=n.loadingIcon,v=n.checkedChildren,b=n.unCheckedChildren,N=n.onClick,I=n.onChange,j=n.onKeyDown,rn=(0,D.Z)(n,L),ln=(0,H.Z)(!1,{value:f,defaultValue:l}),Q=(0,W.Z)(ln,2),T=Q[0],O=Q[1];function U(p,V){var B=T;return a||(B=p,O(B),I==null||I(B,V)),B}function on(p){p.which===Z.Z.LEFT?U(!1,p):p.which===Z.Z.RIGHT&&U(!0,p),j==null||j(p)}function x(p){var V=U(!T,p);N==null||N(V,p)}var dn=z()(s,g,(i={},(0,$.Z)(i,"".concat(s,"-checked"),T),(0,$.Z)(i,"".concat(s,"-disabled"),a),i));return h.createElement("button",(0,k.Z)({},rn,{type:"button",role:"switch","aria-checked":T,disabled:a,className:dn,ref:e,onKeyDown:on,onClick:x}),c,h.createElement("span",{className:"".concat(s,"-inner")},h.createElement("span",{className:"".concat(s,"-inner-checked")},v),h.createElement("span",{className:"".concat(s,"-inner-unchecked")},b)))});K.displayName="Switch";var F=K,Y=t(45353),J=t(53124),cn=t(98866),q=t(98675),r=t(11568),d=t(15063),S=t(14747),u=t(83559),m=t(83262);const A=n=>{const{componentCls:e,trackHeightSM:i,trackPadding:o,trackMinWidthSM:s,innerMinMarginSM:g,innerMaxMarginSM:f,handleSizeSM:l,calc:a}=n,c=`${e}-inner`,v=(0,r.bf)(a(l).add(a(o).mul(2)).equal()),b=(0,r.bf)(a(f).mul(2).equal());return{[e]:{[`&${e}-small`]:{minWidth:s,height:i,lineHeight:(0,r.bf)(i),[`${e}-inner`]:{paddingInlineStart:f,paddingInlineEnd:g,[`${c}-checked, ${c}-unchecked`]:{minHeight:i},[`${c}-checked`]:{marginInlineStart:`calc(-100% + ${v} - ${b})`,marginInlineEnd:`calc(100% - ${v} + ${b})`},[`${c}-unchecked`]:{marginTop:a(i).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`${e}-handle`]:{width:l,height:l},[`${e}-loading-icon`]:{top:a(a(l).sub(n.switchLoadingIconSize)).div(2).equal(),fontSize:n.switchLoadingIconSize},[`&${e}-checked`]:{[`${e}-inner`]:{paddingInlineStart:g,paddingInlineEnd:f,[`${c}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${c}-unchecked`]:{marginInlineStart:`calc(100% - ${v} + ${b})`,marginInlineEnd:`calc(-100% + ${v} - ${b})`}},[`${e}-handle`]:{insetInlineStart:`calc(100% - ${(0,r.bf)(a(l).add(o).equal())})`}},[`&:not(${e}-disabled):active`]:{[`&:not(${e}-checked) ${c}`]:{[`${c}-unchecked`]:{marginInlineStart:a(n.marginXXS).div(2).equal(),marginInlineEnd:a(n.marginXXS).mul(-1).div(2).equal()}},[`&${e}-checked ${c}`]:{[`${c}-checked`]:{marginInlineStart:a(n.marginXXS).mul(-1).div(2).equal(),marginInlineEnd:a(n.marginXXS).div(2).equal()}}}}}}},M=n=>{const{componentCls:e,handleSize:i,calc:o}=n;return{[e]:{[`${e}-loading-icon${n.iconCls}`]:{position:"relative",top:o(o(i).sub(n.fontSize)).div(2).equal(),color:n.switchLoadingIconColor,verticalAlign:"top"},[`&${e}-checked ${e}-loading-icon`]:{color:n.switchColor}}}},E=n=>{const{componentCls:e,trackPadding:i,handleBg:o,handleShadow:s,handleSize:g,calc:f}=n,l=`${e}-handle`;return{[e]:{[l]:{position:"absolute",top:i,insetInlineStart:i,width:g,height:g,transition:`all ${n.switchDuration} ease-in-out`,"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:o,borderRadius:f(g).div(2).equal(),boxShadow:s,transition:`all ${n.switchDuration} ease-in-out`,content:'""'}},[`&${e}-checked ${l}`]:{insetInlineStart:`calc(100% - ${(0,r.bf)(f(g).add(i).equal())})`},[`&:not(${e}-disabled):active`]:{[`${l}::before`]:{insetInlineEnd:n.switchHandleActiveInset,insetInlineStart:0},[`&${e}-checked ${l}::before`]:{insetInlineEnd:0,insetInlineStart:n.switchHandleActiveInset}}}}},C=n=>{const{componentCls:e,trackHeight:i,trackPadding:o,innerMinMargin:s,innerMaxMargin:g,handleSize:f,calc:l}=n,a=`${e}-inner`,c=(0,r.bf)(l(f).add(l(o).mul(2)).equal()),v=(0,r.bf)(l(g).mul(2).equal());return{[e]:{[a]:{display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:g,paddingInlineEnd:s,transition:`padding-inline-start ${n.switchDuration} ease-in-out, padding-inline-end ${n.switchDuration} ease-in-out`,[`${a}-checked, ${a}-unchecked`]:{display:"block",color:n.colorTextLightSolid,fontSize:n.fontSizeSM,transition:`margin-inline-start ${n.switchDuration} ease-in-out, margin-inline-end ${n.switchDuration} ease-in-out`,pointerEvents:"none",minHeight:i},[`${a}-checked`]:{marginInlineStart:`calc(-100% + ${c} - ${v})`,marginInlineEnd:`calc(100% - ${c} + ${v})`},[`${a}-unchecked`]:{marginTop:l(i).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`&${e}-checked ${a}`]:{paddingInlineStart:s,paddingInlineEnd:g,[`${a}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${a}-unchecked`]:{marginInlineStart:`calc(100% - ${c} + ${v})`,marginInlineEnd:`calc(-100% + ${c} - ${v})`}},[`&:not(${e}-disabled):active`]:{[`&:not(${e}-checked) ${a}`]:{[`${a}-unchecked`]:{marginInlineStart:l(o).mul(2).equal(),marginInlineEnd:l(o).mul(-1).mul(2).equal()}},[`&${e}-checked ${a}`]:{[`${a}-checked`]:{marginInlineStart:l(o).mul(-1).mul(2).equal(),marginInlineEnd:l(o).mul(2).equal()}}}}}},_=n=>{const{componentCls:e,trackHeight:i,trackMinWidth:o}=n;return{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,S.Wf)(n)),{position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:o,height:i,lineHeight:(0,r.bf)(i),verticalAlign:"middle",background:n.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:`all ${n.motionDurationMid}`,userSelect:"none",[`&:hover:not(${e}-disabled)`]:{background:n.colorTextTertiary}}),(0,S.Qy)(n)),{[`&${e}-checked`]:{background:n.switchColor,[`&:hover:not(${e}-disabled)`]:{background:n.colorPrimaryHover}},[`&${e}-loading, &${e}-disabled`]:{cursor:"not-allowed",opacity:n.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}},[`&${e}-rtl`]:{direction:"rtl"}})}},nn=n=>{const{fontSize:e,lineHeight:i,controlHeight:o,colorWhite:s}=n,g=e*i,f=o/2,l=2,a=g-l*2,c=f-l*2;return{trackHeight:g,trackHeightSM:f,trackMinWidth:a*2+l*4,trackMinWidthSM:c*2+l*2,trackPadding:l,handleBg:s,handleSize:a,handleSizeSM:c,handleShadow:`0 2px 4px 0 ${new d.t("#00230b").setA(.2).toRgbString()}`,innerMinMargin:a/2,innerMaxMargin:a+l+l*2,innerMinMarginSM:c/2,innerMaxMarginSM:c+l+l*2}};var R=(0,u.I$)("Switch",n=>{const e=(0,m.IX)(n,{switchDuration:n.motionDurationMid,switchColor:n.colorPrimary,switchDisabledOpacity:n.opacityLoading,switchLoadingIconSize:n.calc(n.fontSizeIcon).mul(.75).equal(),switchLoadingIconColor:`rgba(0, 0, 0, ${n.opacityLoading})`,switchHandleActiveInset:"-30%"});return[_(e),C(e),E(e),M(e),A(e)]},nn),en=function(n,e){var i={};for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&e.indexOf(o)<0&&(i[o]=n[o]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,o=Object.getOwnPropertySymbols(n);s<o.length;s++)e.indexOf(o[s])<0&&Object.prototype.propertyIsEnumerable.call(n,o[s])&&(i[o[s]]=n[o[s]]);return i};const G=h.forwardRef((n,e)=>{const{prefixCls:i,size:o,disabled:s,loading:g,className:f,rootClassName:l,style:a,checked:c,value:v,defaultChecked:b,defaultValue:N,onChange:I}=n,j=en(n,["prefixCls","size","disabled","loading","className","rootClassName","style","checked","value","defaultChecked","defaultValue","onChange"]),[rn,ln]=(0,H.Z)(!1,{value:c!=null?c:v,defaultValue:b!=null?b:N}),{getPrefixCls:Q,direction:T,switch:O}=h.useContext(J.E_),U=h.useContext(cn.Z),on=(s!=null?s:U)||g,x=Q("switch",i),dn=h.createElement("div",{className:`${x}-handle`},g&&h.createElement(w.Z,{className:`${x}-loading-icon`})),[p,V,B]=R(x),hn=(0,q.Z)(o),gn=z()(O==null?void 0:O.className,{[`${x}-small`]:hn==="small",[`${x}-loading`]:g,[`${x}-rtl`]:T==="rtl"},f,l,V,B),un=Object.assign(Object.assign({},O==null?void 0:O.style),a),mn=(...sn)=>{ln(sn[0]),I==null||I.apply(void 0,sn)};return p(h.createElement(Y.Z,{component:"Switch"},h.createElement(F,Object.assign({},j,{checked:rn,onChange:mn,prefixCls:x,className:gn,style:un,disabled:on,ref:e,loadingIcon:dn}))))});G.__ANT_SWITCH=!0;var an=G}}]);
