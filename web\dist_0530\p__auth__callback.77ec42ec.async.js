"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7647],{86390:function(we,j,r){r.r(j),r.d(j,{default:function(){return xe}});var L=r(15009),B=r.n(L),W=r(99289),G=r.n(W),U=r(5574),V=r.n(U),l=r(67294),X=r(19735),K=r(17012),Q=r(62208),J=r(29950),Y=r(1558),k=r(93967),b=r.n(k),q=r(29372),_=r(64217),ee=r(42550),ne=r(96159),oe=r(53124),N=r(11568),te=r(14747),re=r(83559);const E=(e,n,o,t,s)=>({background:e,border:`${(0,N.bf)(t.lineWidth)} ${t.lineType} ${n}`,[`${s}-icon`]:{color:o}}),se=e=>{const{componentCls:n,motionDurationSlow:o,marginXS:t,marginSM:s,fontSize:i,fontSizeLG:u,lineHeight:f,borderRadiusLG:d,motionEaseInOutCirc:c,withDescriptionIconSize:g,colorText:p,colorTextHeading:v,withDescriptionPadding:C,defaultPadding:a}=e;return{[n]:Object.assign(Object.assign({},(0,te.Wf)(e)),{position:"relative",display:"flex",alignItems:"center",padding:a,wordWrap:"break-word",borderRadius:d,[`&${n}-rtl`]:{direction:"rtl"},[`${n}-content`]:{flex:1,minWidth:0},[`${n}-icon`]:{marginInlineEnd:t,lineHeight:0},"&-description":{display:"none",fontSize:i,lineHeight:f},"&-message":{color:v},[`&${n}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${o} ${c}, opacity ${o} ${c},
        padding-top ${o} ${c}, padding-bottom ${o} ${c},
        margin-bottom ${o} ${c}`},[`&${n}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${n}-with-description`]:{alignItems:"flex-start",padding:C,[`${n}-icon`]:{marginInlineEnd:s,fontSize:g,lineHeight:0},[`${n}-message`]:{display:"block",marginBottom:t,color:v,fontSize:u},[`${n}-description`]:{display:"block",color:p}},[`${n}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},ae=e=>{const{componentCls:n,colorSuccess:o,colorSuccessBorder:t,colorSuccessBg:s,colorWarning:i,colorWarningBorder:u,colorWarningBg:f,colorError:d,colorErrorBorder:c,colorErrorBg:g,colorInfo:p,colorInfoBorder:v,colorInfoBg:C}=e;return{[n]:{"&-success":E(s,t,o,e,n),"&-info":E(C,v,p,e,n),"&-warning":E(f,u,i,e,n),"&-error":Object.assign(Object.assign({},E(g,c,d,e,n)),{[`${n}-description > pre`]:{margin:0,padding:0}})}}},le=e=>{const{componentCls:n,iconCls:o,motionDurationMid:t,marginXS:s,fontSizeIcon:i,colorIcon:u,colorIconHover:f}=e;return{[n]:{"&-action":{marginInlineStart:s},[`${n}-close-icon`]:{marginInlineStart:s,padding:0,overflow:"hidden",fontSize:i,lineHeight:(0,N.bf)(i),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${o}-close`]:{color:u,transition:`color ${t}`,"&:hover":{color:f}}},"&-close-text":{color:u,transition:`color ${t}`,"&:hover":{color:f}}}}},ie=e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`});var ce=(0,re.I$)("Alert",e=>[se(e),ae(e),le(e)],ie),T=function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,t=Object.getOwnPropertySymbols(e);s<t.length;s++)n.indexOf(t[s])<0&&Object.prototype.propertyIsEnumerable.call(e,t[s])&&(o[t[s]]=e[t[s]]);return o};const de={success:X.Z,info:Y.Z,error:K.Z,warning:J.Z},ue=e=>{const{icon:n,prefixCls:o,type:t}=e,s=de[t]||null;return n?(0,ne.wm)(n,l.createElement("span",{className:`${o}-icon`},n),()=>({className:b()(`${o}-icon`,n.props.className)})):l.createElement(s,{className:`${o}-icon`})},fe=e=>{const{isClosable:n,prefixCls:o,closeIcon:t,handleClose:s,ariaProps:i}=e,u=t===!0||t===void 0?l.createElement(Q.Z,null):t;return n?l.createElement("button",Object.assign({type:"button",onClick:s,className:`${o}-close-icon`,tabIndex:0},i),u):null};var O=l.forwardRef((e,n)=>{const{description:o,prefixCls:t,message:s,banner:i,className:u,rootClassName:f,style:d,onMouseEnter:c,onMouseLeave:g,onClick:p,afterClose:v,showIcon:C,closable:a,closeText:x,closeIcon:$,action:A,id:Ee}=e,be=T(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[z,je]=l.useState(!1),M=l.useRef(null);l.useImperativeHandle(n,()=>({nativeElement:M.current}));const{getPrefixCls:Be,direction:Ne,closable:S,closeIcon:Z,className:Te,style:Oe}=(0,oe.dj)("alert"),m=Be("alert",t),[Fe,Pe,De]=ce(m),Ae=h=>{var I;je(!0),(I=e.onClose)===null||I===void 0||I.call(e,h)},H=l.useMemo(()=>e.type!==void 0?e.type:i?"warning":"info",[e.type,i]),ze=l.useMemo(()=>typeof a=="object"&&a.closeIcon||x?!0:typeof a=="boolean"?a:$!==!1&&$!==null&&$!==void 0?!0:!!S,[x,$,a,S]),R=i&&C===void 0?!0:C,Me=b()(m,`${m}-${H}`,{[`${m}-with-description`]:!!o,[`${m}-no-icon`]:!R,[`${m}-banner`]:!!i,[`${m}-rtl`]:Ne==="rtl"},Te,u,f,De,Pe),Ze=(0,_.Z)(be,{aria:!0,data:!0}),He=l.useMemo(()=>typeof a=="object"&&a.closeIcon?a.closeIcon:x||($!==void 0?$:typeof S=="object"&&S.closeIcon?S.closeIcon:Z),[$,a,x,Z]),Re=l.useMemo(()=>{const h=a!=null?a:S;if(typeof h=="object"){const{closeIcon:I}=h;return T(h,["closeIcon"])}return{}},[a,S]);return Fe(l.createElement(q.ZP,{visible:!z,motionName:`${m}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:h=>({maxHeight:h.offsetHeight}),onLeaveEnd:v},({className:h,style:I},w)=>l.createElement("div",Object.assign({id:Ee,ref:(0,ee.sQ)(M,w),"data-show":!z,className:b()(Me,h),style:Object.assign(Object.assign(Object.assign({},Oe),d),I),onMouseEnter:c,onMouseLeave:g,onClick:p,role:"alert"},Ze),R?l.createElement(ue,{description:o,icon:e.icon,prefixCls:m,type:H}):null,l.createElement("div",{className:`${m}-content`},s?l.createElement("div",{className:`${m}-message`},s):null,o?l.createElement("div",{className:`${m}-description`},o):null),A?l.createElement("div",{className:`${m}-action`},A):null,l.createElement(fe,{isClosable:ze,prefixCls:m,closeIcon:He,handleClose:Ae,ariaProps:Re}))))}),me=r(15671),ge=r(43144),F=r(61120),pe=r(78814),ve=r(82963);function he(e,n,o){return n=(0,F.Z)(n),(0,ve.Z)(e,(0,pe.Z)()?Reflect.construct(n,o||[],(0,F.Z)(e).constructor):n.apply(e,o))}var ye=r(60136),Ce=function(e){function n(){var o;return(0,me.Z)(this,n),o=he(this,n,arguments),o.state={error:void 0,info:{componentStack:""}},o}return(0,ye.Z)(n,e),(0,ge.Z)(n,[{key:"componentDidCatch",value:function(t,s){this.setState({error:t,info:s})}},{key:"render",value:function(){const{message:t,description:s,id:i,children:u}=this.props,{error:f,info:d}=this.state,c=(d==null?void 0:d.componentStack)||null,g=typeof t=="undefined"?(f||"").toString():t,p=typeof s=="undefined"?c:s;return f?l.createElement(O,{id:i,type:"error",message:g,description:l.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},p)}):u}}])}(l.Component);const P=O;P.ErrorBoundary=Ce;var $e=P,Se=r(74330),D=r(84226),y=r(85893),Ie=function(){var n=(0,l.useState)(null),o=V()(n,2),t=o[0],s=o[1];return(0,l.useEffect)(function(){var i=function(){var u=G()(B()().mark(function f(){var d,c,g,p,v;return B()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(a.prev=0,console.log("=== \u5904\u7406Keycloak\u56DE\u8C03\u5F00\u59CB ==="),console.log("\u5F53\u524DURL:",window.location.href),d=new URLSearchParams(window.location.hash.substring(1)),c=d.get("access_token"),g=d.get("id_token"),p=d.get("error"),v=d.get("error_description"),console.log("\u56DE\u8C03\u53C2\u6570:",{accessToken:c?"\u5B58\u5728":"\u4E0D\u5B58\u5728",idToken:g?"\u5B58\u5728":"\u4E0D\u5B58\u5728",error:p,errorDescription:v}),!p){a.next=11;break}throw new Error("Keycloak\u9519\u8BEF: ".concat(p," - ").concat(v));case 11:c?(console.log("\u68C0\u6D4B\u5230\u8BBF\u95EE\u4EE4\u724C\uFF0C\u91CD\u5B9A\u5411\u5230\u4E3B\u9875"),localStorage.setItem("access_token",c),g&&localStorage.setItem("id_token",g),window.location.replace("/Console/projects")):(console.log("\u6CA1\u6709\u8BBF\u95EE\u4EE4\u724C\uFF0C\u91CD\u5B9A\u5411\u5230\u767B\u5F55\u9875\u9762"),s("\u65E0\u6548\u7684\u56DE\u8C03\u8BBF\u95EE\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"),setTimeout(function(){D.history.replace("/user/login")},2e3)),a.next=19;break;case 14:a.prev=14,a.t0=a.catch(0),console.error("\u56DE\u8C03\u5904\u7406\u5931\u8D25:",a.t0),s("\u56DE\u8C03\u5904\u7406\u5931\u8D25: ".concat(a.t0.message||"\u672A\u77E5\u9519\u8BEF")),setTimeout(function(){D.history.replace("/user/login")},3e3);case 19:case"end":return a.stop()}},f,null,[[0,14]])}));return function(){return u.apply(this,arguments)}}();i()},[]),(0,y.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",flexDirection:"column",padding:"20px",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",color:"white"},children:t?(0,y.jsx)($e,{message:"\u8BA4\u8BC1\u9519\u8BEF",description:t,type:"error",showIcon:!0,style:{marginBottom:16}}):(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(Se.Z,{size:"large",style:{color:"white"}}),(0,y.jsx)("div",{style:{marginTop:24,fontSize:"18px",fontWeight:500,textAlign:"center"},children:"\u767B\u5F55\u6210\u529F\uFF01\u6B63\u5728\u8DF3\u8F6C..."}),(0,y.jsx)("div",{style:{marginTop:8,fontSize:"14px",opacity:.8,textAlign:"center"},children:"\u8BF7\u7A0D\u5019\uFF0C\u5373\u5C06\u8FDB\u5165\u7CFB\u7EDF"})]})})},xe=Ie}}]);
