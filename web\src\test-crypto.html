<!DOCTYPE html>
<html>
<head>
    <title>Crypto API Test</title>
</head>
<body>
    <h1>Web Crypto API Test</h1>
    <div id="results"></div>
    
    <script>
        const results = document.getElementById('results');
        
        function log(message) {
            console.log(message);
            results.innerHTML += '<p>' + message + '</p>';
        }
        
        // Test Web Crypto API availability
        log('Testing Web Crypto API...');
        
        if (typeof window.crypto !== 'undefined') {
            log('✅ window.crypto is available');
            
            if (typeof window.crypto.getRandomValues !== 'undefined') {
                log('✅ window.crypto.getRandomValues is available');
                
                // Test getRandomValues
                try {
                    const array = new Uint8Array(10);
                    window.crypto.getRandomValues(array);
                    log('✅ getRandomValues works: ' + Array.from(array).join(','));
                } catch (e) {
                    log('❌ getRandomValues failed: ' + e.message);
                }
            } else {
                log('❌ window.crypto.getRandomValues is not available');
            }
            
            if (typeof window.crypto.subtle !== 'undefined') {
                log('✅ window.crypto.subtle is available');
                
                // Test digest
                try {
                    const encoder = new TextEncoder();
                    const data = encoder.encode('test');
                    window.crypto.subtle.digest('SHA-256', data).then(hash => {
                        log('✅ SHA-256 digest works');
                    }).catch(e => {
                        log('❌ SHA-256 digest failed: ' + e.message);
                    });
                } catch (e) {
                    log('❌ SHA-256 digest failed: ' + e.message);
                }
            } else {
                log('❌ window.crypto.subtle is not available');
            }
        } else {
            log('❌ window.crypto is not available');
        }
        
        // Test if we're in a secure context
        log('Secure context: ' + (window.isSecureContext ? 'Yes' : 'No'));
        log('Protocol: ' + window.location.protocol);
        log('Host: ' + window.location.host);
    </script>
</body>
</html>
