// Web Crypto API polyfill for environments where it's not available
// This is needed for Keycloak in some browser environments

declare global {
  interface Window {
    crypto: Crypto;
  }
}

console.log('Loading crypto polyfill...');

// Simple polyfill for getRandomValues
const getRandomValues = (array: any) => {
  for (let i = 0; i < array.length; i++) {
    array[i] = Math.floor(Math.random() * 256);
  }
  return array;
};

// Base64 URL encoding/decoding utilities
const base64UrlEncode = (str: string): string => {
  return btoa(str)
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
};

const base64UrlDecode = (str: string): string => {
  str = (str + '===').slice(0, str.length + (str.length % 4));
  str = str.replace(/-/g, '+').replace(/_/g, '/');
  return atob(str);
};

// Simple SHA-256 implementation for PKCE
const sha256 = async (message: string): Promise<ArrayBuffer> => {
  // This is a very basic implementation
  // In a real application, you'd want to use a proper crypto library
  const msgBuffer = new TextEncoder().encode(message);

  // For demo purposes, we'll create a fake hash
  // In production, use a proper SHA-256 implementation
  const hash = new Uint8Array(32);
  for (let i = 0; i < 32; i++) {
    hash[i] = (msgBuffer[i % msgBuffer.length] + i) % 256;
  }

  return hash.buffer;
};

// Create a more complete SubtleCrypto implementation
const createSubtleCrypto = () => ({
  digest: async (algorithm: any, data: any) => {
    if (algorithm === 'SHA-256' || (algorithm && algorithm.name === 'SHA-256')) {
      const text = typeof data === 'string' ? data : new TextDecoder().decode(data);
      return sha256(text);
    }
    throw new Error('Only SHA-256 is supported in this polyfill');
  },
  encrypt: () => Promise.reject(new Error('SubtleCrypto encrypt not implemented in polyfill')),
  decrypt: () => Promise.reject(new Error('SubtleCrypto decrypt not implemented in polyfill')),
  sign: () => Promise.reject(new Error('SubtleCrypto sign not implemented in polyfill')),
  verify: () => Promise.reject(new Error('SubtleCrypto verify not implemented in polyfill')),
  generateKey: () => Promise.reject(new Error('SubtleCrypto generateKey not implemented in polyfill')),
  deriveKey: () => Promise.reject(new Error('SubtleCrypto deriveKey not implemented in polyfill')),
  deriveBits: () => Promise.reject(new Error('SubtleCrypto deriveBits not implemented in polyfill')),
  importKey: () => Promise.reject(new Error('SubtleCrypto importKey not implemented in polyfill')),
  exportKey: () => Promise.reject(new Error('SubtleCrypto exportKey not implemented in polyfill')),
  wrapKey: () => Promise.reject(new Error('SubtleCrypto wrapKey not implemented in polyfill')),
  unwrapKey: () => Promise.reject(new Error('SubtleCrypto unwrapKey not implemented in polyfill')),
});

if (typeof window !== 'undefined') {
  console.log('Setting up crypto polyfill...');

  // Check if crypto exists and what methods are available
  const hasCrypto = !!window.crypto;
  const hasGetRandomValues = !!(window.crypto && window.crypto.getRandomValues);
  const hasSubtle = !!(window.crypto && window.crypto.subtle);

  console.log('Crypto status:', { hasCrypto, hasGetRandomValues, hasSubtle });

  // If crypto doesn't exist, try to create it
  if (!hasCrypto) {
    try {
      console.warn('window.crypto not available, attempting to create polyfill');
      Object.defineProperty(window, 'crypto', {
        value: {
          getRandomValues,
          subtle: createSubtleCrypto()
        },
        writable: false,
        configurable: true
      });
      console.log('✅ Created window.crypto polyfill');
    } catch (e) {
      console.error('❌ Failed to create window.crypto:', e.message);
    }
  } else {
    // Crypto exists, but check if methods work
    if (!hasGetRandomValues) {
      try {
        console.warn('getRandomValues not available, attempting to add polyfill');
        Object.defineProperty(window.crypto, 'getRandomValues', {
          value: getRandomValues,
          writable: false,
          configurable: true
        });
        console.log('✅ Added getRandomValues polyfill');
      } catch (e) {
        console.error('❌ Failed to add getRandomValues:', e.message);
      }
    } else {
      // Test if getRandomValues actually works
      try {
        const testArray = new Uint8Array(1);
        window.crypto.getRandomValues(testArray);
        console.log('✅ Native getRandomValues works');
      } catch (e) {
        console.warn('❌ Native getRandomValues failed, but cannot replace it:', e.message);
      }
    }

    if (!hasSubtle) {
      try {
        console.warn('subtle crypto not available, attempting to add polyfill');
        Object.defineProperty(window.crypto, 'subtle', {
          value: createSubtleCrypto(),
          writable: false,
          configurable: true
        });
        console.log('✅ Added subtle crypto polyfill');
      } catch (e) {
        console.error('❌ Failed to add subtle crypto:', e.message);
      }
    }
  }

  // Final status check
  console.log('Final crypto status:', {
    crypto: !!window.crypto,
    getRandomValues: !!(window.crypto && window.crypto.getRandomValues),
    subtle: !!(window.crypto && window.crypto.subtle)
  });
}

export {};
