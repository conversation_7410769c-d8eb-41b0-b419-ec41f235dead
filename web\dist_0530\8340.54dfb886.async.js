"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8340],{83832:function(de,M,r){r.d(M,{S:function(){return W}});var e=r(1413),h=r(91),E=r(74330),G=r(67294),N=r(85893),U=["isLoading","pastDelay","timedOut","error","retry"],W=function(D){var K=D.isLoading,T=D.pastDelay,o=D.timedOut,u=D.error,v=D.retry,g=(0,h.Z)(D,U);return(0,N.jsx)("div",{style:{paddingBlockStart:100,textAlign:"center"},children:(0,N.jsx)(E.Z,(0,e.Z)({size:"large"},g))})}},76509:function(de,M,r){r.d(M,{X:function(){return h}});var e=r(67294),h=(0,e.createContext)({})},87646:function(de,M,r){r.r(M),r.d(M,{blue:function(){return Q},blueDark:function(){return k},cyan:function(){return ue},cyanDark:function(){return p},geekblue:function(){return te},geekblueDark:function(){return b},generate:function(){return u},gold:function(){return F},goldDark:function(){return s},gray:function(){return he},green:function(){return ae},greenDark:function(){return c},grey:function(){return V},greyDark:function(){return O},lime:function(){return X},limeDark:function(){return n},magenta:function(){return oe},magentaDark:function(){return $},orange:function(){return B},orangeDark:function(){return t},presetDarkPalettes:function(){return Y},presetPalettes:function(){return pe},presetPrimaryColors:function(){return v},purple:function(){return ne},purpleDark:function(){return j},red:function(){return g},redDark:function(){return J},volcano:function(){return A},volcanoDark:function(){return se},yellow:function(){return re},yellowDark:function(){return i}});var e=r(15063),h=2,E=.16,G=.05,N=.05,U=.15,W=5,H=4,D=[{index:7,amount:15},{index:6,amount:25},{index:5,amount:30},{index:5,amount:45},{index:5,amount:65},{index:5,amount:85},{index:4,amount:90},{index:3,amount:95},{index:2,amount:97},{index:1,amount:98}];function K(l,d,m){var a;return Math.round(l.h)>=60&&Math.round(l.h)<=240?a=m?Math.round(l.h)-h*d:Math.round(l.h)+h*d:a=m?Math.round(l.h)+h*d:Math.round(l.h)-h*d,a<0?a+=360:a>=360&&(a-=360),a}function T(l,d,m){if(l.h===0&&l.s===0)return l.s;var a;return m?a=l.s-E*d:d===H?a=l.s+E:a=l.s+G*d,a>1&&(a=1),m&&d===W&&a>.1&&(a=.1),a<.06&&(a=.06),Math.round(a*100)/100}function o(l,d,m){var a;return m?a=l.v+N*d:a=l.v-U*d,a=Math.max(0,Math.min(1,a)),Math.round(a*100)/100}function u(l){for(var d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},m=[],a=new e.t(l),S=a.toHsv(),P=W;P>0;P-=1){var f=new e.t({h:K(S,P,!0),s:T(S,P,!0),v:o(S,P,!0)});m.push(f)}m.push(a);for(var C=1;C<=H;C+=1){var Z=new e.t({h:K(S,C),s:T(S,C),v:o(S,C)});m.push(Z)}return d.theme==="dark"?D.map(function(w){var _=w.index,ie=w.amount;return new e.t(d.backgroundColor||"#141414").mix(m[_],ie).toHexString()}):m.map(function(w){return w.toHexString()})}var v={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1677FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},g=["#fff1f0","#ffccc7","#ffa39e","#ff7875","#ff4d4f","#f5222d","#cf1322","#a8071a","#820014","#5c0011"];g.primary=g[5];var A=["#fff2e8","#ffd8bf","#ffbb96","#ff9c6e","#ff7a45","#fa541c","#d4380d","#ad2102","#871400","#610b00"];A.primary=A[5];var B=["#fff7e6","#ffe7ba","#ffd591","#ffc069","#ffa940","#fa8c16","#d46b08","#ad4e00","#873800","#612500"];B.primary=B[5];var F=["#fffbe6","#fff1b8","#ffe58f","#ffd666","#ffc53d","#faad14","#d48806","#ad6800","#874d00","#613400"];F.primary=F[5];var re=["#feffe6","#ffffb8","#fffb8f","#fff566","#ffec3d","#fadb14","#d4b106","#ad8b00","#876800","#614700"];re.primary=re[5];var X=["#fcffe6","#f4ffb8","#eaff8f","#d3f261","#bae637","#a0d911","#7cb305","#5b8c00","#3f6600","#254000"];X.primary=X[5];var ae=["#f6ffed","#d9f7be","#b7eb8f","#95de64","#73d13d","#52c41a","#389e0d","#237804","#135200","#092b00"];ae.primary=ae[5];var ue=["#e6fffb","#b5f5ec","#87e8de","#5cdbd3","#36cfc9","#13c2c2","#08979c","#006d75","#00474f","#002329"];ue.primary=ue[5];var Q=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];Q.primary=Q[5];var te=["#f0f5ff","#d6e4ff","#adc6ff","#85a5ff","#597ef7","#2f54eb","#1d39c4","#10239e","#061178","#030852"];te.primary=te[5];var ne=["#f9f0ff","#efdbff","#d3adf7","#b37feb","#9254de","#722ed1","#531dab","#391085","#22075e","#120338"];ne.primary=ne[5];var oe=["#fff0f6","#ffd6e7","#ffadd2","#ff85c0","#f759ab","#eb2f96","#c41d7f","#9e1068","#780650","#520339"];oe.primary=oe[5];var V=["#a6a6a6","#999999","#8c8c8c","#808080","#737373","#666666","#404040","#1a1a1a","#000000","#000000"];V.primary=V[5];var he=V,pe={red:g,volcano:A,orange:B,gold:F,yellow:re,lime:X,green:ae,cyan:ue,blue:Q,geekblue:te,purple:ne,magenta:oe,grey:V},J=["#2a1215","#431418","#58181c","#791a1f","#a61d24","#d32029","#e84749","#f37370","#f89f9a","#fac8c3"];J.primary=J[5];var se=["#2b1611","#441d12","#592716","#7c3118","#aa3e19","#d84a1b","#e87040","#f3956a","#f8b692","#fad4bc"];se.primary=se[5];var t=["#2b1d11","#442a11","#593815","#7c4a15","#aa6215","#d87a16","#e89a3c","#f3b765","#f8cf8d","#fae3b7"];t.primary=t[5];var s=["#2b2111","#443111","#594214","#7c5914","#aa7714","#d89614","#e8b339","#f3cc62","#f8df8b","#faedb5"];s.primary=s[5];var i=["#2b2611","#443b11","#595014","#7c6e14","#aa9514","#d8bd14","#e8d639","#f3ea62","#f8f48b","#fafab5"];i.primary=i[5];var n=["#1f2611","#2e3c10","#3e4f13","#536d13","#6f9412","#8bbb11","#a9d134","#c9e75d","#e4f88b","#f0fab5"];n.primary=n[5];var c=["#162312","#1d3712","#274916","#306317","#3c8618","#49aa19","#6abe39","#8fd460","#b2e58b","#d5f2bb"];c.primary=c[5];var p=["#112123","#113536","#144848","#146262","#138585","#13a8a8","#33bcb7","#58d1c9","#84e2d8","#b2f1e8"];p.primary=p[5];var k=["#111a2c","#112545","#15325b","#15417e","#1554ad","#1668dc","#3c89e8","#65a9f3","#8dc5f8","#b7dcfa"];k.primary=k[5];var b=["#131629","#161d40","#1c2755","#203175","#263ea0","#2b4acb","#5273e0","#7f9ef3","#a8c1f8","#d2e0fa"];b.primary=b[5];var j=["#1a1325","#24163a","#301c4d","#3e2069","#51258f","#642ab5","#854eca","#ab7ae0","#cda8f0","#ebd7fa"];j.primary=j[5];var $=["#291321","#40162f","#551c3b","#75204f","#a02669","#cb2b83","#e0529c","#f37fb7","#f8a8cc","#fad2e3"];$.primary=$[5];var O=["#151515","#1f1f1f","#2d2d2d","#393939","#494949","#5a5a5a","#6a6a6a","#7b7b7b","#888888","#969696"];O.primary=O[5];var Y={red:J,volcano:se,orange:t,gold:s,yellow:i,lime:n,green:c,cyan:p,blue:k,geekblue:b,purple:j,magenta:$,grey:O}},1977:function(de,M,r){r.d(M,{n:function(){return K}});var e=r(97685),h=r(71002),E=/^[v^~<>=]*?(\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+))?(?:-([\da-z\-]+(?:\.[\da-z\-]+)*))?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i,G=function(o){return o==="*"||o==="x"||o==="X"},N=function(o){var u=parseInt(o,10);return isNaN(u)?o:u},U=function(o,u){return(0,h.Z)(o)!==(0,h.Z)(u)?[String(o),String(u)]:[o,u]},W=function(o,u){if(G(o)||G(u))return 0;var v=U(N(o),N(u)),g=(0,e.Z)(v,2),A=g[0],B=g[1];return A>B?1:A<B?-1:0},H=function(o,u){for(var v=0;v<Math.max(o.length,u.length);v++){var g=W(o[v]||"0",u[v]||"0");if(g!==0)return g}return 0},D=function(o){var u,v=o.match(E);return v==null||(u=v.shift)===null||u===void 0||u.call(v),v},K=function(o,u){var v=D(o),g=D(u),A=v.pop(),B=g.pop(),F=H(v,g);return F!==0?F:A||B?A?-1:1:0}},12044:function(de,M,r){r.d(M,{j:function(){return E}});var e=r(34155),h=typeof e!="undefined"&&e.versions!=null&&e.versions.node!=null,E=function(){return typeof window!="undefined"&&typeof window.document!="undefined"&&typeof window.matchMedia!="undefined"&&!h}},85357:function(de,M,r){r.d(M,{Z:function(){return se}});var e=r(67294),h=r(93967),E=r.n(h),G=r(9220),N=r(42550),U=r(74443),W=r(53124),H=r(35792),D=r(98675),K=r(25378),o=e.createContext({}),u=r(11568),v=r(14747),g=r(83559),A=r(83262);const B=t=>{const{antCls:s,componentCls:i,iconCls:n,avatarBg:c,avatarColor:p,containerSize:k,containerSizeLG:b,containerSizeSM:j,textFontSize:$,textFontSizeLG:O,textFontSizeSM:Y,borderRadius:l,borderRadiusLG:d,borderRadiusSM:m,lineWidth:a,lineType:S}=t,P=(f,C,Z)=>({width:f,height:f,borderRadius:"50%",[`&${i}-square`]:{borderRadius:Z},[`&${i}-icon`]:{fontSize:C,[`> ${n}`]:{margin:0}}});return{[i]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,v.Wf)(t)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:p,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:c,border:`${(0,u.bf)(a)} ${S} transparent`,"&-image":{background:"transparent"},[`${s}-image-img`]:{display:"block"}}),P(k,$,l)),{"&-lg":Object.assign({},P(b,O,d)),"&-sm":Object.assign({},P(j,Y,m)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},F=t=>{const{componentCls:s,groupBorderColor:i,groupOverlapping:n,groupSpace:c}=t;return{[`${s}-group`]:{display:"inline-flex",[s]:{borderColor:i},"> *:not(:first-child)":{marginInlineStart:n}},[`${s}-group-popover`]:{[`${s} + ${s}`]:{marginInlineStart:c}}}},re=t=>{const{controlHeight:s,controlHeightLG:i,controlHeightSM:n,fontSize:c,fontSizeLG:p,fontSizeXL:k,fontSizeHeading3:b,marginXS:j,marginXXS:$,colorBorderBg:O}=t;return{containerSize:s,containerSizeLG:i,containerSizeSM:n,textFontSize:Math.round((p+k)/2),textFontSizeLG:b,textFontSizeSM:c,groupSpace:$,groupOverlapping:-j,groupBorderColor:O}};var X=(0,g.I$)("Avatar",t=>{const{colorTextLightSolid:s,colorTextPlaceholder:i}=t,n=(0,A.IX)(t,{avatarBg:i,avatarColor:s});return[B(n),F(n)]},re),ae=function(t,s){var i={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&s.indexOf(n)<0&&(i[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,n=Object.getOwnPropertySymbols(t);c<n.length;c++)s.indexOf(n[c])<0&&Object.prototype.propertyIsEnumerable.call(t,n[c])&&(i[n[c]]=t[n[c]]);return i},Q=e.forwardRef((t,s)=>{const{prefixCls:i,shape:n,size:c,src:p,srcSet:k,icon:b,className:j,rootClassName:$,style:O,alt:Y,draggable:l,children:d,crossOrigin:m,gap:a=4,onError:S}=t,P=ae(t,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","style","alt","draggable","children","crossOrigin","gap","onError"]),[f,C]=e.useState(1),[Z,w]=e.useState(!1),[_,ie]=e.useState(!0),ve=e.useRef(null),q=e.useRef(null),fe=(0,N.sQ)(s,ve),{getPrefixCls:ee,avatar:R}=e.useContext(W.E_),L=e.useContext(o),ce=()=>{if(!q.current||!ve.current)return;const x=q.current.offsetWidth,y=ve.current.offsetWidth;x!==0&&y!==0&&a*2<y&&C(y-a*2<x?(y-a*2)/x:1)};e.useEffect(()=>{w(!0)},[]),e.useEffect(()=>{ie(!0),C(1)},[p]),e.useEffect(ce,[a]);const be=()=>{(S==null?void 0:S())!==!1&&ie(!1)},z=(0,D.Z)(x=>{var y,me;return(me=(y=c!=null?c:L==null?void 0:L.size)!==null&&y!==void 0?y:x)!==null&&me!==void 0?me:"default"}),ye=Object.keys(typeof z=="object"?z||{}:{}).some(x=>["xs","sm","md","lg","xl","xxl"].includes(x)),ge=(0,K.Z)(ye),xe=e.useMemo(()=>{if(typeof z!="object")return{};const x=U.c4.find(me=>ge[me]),y=z[x];return y?{width:y,height:y,fontSize:y&&(b||d)?y/2:18}:{}},[ge,z]),I=ee("avatar",i),Se=(0,H.Z)(I),[Ee,De,Oe]=X(I,Se),Pe=E()({[`${I}-lg`]:z==="large",[`${I}-sm`]:z==="small"}),Ce=e.isValidElement(p),ze=n||(L==null?void 0:L.shape)||"circle",Me=E()(I,Pe,R==null?void 0:R.className,`${I}-${ze}`,{[`${I}-image`]:Ce||p&&_,[`${I}-icon`]:!!b},Oe,Se,j,$,De),Ae=typeof z=="number"?{width:z,height:z,fontSize:b?z/2:18}:{};let le;if(typeof p=="string"&&_)le=e.createElement("img",{src:p,draggable:l,srcSet:k,onError:be,alt:Y,crossOrigin:m});else if(Ce)le=p;else if(b)le=b;else if(Z||f!==1){const x=`scale(${f})`,y={msTransform:x,WebkitTransform:x,transform:x};le=e.createElement(G.Z,{onResize:ce},e.createElement("span",{className:`${I}-string`,ref:q,style:Object.assign({},y)},d))}else le=e.createElement("span",{className:`${I}-string`,style:{opacity:0},ref:q},d);return Ee(e.createElement("span",Object.assign({},P,{style:Object.assign(Object.assign(Object.assign(Object.assign({},Ae),xe),R==null?void 0:R.style),O),className:Me,ref:fe}),le))}),te=r(50344),ne=r(96159),oe=r(55241);const V=t=>{const{size:s,shape:i}=e.useContext(o),n=e.useMemo(()=>({size:t.size||s,shape:t.shape||i}),[t.size,t.shape,s,i]);return e.createElement(o.Provider,{value:n},t.children)};var pe=t=>{var s,i,n,c;const{getPrefixCls:p,direction:k}=e.useContext(W.E_),{prefixCls:b,className:j,rootClassName:$,style:O,maxCount:Y,maxStyle:l,size:d,shape:m,maxPopoverPlacement:a,maxPopoverTrigger:S,children:P,max:f}=t,C=p("avatar",b),Z=`${C}-group`,w=(0,H.Z)(C),[_,ie,ve]=X(C,w),q=E()(Z,{[`${Z}-rtl`]:k==="rtl"},ve,w,j,$,ie),fe=(0,te.Z)(P).map((L,ce)=>(0,ne.Tm)(L,{key:`avatar-key-${ce}`})),ee=(f==null?void 0:f.count)||Y,R=fe.length;if(ee&&ee<R){const L=fe.slice(0,ee),ce=fe.slice(ee,R),be=(f==null?void 0:f.style)||l,z=((s=f==null?void 0:f.popover)===null||s===void 0?void 0:s.trigger)||S||"hover",ye=((i=f==null?void 0:f.popover)===null||i===void 0?void 0:i.placement)||a||"top",ge=Object.assign(Object.assign({content:ce},f==null?void 0:f.popover),{classNames:{root:E()(`${Z}-popover`,(c=(n=f==null?void 0:f.popover)===null||n===void 0?void 0:n.classNames)===null||c===void 0?void 0:c.root)},placement:ye,trigger:z});return L.push(e.createElement(oe.Z,Object.assign({key:"avatar-popover-key",destroyOnHidden:!0},ge),e.createElement(Q,{style:be},`+${R-ee}`))),_(e.createElement(V,{shape:m,size:d},e.createElement("div",{className:q,style:O},L)))}return _(e.createElement(V,{shape:m,size:d},e.createElement("div",{className:q,style:O},fe)))};const J=Q;J.Group=pe;var se=J}}]);
