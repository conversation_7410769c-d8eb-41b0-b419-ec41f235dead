!(function(){var at=(ve,ne)=>(ne=Symbol[ve])?ne:Symbol.for("Symbol."+ve),gi=ve=>{throw TypeError(ve)};var hi=function(ve,ne){this[0]=ve,this[1]=ne};var it=ve=>{var ne=ve[at("asyncIterator")],f=!1,l,K={};return ne==null?(ne=ve[at("iterator")](),l=k=>K[k]=G=>ne[k](G)):(ne=ne.call(ve),l=k=>K[k]=G=>{if(f){if(f=!1,k==="throw")throw G;return G}return f=!0,{done:!1,value:new hi(new Promise(F=>{var d=ne[k](G);d instanceof Object||gi("Object expected"),F(d)}),1)}}),K[at("iterator")]=()=>K,l("next"),"throw"in ne?l("throw"):K.throw=k=>{throw k},"return"in ne&&l("return"),K};(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4265],{2354:function(ve,ne,f){"use strict";f.d(ne,{f:function(){return qa}});var l=f(4942),K=f(74165),k=f(15861),G=f(91),F=f(97685),d=f(1413),te=f(10915),Q=f(21770),m=f(67294);function $(r){var e=typeof window=="undefined",n=(0,m.useState)(function(){return e?!1:window.matchMedia(r).matches}),t=(0,F.Z)(n,2),o=t[0],i=t[1];return(0,m.useLayoutEffect)(function(){if(!e){var a=window.matchMedia(r),c=function(p){return i(p.matches)};return a.addListener(c),function(){return a.removeListener(c)}}},[r]),o}var Y={xs:{maxWidth:575,matchMedia:"(max-width: 575px)"},sm:{minWidth:576,maxWidth:767,matchMedia:"(min-width: 576px) and (max-width: 767px)"},md:{minWidth:768,maxWidth:991,matchMedia:"(min-width: 768px) and (max-width: 991px)"},lg:{minWidth:992,maxWidth:1199,matchMedia:"(min-width: 992px) and (max-width: 1199px)"},xl:{minWidth:1200,maxWidth:1599,matchMedia:"(min-width: 1200px) and (max-width: 1599px)"},xxl:{minWidth:1600,matchMedia:"(min-width: 1600px)"}},de=function(){var e=void 0;if(typeof window=="undefined")return e;var n=Object.keys(Y).find(function(t){var o=Y[t].matchMedia;return!!window.matchMedia(o).matches});return e=n,e},me=function(){var e=$(Y.md.matchMedia),n=$(Y.lg.matchMedia),t=$(Y.xxl.matchMedia),o=$(Y.xl.matchMedia),i=$(Y.sm.matchMedia),a=$(Y.xs.matchMedia),c=(0,m.useState)(de()),u=(0,F.Z)(c,2),p=u[0],v=u[1];return(0,m.useEffect)(function(){if(t){v("xxl");return}if(o){v("xl");return}if(n){v("lg");return}if(e){v("md");return}if(i){v("sm");return}if(a){v("xs");return}v("md")},[e,n,t,o,i,a]),p},ee=f(12044);function P(r,e){var n=typeof r.pageName=="string"?r.title:e;(0,m.useEffect)(function(){(0,ee.j)()&&n&&(document.title=n)},[r.title,n])}var E=f(1977),M=f(73177);function X(r){if((0,E.n)((0,M.b)(),"5.6.0")<0)return r;var e={colorGroupTitle:"groupTitleColor",radiusItem:"itemBorderRadius",radiusSubMenuItem:"subMenuItemBorderRadius",colorItemText:"itemColor",colorItemTextHover:"itemHoverColor",colorItemTextHoverHorizontal:"horizontalItemHoverColor",colorItemTextSelected:"itemSelectedColor",colorItemTextSelectedHorizontal:"horizontalItemSelectedColor",colorItemTextDisabled:"itemDisabledColor",colorDangerItemText:"dangerItemColor",colorDangerItemTextHover:"dangerItemHoverColor",colorDangerItemTextSelected:"dangerItemSelectedColor",colorDangerItemBgActive:"dangerItemActiveBg",colorDangerItemBgSelected:"dangerItemSelectedBg",colorItemBg:"itemBg",colorItemBgHover:"itemHoverBg",colorSubItemBg:"subMenuItemBg",colorItemBgActive:"itemActiveBg",colorItemBgSelected:"itemSelectedBg",colorItemBgSelectedHorizontal:"horizontalItemSelectedBg",colorActiveBarWidth:"activeBarWidth",colorActiveBarHeight:"activeBarHeight",colorActiveBarBorderSize:"activeBarBorderWidth"},n=(0,d.Z)({},r);return Object.keys(e).forEach(function(t){n[t]!==void 0&&(n[e[t]]=n[t],delete n[t])}),n}var ce=f(90743);function J(r,e){return e>>>r|e<<32-r}function oe(r,e,n){return r&e^~r&n}function se(r,e,n){return r&e^r&n^e&n}function q(r){return J(2,r)^J(13,r)^J(22,r)}function _(r){return J(6,r)^J(11,r)^J(25,r)}function C(r){return J(7,r)^J(18,r)^r>>>3}function g(r){return J(17,r)^J(19,r)^r>>>10}function O(r,e){return r[e&15]+=g(r[e+14&15])+r[e+9&15]+C(r[e+1&15])}var D=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],S,H,I,pe="0123456789abcdef";function ge(r,e){var n=(r&65535)+(e&65535),t=(r>>16)+(e>>16)+(n>>16);return t<<16|n&65535}function Ie(){S=new Array(8),H=new Array(2),I=new Array(64),H[0]=H[1]=0,S[0]=1779033703,S[1]=3144134277,S[2]=1013904242,S[3]=2773480762,S[4]=1359893119,S[5]=2600822924,S[6]=528734635,S[7]=1541459225}function Ce(){var r,e,n,t,o,i,a,c,u,p,v=new Array(16);r=S[0],e=S[1],n=S[2],t=S[3],o=S[4],i=S[5],a=S[6],c=S[7];for(var h=0;h<16;h++)v[h]=I[(h<<2)+3]|I[(h<<2)+2]<<8|I[(h<<2)+1]<<16|I[h<<2]<<24;for(var y=0;y<64;y++)u=c+_(o)+oe(o,i,a)+D[y],y<16?u+=v[y]:u+=O(v,y),p=q(r)+se(r,e,n),c=a,a=i,i=o,o=ge(t,u),t=n,n=e,e=r,r=ge(u,p);S[0]+=r,S[1]+=e,S[2]+=n,S[3]+=t,S[4]+=o,S[5]+=i,S[6]+=a,S[7]+=c}function Te(r,e){var n,t,o=0;t=H[0]>>3&63;var i=e&63;for((H[0]+=e<<3)<e<<3&&H[1]++,H[1]+=e>>29,n=0;n+63<e;n+=64){for(var a=t;a<64;a++)I[a]=r.charCodeAt(o++);Ce(),t=0}for(var c=0;c<i;c++)I[c]=r.charCodeAt(o++)}function Me(){var r=H[0]>>3&63;if(I[r++]=128,r<=56)for(var e=r;e<56;e++)I[e]=0;else{for(var n=r;n<64;n++)I[n]=0;Ce();for(var t=0;t<56;t++)I[t]=0}I[56]=H[1]>>>24&255,I[57]=H[1]>>>16&255,I[58]=H[1]>>>8&255,I[59]=H[1]&255,I[60]=H[0]>>>24&255,I[61]=H[0]>>>16&255,I[62]=H[0]>>>8&255,I[63]=H[0]&255,Ce()}function Oe(){for(var r=0,e=new Array(32),n=0;n<8;n++)e[r++]=S[n]>>>24&255,e[r++]=S[n]>>>16&255,e[r++]=S[n]>>>8&255,e[r++]=S[n]&255;return e}function Be(){for(var r=new String,e=0;e<8;e++)for(var n=28;n>=0;n-=4)r+=pe.charAt(S[e]>>>n&15);return r}function $e(r){return Ie(),Te(r,r.length),Me(),Be()}var on=$e;function Ke(r){"@babel/helpers - typeof";return Ke=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ke(r)}var On=["pro_layout_parentKeys","children","icon","flatMenu","indexRoute","routes"];function Nn(r,e){return an(r)||wn(r,e)||Bn(r,e)||Zn()}function Zn(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function wn(r,e){var n=r==null?null:typeof Symbol!="undefined"&&r[Symbol.iterator]||r["@@iterator"];if(n!=null){var t=[],o=!0,i=!1,a,c;try{for(n=n.call(r);!(o=(a=n.next()).done)&&(t.push(a.value),!(e&&t.length===e));o=!0);}catch(u){i=!0,c=u}finally{try{!o&&n.return!=null&&n.return()}finally{if(i)throw c}}return t}}function an(r){if(Array.isArray(r))return r}function ln(r,e){var n=typeof Symbol!="undefined"&&r[Symbol.iterator]||r["@@iterator"];if(!n){if(Array.isArray(r)||(n=Bn(r))||e&&r&&typeof r.length=="number"){n&&(r=n);var t=0,o=function(){};return{s:o,n:function(){return t>=r.length?{done:!0}:{done:!1,value:r[t++]}},e:function(p){throw p},f:o}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var i=!0,a=!1,c;return{s:function(){n=n.call(r)},n:function(){var p=n.next();return i=p.done,p},e:function(p){a=!0,c=p},f:function(){try{!i&&n.return!=null&&n.return()}finally{if(a)throw c}}}}function jn(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function lt(r,e){for(var n=0;n<e.length;n++){var t=e[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(r,t.key,t)}}function sr(r,e,n){return e&&lt(r.prototype,e),n&&lt(r,n),Object.defineProperty(r,"prototype",{writable:!1}),r}function cr(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&sn(r,e)}function ur(r){var e=st();return function(){var t=cn(r),o;if(e){var i=cn(this).constructor;o=Reflect.construct(t,arguments,i)}else o=t.apply(this,arguments);return dr(this,o)}}function dr(r,e){if(e&&(Ke(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return mr(r)}function mr(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function Ln(r){var e=typeof Map=="function"?new Map:void 0;return Ln=function(t){if(t===null||!pr(t))return t;if(typeof t!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e!="undefined"){if(e.has(t))return e.get(t);e.set(t,o)}function o(){return xn(t,arguments,cn(this).constructor)}return o.prototype=Object.create(t.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),sn(o,t)},Ln(r)}function xn(r,e,n){return st()?xn=Reflect.construct.bind():xn=function(o,i,a){var c=[null];c.push.apply(c,i);var u=Function.bind.apply(o,c),p=new u;return a&&sn(p,a.prototype),p},xn.apply(null,arguments)}function st(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(r){return!1}}function pr(r){return Function.toString.call(r).indexOf("[native code]")!==-1}function sn(r,e){return sn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,o){return t.__proto__=o,t},sn(r,e)}function cn(r){return cn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},cn(r)}function ct(r){return gr(r)||vr(r)||Bn(r)||fr()}function fr(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Bn(r,e){if(r){if(typeof r=="string")return Fn(r,e);var n=Object.prototype.toString.call(r).slice(8,-1);if(n==="Object"&&r.constructor&&(n=r.constructor.name),n==="Map"||n==="Set")return Array.from(r);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Fn(r,e)}}function vr(r){if(typeof Symbol!="undefined"&&r[Symbol.iterator]!=null||r["@@iterator"]!=null)return Array.from(r)}function gr(r){if(Array.isArray(r))return Fn(r)}function Fn(r,e){(e==null||e>r.length)&&(e=r.length);for(var n=0,t=new Array(e);n<e;n++)t[n]=r[n];return t}function hr(r,e){if(r==null)return{};var n=yr(r,e),t,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(r);for(o=0;o<i.length;o++)t=i[o],!(e.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(r,t)&&(n[t]=r[t])}return n}function yr(r,e){if(r==null)return{};var n={},t=Object.keys(r),o,i;for(i=0;i<t.length;i++)o=t[i],!(e.indexOf(o)>=0)&&(n[o]=r[o]);return n}function ut(r,e){var n=Object.keys(r);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(r);e&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(r,o).enumerable})),n.push.apply(n,t)}return n}function be(r){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?ut(Object(n),!0).forEach(function(t){Cr(r,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(n)):ut(Object(n)).forEach(function(t){Object.defineProperty(r,t,Object.getOwnPropertyDescriptor(n,t))})}return r}function Cr(r,e,n){return e in r?Object.defineProperty(r,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[e]=n,r}var Ze="routes";function un(r){return r.split("?")[0].split("#")[0]}var Wn=function(e){if(!e.startsWith("http"))return!1;try{var n=new URL(e);return!!n}catch(t){return!1}},xr=function(e){var n=e.path;if(!n||n==="/")try{return"/".concat(on(JSON.stringify(e)))}catch(t){}return n&&un(n)},br=function(e,n){var t=e.name,o=e.locale;return"locale"in e&&o===!1||!t?!1:e.locale||"".concat(n,".").concat(t)},dt=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"/";return e.endsWith("/*")?e.replace("/*","/"):(e||n).startsWith("/")||Wn(e)?e:"/".concat(n,"/").concat(e).replace(/\/\//g,"/").replace(/\/\//g,"/")},Sr=function(e,n){var t=e.menu,o=t===void 0?{}:t,i=e.indexRoute,a=e.path,c=a===void 0?"":a,u=e.children||[],p=o.name,v=p===void 0?e.name:p,h=o.icon,y=h===void 0?e.icon:h,R=o.hideChildren,j=R===void 0?e.hideChildren:R,N=o.flatMenu,T=N===void 0?e.flatMenu:N,V=i&&Object.keys(i).join(",")!=="redirect"?[be({path:c,menu:o},i)].concat(u||[]):u,B=be({},e);if(v&&(B.name=v),y&&(B.icon=y),V&&V.length){if(j)return delete B.children,B;var z=Hn(be(be({},n),{},{data:V}),e);if(T)return z;delete B[Ze]}return B},Xe=function(e){return Array.isArray(e)&&e.length>0};function Hn(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{path:"/"},n=r.data,t=r.formatMessage,o=r.parentName,i=r.locale;return!n||!Array.isArray(n)?[]:n.filter(function(a){return a?Xe(a.children)||a.path||a.originPath||a.layout?!0:(a.redirect||a.unaccessible,!1):!1}).filter(function(a){var c,u;return!(a==null||(c=a.menu)===null||c===void 0)&&c.name||a!=null&&a.flatMenu||!(a==null||(u=a.menu)===null||u===void 0)&&u.flatMenu?!0:a.menu!==!1}).map(function(a){var c=be(be({},a),{},{path:a.path||a.originPath});return!c.children&&c[Ze]&&(c.children=c[Ze],delete c[Ze]),c.unaccessible&&delete c.name,c.path==="*"&&(c.path="."),c.path==="/*"&&(c.path="."),!c.path&&c.originPath&&(c.path=c.originPath),c}).map(function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{path:"/"},c=a.children||a[Ze]||[],u=dt(a.path,e?e.path:"/"),p=a.name,v=br(a,o||"menu"),h=v!==!1&&i!==!1&&t&&v?t({id:v,defaultMessage:p}):p,y=e.pro_layout_parentKeys,R=y===void 0?[]:y,j=e.children,N=e.icon,T=e.flatMenu,V=e.indexRoute,B=e.routes,z=hr(e,On),Z=new Set([].concat(ct(R),ct(a.parentKeys||[])));e.key&&Z.add(e.key);var W=be(be(be({},z),{},{menu:void 0},a),{},{path:u,locale:v,key:a.key||xr(be(be({},a),{},{path:u})),pro_layout_parentKeys:Array.from(Z).filter(function(w){return w&&w!=="/"})});if(h?W.name=h:delete W.name,W.menu===void 0&&delete W.menu,Xe(c)){var b=Hn(be(be({},r),{},{data:c,parentName:v||""}),W);Xe(b)&&(W.children=b)}return Sr(W,r)}).flat(1)}var _r=function r(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return e.filter(function(n){return n&&(n.name||Xe(n.children))&&!n.hideInMenu&&!n.redirect}).map(function(n){var t=be({},n),o=t.children||n[Ze]||[];if(delete t[Ze],Xe(o)&&!t.hideChildrenInMenu&&o.some(function(a){return a&&!!a.name})){var i=r(o);if(i.length)return be(be({},t),{},{children:i})}return be({},n)}).filter(function(n){return n})},Pr=function(r){cr(n,r);var e=ur(n);function n(){return jn(this,n),e.apply(this,arguments)}return sr(n,[{key:"get",value:function(o){var i;try{var a=ln(this.entries()),c;try{for(a.s();!(c=a.n()).done;){var u=Nn(c.value,2),p=u[0],v=u[1],h=un(p);if(!Wn(p)&&(0,ce.Bo)(h,[]).test(o)){i=v;break}}}catch(y){a.e(y)}finally{a.f()}}catch(y){i=void 0}return i}}]),n}(Ln(Map)),Ir=function(e){var n=new Pr,t=function o(i,a){i.forEach(function(c){var u=c.children||c[Ze]||[];Xe(u)&&o(u,c);var p=dt(c.path,a?a.path:"/");n.set(un(p),c)})};return t(e),n},Mr=function r(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return e.map(function(n){var t=n.children||n[Ze];if(Xe(t)){var o=r(t);if(o.length)return be({},n)}var i=be({},n);return delete i[Ze],delete i.children,i}).filter(function(n){return n})},Er=function(e,n,t,o){var i=Hn({data:e,formatMessage:t,locale:n}),a=o?Mr(i):_r(i),c=Ir(i);return{breadcrumb:c,menuData:a}},Dr=Er;function mt(r,e){var n=Object.keys(r);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(r);e&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(r,o).enumerable})),n.push.apply(n,t)}return n}function dn(r){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?mt(Object(n),!0).forEach(function(t){Rr(r,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(n)):mt(Object(n)).forEach(function(t){Object.defineProperty(r,t,Object.getOwnPropertyDescriptor(n,t))})}return r}function Rr(r,e,n){return e in r?Object.defineProperty(r,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[e]=n,r}var Tr=function r(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n={};return e.forEach(function(t){var o=dn({},t);if(!(!o||!o.key)){!o.children&&o[Ze]&&(o.children=o[Ze],delete o[Ze]);var i=o.children||[];n[un(o.path||o.key||"/")]=dn({},o),n[o.key||o.path||"/"]=dn({},o),i&&(n=dn(dn({},n),r(i)))}}),n},Ar=Tr,Or=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n=arguments.length>1?arguments[1]:void 0,t=arguments.length>2?arguments[2]:void 0;return e.filter(function(o){if(o==="/"&&n==="/")return!0;if(o!=="/"&&o!=="/*"&&o&&!Wn(o)){var i=un(o);try{if(t&&(0,ce.Bo)("".concat(i)).test(n)||(0,ce.Bo)("".concat(i),[]).test(n)||(0,ce.Bo)("".concat(i,"/(.*)")).test(n))return!0}catch(a){}}return!1}).sort(function(o,i){return o===n?10:i===n?-10:o.substr(1).split("/").length-i.substr(1).split("/").length})},Nr=function(e,n,t,o){var i=Ar(n),a=Object.keys(i),c=Or(a,e||"/",o);return!c||c.length<1?[]:(t||(c=[c[c.length-1]]),c.map(function(u){var p=i[u]||{pro_layout_parentKeys:"",key:""},v=new Map,h=(p.pro_layout_parentKeys||[]).map(function(y){return v.has(y)?null:(v.set(y,!0),i[y])}).filter(function(y){return y});return p.key&&h.push(p),h}).flat(1))},Zr=Nr,Fe=f(21532),Je=f(26058),wr=f(93967),le=f.n(wr),pt=f(98423),ft=f(80334),jr=f(5068),Lr=f(25269),Br=f(78164),s=f(85893),Fr=function(e){var n=(0,m.useContext)(te.L_),t=n.hashId,o=e.style,i=e.prefixCls,a=e.children,c=e.hasPageContainer,u=c===void 0?0:c,p=le()("".concat(i,"-content"),t,(0,l.Z)((0,l.Z)({},"".concat(i,"-has-header"),e.hasHeader),"".concat(i,"-content-has-page-container"),u>0)),v=e.ErrorBoundary||Br.S;return e.ErrorBoundary===!1?(0,s.jsx)(Je.Z.Content,{className:p,style:o,children:a}):(0,s.jsx)(v,{children:(0,s.jsx)(Je.Z.Content,{className:p,style:o,children:a})})},Wr=function(){return(0,s.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 200 200",children:[(0,s.jsxs)("defs",{children:[(0,s.jsxs)("linearGradient",{x1:"62.1023273%",y1:"0%",x2:"108.19718%",y2:"37.8635764%",id:"linearGradient-1",children:[(0,s.jsx)("stop",{stopColor:"#4285EB",offset:"0%"}),(0,s.jsx)("stop",{stopColor:"#2EC7FF",offset:"100%"})]}),(0,s.jsxs)("linearGradient",{x1:"69.644116%",y1:"0%",x2:"54.0428975%",y2:"108.456714%",id:"linearGradient-2",children:[(0,s.jsx)("stop",{stopColor:"#29CDFF",offset:"0%"}),(0,s.jsx)("stop",{stopColor:"#148EFF",offset:"37.8600687%"}),(0,s.jsx)("stop",{stopColor:"#0A60FF",offset:"100%"})]}),(0,s.jsxs)("linearGradient",{x1:"69.6908165%",y1:"-12.9743587%",x2:"16.7228981%",y2:"117.391248%",id:"linearGradient-3",children:[(0,s.jsx)("stop",{stopColor:"#FA816E",offset:"0%"}),(0,s.jsx)("stop",{stopColor:"#F74A5C",offset:"41.472606%"}),(0,s.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]}),(0,s.jsxs)("linearGradient",{x1:"68.1279872%",y1:"-35.6905737%",x2:"30.4400914%",y2:"114.942679%",id:"linearGradient-4",children:[(0,s.jsx)("stop",{stopColor:"#FA8E7D",offset:"0%"}),(0,s.jsx)("stop",{stopColor:"#F74A5C",offset:"51.2635191%"}),(0,s.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]})]}),(0,s.jsx)("g",{stroke:"none",strokeWidth:1,fill:"none",fillRule:"evenodd",children:(0,s.jsx)("g",{transform:"translate(-20.000000, -20.000000)",children:(0,s.jsx)("g",{transform:"translate(20.000000, 20.000000)",children:(0,s.jsxs)("g",{children:[(0,s.jsxs)("g",{fillRule:"nonzero",children:[(0,s.jsxs)("g",{children:[(0,s.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C99.2571609,26.9692191 101.032305,26.9692191 102.20193,28.1378823 L129.985225,55.8983314 C134.193707,60.1033528 141.017005,60.1033528 145.225487,55.8983314 C149.433969,51.69331 149.433969,44.8756232 145.225487,40.6706018 L108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-1)"}),(0,s.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C100.999864,25.6271836 105.751642,20.541824 112.729652,19.3524487 C117.915585,18.4685261 123.585219,20.4140239 129.738554,25.1889424 C125.624663,21.0784292 118.571995,14.0340304 108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-2)"})]}),(0,s.jsx)("path",{d:"M153.685633,135.854579 C157.894115,140.0596 164.717412,140.0596 168.925894,135.854579 L195.959977,108.842726 C200.659183,104.147384 200.659183,96.5636133 195.960527,91.8688194 L168.690777,64.7181159 C164.472332,60.5180858 157.646868,60.5241425 153.435895,64.7316526 C149.227413,68.936674 149.227413,75.7543607 153.435895,79.9593821 L171.854035,98.3623765 C173.02366,99.5310396 173.02366,101.304724 171.854035,102.473387 L153.685633,120.626849 C149.47715,124.83187 149.47715,131.649557 153.685633,135.854579 Z",fill:"url(#linearGradient-3)"})]}),(0,s.jsx)("ellipse",{fill:"url(#linearGradient-4)",cx:"100.519339",cy:"100.436681",rx:"23.6001926",ry:"23.580786"})]})})})})]})},Qe=f(87462),Hr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm5.6-532.7c53 0 89 33.8 93 83.4.3 4.2 3.8 7.4 8 7.4h56.7c2.6 0 4.7-2.1 4.7-4.7 0-86.7-68.4-147.4-162.7-147.4C407.4 290 344 364.2 344 486.8v52.3C344 660.8 407.4 734 517.3 734c94 0 162.7-58.8 162.7-141.4 0-2.6-2.1-4.7-4.7-4.7h-56.8c-4.2 0-7.6 3.2-8 7.3-4.2 46.1-40.1 77.8-93 77.8-65.3 0-102.1-47.9-102.1-133.6v-52.6c.1-87 37-135.5 102.2-135.5z"}}]},name:"copyright",theme:"outlined"},Ur=Hr,vt=f(87646),$r=(0,m.createContext)({}),Un=$r,$n=f(71002),Vr=f(44958),zr=f(27571);function kr(r){return r.replace(/-(.)/g,function(e,n){return n.toUpperCase()})}function Vn(r,e){(0,ft.ZP)(r,"[@ant-design/icons] ".concat(e))}function gt(r){return(0,$n.Z)(r)==="object"&&typeof r.name=="string"&&typeof r.theme=="string"&&((0,$n.Z)(r.icon)==="object"||typeof r.icon=="function")}function ht(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return Object.keys(r).reduce(function(e,n){var t=r[n];switch(n){case"class":e.className=t,delete e.class;break;default:delete e[n],e[kr(n)]=t}return e},{})}function zn(r,e,n){return n?m.createElement(r.tag,(0,d.Z)((0,d.Z)({key:e},ht(r.attrs)),n),(r.children||[]).map(function(t,o){return zn(t,"".concat(e,"-").concat(r.tag,"-").concat(o))})):m.createElement(r.tag,(0,d.Z)({key:e},ht(r.attrs)),(r.children||[]).map(function(t,o){return zn(t,"".concat(e,"-").concat(r.tag,"-").concat(o))}))}function yt(r){return(0,vt.generate)(r)[0]}function Ct(r){return r?Array.isArray(r)?r:[r]:[]}var Gr={width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",focusable:"false"},Kr=`
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`,xt=function(e){var n=(0,m.useContext)(Un),t=n.csp,o=n.prefixCls,i=n.layer,a=Kr;o&&(a=a.replace(/anticon/g,o)),i&&(a="@layer ".concat(i,` {
`).concat(a,`
}`)),(0,m.useEffect)(function(){var c=e.current,u=(0,zr.A)(c);(0,Vr.hq)(a,"@ant-design-icons",{prepend:!i,csp:t,attachTo:u})},[])},Xr=["icon","className","onClick","style","primaryColor","secondaryColor"],mn={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};function Jr(r){var e=r.primaryColor,n=r.secondaryColor;mn.primaryColor=e,mn.secondaryColor=n||yt(e),mn.calculated=!!n}function Qr(){return(0,d.Z)({},mn)}var bn=function(e){var n=e.icon,t=e.className,o=e.onClick,i=e.style,a=e.primaryColor,c=e.secondaryColor,u=(0,G.Z)(e,Xr),p=m.useRef(),v=mn;if(a&&(v={primaryColor:a,secondaryColor:c||yt(a)}),xt(p),Vn(gt(n),"icon should be icon definiton, but got ".concat(n)),!gt(n))return null;var h=n;return h&&typeof h.icon=="function"&&(h=(0,d.Z)((0,d.Z)({},h),{},{icon:h.icon(v.primaryColor,v.secondaryColor)})),zn(h.icon,"svg-".concat(h.name),(0,d.Z)((0,d.Z)({className:t,onClick:o,style:i,"data-icon":h.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},u),{},{ref:p}))};bn.displayName="IconReact",bn.getTwoToneColors=Qr,bn.setTwoToneColors=Jr;var kn=bn;function bt(r){var e=Ct(r),n=(0,F.Z)(e,2),t=n[0],o=n[1];return kn.setTwoToneColors({primaryColor:t,secondaryColor:o})}function Yr(){var r=kn.getTwoToneColors();return r.calculated?[r.primaryColor,r.secondaryColor]:r.primaryColor}var qr=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];bt(vt.blue.primary);var Sn=m.forwardRef(function(r,e){var n=r.className,t=r.icon,o=r.spin,i=r.rotate,a=r.tabIndex,c=r.onClick,u=r.twoToneColor,p=(0,G.Z)(r,qr),v=m.useContext(Un),h=v.prefixCls,y=h===void 0?"anticon":h,R=v.rootClassName,j=le()(R,y,(0,l.Z)((0,l.Z)({},"".concat(y,"-").concat(t.name),!!t.name),"".concat(y,"-spin"),!!o||t.name==="loading"),n),N=a;N===void 0&&c&&(N=-1);var T=i?{msTransform:"rotate(".concat(i,"deg)"),transform:"rotate(".concat(i,"deg)")}:void 0,V=Ct(u),B=(0,F.Z)(V,2),z=B[0],Z=B[1];return m.createElement("span",(0,Qe.Z)({role:"img","aria-label":t.name},p,{ref:e,tabIndex:N,onClick:c,className:j}),m.createElement(kn,{icon:t,primaryColor:z,secondaryColor:Z,style:T}))});Sn.displayName="AntdIcon",Sn.getTwoToneColor=Yr,Sn.setTwoToneColor=bt;var St=Sn,eo=function(e,n){return m.createElement(St,(0,Qe.Z)({},e,{ref:n,icon:Ur}))},no=m.forwardRef(eo),to=no,Ae=f(64847),ro=function(e){return(0,l.Z)({},e.componentCls,{marginBlock:0,marginBlockStart:48,marginBlockEnd:24,marginInline:0,paddingBlock:0,paddingInline:16,textAlign:"center","&-list":{marginBlockEnd:8,color:e.colorTextSecondary,"&-link":{color:e.colorTextSecondary,textDecoration:e.linkDecoration},"*:not(:last-child)":{marginInlineEnd:8},"&:hover":{color:e.colorPrimary}},"&-copyright":{fontSize:"14px",color:e.colorText}})};function oo(r){return(0,Ae.Xj)("ProLayoutFooter",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[ro(n)]})}var ao=function(e){var n=e.className,t=e.prefixCls,o=e.links,i=e.copyright,a=e.style,c=(0,m.useContext)(Fe.ZP.ConfigContext),u=c.getPrefixCls(t||"pro-global-footer"),p=oo(u),v=p.wrapSSR,h=p.hashId;return(o==null||o===!1||Array.isArray(o)&&o.length===0)&&(i==null||i===!1)?null:v((0,s.jsxs)("div",{className:le()(u,h,n),style:a,children:[o&&(0,s.jsx)("div",{className:"".concat(u,"-list ").concat(h).trim(),children:o.map(function(y){return(0,s.jsx)("a",{className:"".concat(u,"-list-link ").concat(h).trim(),title:y.key,target:y.blankTarget?"_blank":"_self",href:y.href,rel:"noreferrer",children:y.title},y.key)})}),i&&(0,s.jsx)("div",{className:"".concat(u,"-copyright ").concat(h).trim(),children:i})]}))},io=Je.Z.Footer,lo=function(e){var n=e.links,t=e.copyright,o=e.style,i=e.className,a=e.prefixCls;return(0,s.jsx)(io,{className:i,style:(0,d.Z)({padding:0},o),children:(0,s.jsx)(ao,{links:n,prefixCls:a,copyright:t===!1?null:(0,s.jsxs)(m.Fragment,{children:[(0,s.jsx)(to,{})," ",t]})})})},_t=function r(e){return(e||[]).reduce(function(n,t){if(t.key&&n.push(t.key),t.children||t.routes){var o=n.concat(r(t.children||t.routes)||[]);return o}return n},[])},Pt={techBlue:"#1677FF",daybreak:"#1890ff",dust:"#F5222D",volcano:"#FA541C",sunset:"#FAAD14",cyan:"#13C2C2",green:"#52C41A",geekblue:"#2F54EB",purple:"#722ED1"};function yi(r){return r&&Pt[r]?Pt[r]:r||""}function _n(r){return r.map(function(e){var n=e.children||[],t=(0,d.Z)({},e);if(!t.children&&t.routes&&(t.children=t.routes),!t.name||t.hideInMenu)return null;if(t&&t!==null&&t!==void 0&&t.children){if(!t.hideChildrenInMenu&&n.some(function(o){return o&&o.name&&!o.hideInMenu}))return(0,d.Z)((0,d.Z)({},e),{},{children:_n(n)});delete t.children}return delete t.routes,t}).filter(function(e){return e})}var so={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"}}]},name:"menu",theme:"outlined"},co=so,uo=function(e,n){return m.createElement(St,(0,Qe.Z)({},e,{ref:n,icon:co}))},mo=m.forwardRef(uo),po=mo,fo=f(55241),vo=function(){return(0,s.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 12 12",fill:"currentColor","aria-hidden":"true",children:(0,s.jsx)("path",{d:"M0 0h3v3H0V0zm4.5 0h3v3h-3V0zM9 0h3v3H9V0zM0 4.5h3v3H0v-3zm4.503 0h3v3h-3v-3zM9 4.5h3v3H9v-3zM0 9h3v3H0V9zm4.503 0h3v3h-3V9zM9 9h3v3H9V9z"})})},go=function r(e){var n=e.appList,t=e.baseClassName,o=e.hashId,i=e.itemClick;return(0,s.jsx)("div",{className:"".concat(t,"-content ").concat(o).trim(),children:(0,s.jsx)("ul",{className:"".concat(t,"-content-list ").concat(o).trim(),children:n==null?void 0:n.map(function(a,c){var u;return a!=null&&(u=a.children)!==null&&u!==void 0&&u.length?(0,s.jsxs)("div",{className:"".concat(t,"-content-list-item-group ").concat(o).trim(),children:[(0,s.jsx)("div",{className:"".concat(t,"-content-list-item-group-title ").concat(o).trim(),children:a.title}),(0,s.jsx)(r,{hashId:o,itemClick:i,appList:a==null?void 0:a.children,baseClassName:t})]},c):(0,s.jsx)("li",{className:"".concat(t,"-content-list-item ").concat(o).trim(),onClick:function(v){v.stopPropagation(),i==null||i(a)},children:(0,s.jsxs)("a",{href:i?void 0:a.url,target:a.target,rel:"noreferrer",children:[Kn(a.icon),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{children:a.title}),a.desc?(0,s.jsx)("span",{children:a.desc}):null]})]})},c)})})})},Gn=function(e){if(!e||!e.startsWith("http"))return!1;try{var n=new URL(e);return!!n}catch(t){return!1}},ho=function(e,n){if(e&&typeof e=="string"&&Gn(e))return(0,s.jsx)("img",{src:e,alt:"logo"});if(typeof e=="function")return e();if(e&&typeof e=="string")return(0,s.jsx)("div",{id:"avatarLogo",children:e});if(!e&&n&&typeof n=="string"){var t=n.substring(0,1);return(0,s.jsx)("div",{id:"avatarLogo",children:t})}return e},yo=function r(e){var n=e.appList,t=e.baseClassName,o=e.hashId,i=e.itemClick;return(0,s.jsx)("div",{className:"".concat(t,"-content ").concat(o).trim(),children:(0,s.jsx)("ul",{className:"".concat(t,"-content-list ").concat(o).trim(),children:n==null?void 0:n.map(function(a,c){var u;return a!=null&&(u=a.children)!==null&&u!==void 0&&u.length?(0,s.jsxs)("div",{className:"".concat(t,"-content-list-item-group ").concat(o).trim(),children:[(0,s.jsx)("div",{className:"".concat(t,"-content-list-item-group-title ").concat(o).trim(),children:a.title}),(0,s.jsx)(r,{hashId:o,itemClick:i,appList:a==null?void 0:a.children,baseClassName:t})]},c):(0,s.jsx)("li",{className:"".concat(t,"-content-list-item ").concat(o).trim(),onClick:function(v){v.stopPropagation(),i==null||i(a)},children:(0,s.jsxs)("a",{href:i?"javascript:;":a.url,target:a.target,rel:"noreferrer",children:[ho(a.icon,a.title),(0,s.jsx)("div",{children:(0,s.jsx)("div",{children:a.title})})]})},c)})})})},Co=function(e){return{"&-content":{maxHeight:"calc(100vh - 48px)",overflow:"auto","&-list":{boxSizing:"content-box",maxWidth:656,marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none","&-item":{position:"relative",display:"inline-block",width:328,height:72,paddingInline:16,paddingBlock:16,verticalAlign:"top",listStyleType:"none",transition:"transform 0.2s cubic-bezier(0.333, 0, 0, 1)",borderRadius:e.borderRadius,"&-group":{marginBottom:16,"&-title":{margin:"16px 0 8px 12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginTop:12}}},"&:hover":{backgroundColor:e.colorBgTextHover},"* div":Ae.Wf===null||Ae.Wf===void 0?void 0:(0,Ae.Wf)(e),a:{display:"flex",height:"100%",fontSize:12,textDecoration:"none","& > img":{width:40,height:40},"& > div":{marginInlineStart:14,color:e.colorTextHeading,fontSize:14,lineHeight:"22px",whiteSpace:"nowrap",textOverflow:"ellipsis"},"& > div > span":{color:e.colorTextSecondary,fontSize:12,lineHeight:"20px"}}}}}}},xo=function(e){return{"&-content":{maxHeight:"calc(100vh - 48px)",overflow:"auto","&-list":{boxSizing:"border-box",maxWidth:376,marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none","&-item":{position:"relative",display:"inline-block",width:104,height:104,marginBlock:8,marginInline:8,paddingInline:24,paddingBlock:24,verticalAlign:"top",listStyleType:"none",transition:"transform 0.2s cubic-bezier(0.333, 0, 0, 1)",borderRadius:e.borderRadius,"&-group":{marginBottom:16,"&-title":{margin:"16px 0 8px 12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginTop:12}}},"&:hover":{backgroundColor:e.colorBgTextHover},a:{display:"flex",flexDirection:"column",alignItems:"center",height:"100%",fontSize:12,textDecoration:"none","& > #avatarLogo":{width:40,height:40,margin:"0 auto",color:e.colorPrimary,fontSize:22,lineHeight:"40px",textAlign:"center",backgroundImage:"linear-gradient(180deg, #E8F0FB 0%, #F6F8FC 100%)",borderRadius:e.borderRadius},"& > img":{width:40,height:40},"& > div":{marginBlockStart:5,marginInlineStart:0,color:e.colorTextHeading,fontSize:14,lineHeight:"22px",whiteSpace:"nowrap",textOverflow:"ellipsis"},"& > div > span":{color:e.colorTextSecondary,fontSize:12,lineHeight:"20px"}}}}}}},bo=function(e){var n,t,o,i,a;return(0,l.Z)({},e.componentCls,{"&-icon":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingInline:4,paddingBlock:0,fontSize:14,lineHeight:"14px",height:28,width:28,cursor:"pointer",color:(n=e.layout)===null||n===void 0?void 0:n.colorTextAppListIcon,borderRadius:e.borderRadius,"&:hover":{color:(t=e.layout)===null||t===void 0?void 0:t.colorTextAppListIconHover,backgroundColor:(o=e.layout)===null||o===void 0?void 0:o.colorBgAppListIconHover},"&-active":{color:(i=e.layout)===null||i===void 0?void 0:i.colorTextAppListIconHover,backgroundColor:(a=e.layout)===null||a===void 0?void 0:a.colorBgAppListIconHover}},"&-item-title":{marginInlineStart:"16px",marginInlineEnd:"8px",marginBlockStart:0,marginBlockEnd:"12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginBlockStart:12}},"&-popover":(0,l.Z)({},"".concat(e.antCls,"-popover-arrow"),{display:"none"}),"&-simple":xo(e),"&-default":Co(e)})};function So(r){return(0,Ae.Xj)("AppsLogoComponents",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[bo(n)]})}var Kn=function(e){return typeof e=="string"?(0,s.jsx)("img",{width:"auto",height:22,src:e,alt:"logo"}):typeof e=="function"?e():e},Xn=function(e){var n,t=e.appList,o=e.appListRender,i=e.prefixCls,a=i===void 0?"ant-pro":i,c=e.onItemClick,u=m.useRef(null),p=m.useRef(null),v="".concat(a,"-layout-apps"),h=So(v),y=h.wrapSSR,R=h.hashId,j=(0,m.useState)(!1),N=(0,F.Z)(j,2),T=N[0],V=N[1],B=function(w){c==null||c(w,p)},z=(0,m.useMemo)(function(){var b=t==null?void 0:t.some(function(w){return!(w!=null&&w.desc)});return b?(0,s.jsx)(yo,{hashId:R,appList:t,itemClick:c?B:void 0,baseClassName:"".concat(v,"-simple")}):(0,s.jsx)(go,{hashId:R,appList:t,itemClick:c?B:void 0,baseClassName:"".concat(v,"-default")})},[t,v,R]);if(!(e!=null&&(n=e.appList)!==null&&n!==void 0&&n.length))return null;var Z=o?o(e==null?void 0:e.appList,z):z,W=(0,M.X)(void 0,function(b){return V(b)});return y((0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{ref:u,onClick:function(w){w.stopPropagation(),w.preventDefault()}}),(0,s.jsx)(fo.Z,(0,d.Z)((0,d.Z)({placement:"bottomRight",trigger:["click"],zIndex:9999,arrow:!1},W),{},{overlayClassName:"".concat(v,"-popover ").concat(R).trim(),content:Z,getPopupContainer:function(){return u.current||document.body},children:(0,s.jsx)("span",{ref:p,onClick:function(w){w.stopPropagation()},className:le()("".concat(v,"-icon"),R,(0,l.Z)({},"".concat(v,"-icon-active"),T)),children:(0,s.jsx)(vo,{})})}))]}))},It=f(85357),_o=f(78957),Mt=f(50136);function Po(){return(0,s.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 12 12",fill:"currentColor","aria-hidden":"true",children:(0,s.jsx)("path",{d:"M6.432 7.967a.448.448 0 01-.318.133h-.228a.46.46 0 01-.318-.133L2.488 4.85a.305.305 0 010-.43l.427-.43a.293.293 0 01.42 0L6 6.687l2.665-2.699a.299.299 0 01.426 0l.42.431a.305.305 0 010 .43L6.432 7.967z"})})}var Io=function(e){var n,t,o;return(0,l.Z)({},e.componentCls,{position:"absolute",insetBlockStart:"18px",zIndex:"101",width:"24px",height:"24px",fontSize:["14px","16px"],textAlign:"center",borderRadius:"40px",insetInlineEnd:"-13px",transition:"transform 0.3s",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",color:(n=e.layout)===null||n===void 0||(n=n.sider)===null||n===void 0?void 0:n.colorTextCollapsedButton,backgroundColor:(t=e.layout)===null||t===void 0||(t=t.sider)===null||t===void 0?void 0:t.colorBgCollapsedButton,boxShadow:"0 2px 8px -2px rgba(0,0,0,0.05), 0 1px 4px -1px rgba(25,15,15,0.07), 0 0 1px 0 rgba(0,0,0,0.08)","&:hover":{color:(o=e.layout)===null||o===void 0||(o=o.sider)===null||o===void 0?void 0:o.colorTextCollapsedButtonHover,boxShadow:"0 4px 16px -4px rgba(0,0,0,0.05), 0 2px 8px -2px rgba(25,15,15,0.07), 0 1px 2px 0 rgba(0,0,0,0.08)"},".anticon":{fontSize:"14px"},"& > svg":{transition:"transform  0.3s",transform:"rotate(90deg)"},"&-collapsed":{"& > svg":{transform:"rotate(-90deg)"}}})};function Mo(r){return(0,Ae.Xj)("SiderMenuCollapsedIcon",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[Io(n)]})}var Eo=["isMobile","collapsed"],Do=function(e){var n=e.isMobile,t=e.collapsed,o=(0,G.Z)(e,Eo),i=Mo(e.className),a=i.wrapSSR,c=i.hashId;return n&&t?null:a((0,s.jsx)("div",(0,d.Z)((0,d.Z)({},o),{},{className:le()(e.className,c,(0,l.Z)((0,l.Z)({},"".concat(e.className,"-collapsed"),t),"".concat(e.className,"-is-mobile"),n)),children:(0,s.jsx)(Po,{})})))},Pn=f(74902),Ro=f(43144),To=f(15671),Ao=f(42550),Oo=["className","component","viewBox","spin","rotate","tabIndex","onClick","children"],Et=m.forwardRef(function(r,e){var n=r.className,t=r.component,o=r.viewBox,i=r.spin,a=r.rotate,c=r.tabIndex,u=r.onClick,p=r.children,v=(0,G.Z)(r,Oo),h=m.useRef(),y=(0,Ao.x1)(h,e);Vn(!!(t||p),"Should have `component` prop or `children`."),xt(h);var R=m.useContext(Un),j=R.prefixCls,N=j===void 0?"anticon":j,T=R.rootClassName,V=le()(T,N,(0,l.Z)({},"".concat(N,"-spin"),!!i&&!!t),n),B=le()((0,l.Z)({},"".concat(N,"-spin"),!!i)),z=a?{msTransform:"rotate(".concat(a,"deg)"),transform:"rotate(".concat(a,"deg)")}:void 0,Z=(0,d.Z)((0,d.Z)({},Gr),{},{className:B,style:z,viewBox:o});o||delete Z.viewBox;var W=function(){return t?m.createElement(t,Z,p):p?(Vn(!!o||m.Children.count(p)===1&&m.isValidElement(p)&&m.Children.only(p).type==="use","Make sure that you provide correct `viewBox` prop (default `0 0 1024 1024`) to the icon."),m.createElement("svg",(0,Qe.Z)({},Z,{viewBox:o}),p)):null},b=c;return b===void 0&&u&&(b=-1),m.createElement("span",(0,Qe.Z)({role:"img"},v,{ref:y,tabIndex:b,onClick:u,className:V}),W())});Et.displayName="AntdIcon";var No=Et,Zo=["type","children"],Dt=new Set;function wo(r){return!!(typeof r=="string"&&r.length&&!Dt.has(r))}function In(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=r[e];if(wo(n)){var t=document.createElement("script");t.setAttribute("src",n),t.setAttribute("data-namespace",n),r.length>e+1&&(t.onload=function(){In(r,e+1)},t.onerror=function(){In(r,e+1)}),Dt.add(n),document.body.appendChild(t)}}function Rt(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=r.scriptUrl,n=r.extraCommonProps,t=n===void 0?{}:n;e&&typeof document!="undefined"&&typeof window!="undefined"&&typeof document.createElement=="function"&&(Array.isArray(e)?In(e.reverse()):In([e]));var o=m.forwardRef(function(i,a){var c=i.type,u=i.children,p=(0,G.Z)(i,Zo),v=null;return i.type&&(v=m.createElement("use",{xlinkHref:"#".concat(c)})),u&&(v=u),m.createElement(No,(0,Qe.Z)({},t,p,{ref:a}),v)});return o.displayName="Iconfont",o}function jo(r){return/\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(r)}var Lo=f(83062),Bo=f(48054),Tt={navTheme:"light",layout:"side",contentWidth:"Fluid",fixedHeader:!1,fixSiderbar:!0,iconfontUrl:"",colorPrimary:"#1677FF",splitMenus:!1},Fo=function(e,n){var t,o,i=n.includes("horizontal")?(t=e.layout)===null||t===void 0?void 0:t.header:(o=e.layout)===null||o===void 0?void 0:o.sider;return(0,d.Z)((0,d.Z)((0,l.Z)({},"".concat(e.componentCls),(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({background:"transparent",color:i==null?void 0:i.colorTextMenu,border:"none"},"".concat(e.componentCls,"-menu-item"),{transition:"none !important"}),"".concat(e.componentCls,"-submenu-has-icon"),(0,l.Z)({},"> ".concat(e.antCls,"-menu-sub"),{paddingInlineStart:10})),"".concat(e.antCls,"-menu-title-content"),{width:"100%",height:"100%",display:"inline-flex"}),"".concat(e.antCls,"-menu-title-content"),{"&:first-child":{width:"100%"}}),"".concat(e.componentCls,"-item-icon"),{display:"flex",alignItems:"center"}),"&&-collapsed",(0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(e.antCls,`-menu-item, 
        `).concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,`-menu-item, 
        `).concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu > ").concat(e.antCls,`-menu-submenu-title, 
        `).concat(e.antCls,"-menu-submenu > ").concat(e.antCls,"-menu-submenu-title"),{paddingInline:"0 !important",marginBlock:"4px !important"}),"".concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,`-menu-submenu-title, 
        `).concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,"-menu-submenu-title"),{backgroundColor:i==null?void 0:i.colorBgMenuItemSelected,borderRadius:e.borderRadiusLG}),"".concat(e.componentCls,"-group"),(0,l.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{paddingInline:0}))),"&-item-title",(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({display:"flex",flexDirection:"row",alignItems:"center",gap:e.marginXS},"".concat(e.componentCls,"-item-text"),{maxWidth:"100%",textOverflow:"ellipsis",overflow:"hidden",wordBreak:"break-all",whiteSpace:"nowrap"}),"&-collapsed",(0,l.Z)((0,l.Z)({minWidth:40,height:40},"".concat(e.componentCls,"-item-icon"),{height:"16px",width:"16px",lineHeight:"16px !important",".anticon":{lineHeight:"16px !important",height:"16px"}}),"".concat(e.componentCls,"-item-text-has-icon"),{display:"none !important"})),"&-collapsed-level-0",{flexDirection:"column",justifyContent:"center"}),"&".concat(e.componentCls,"-group-item-title"),{gap:e.marginXS,height:18,overflow:"hidden"}),"&".concat(e.componentCls,"-item-collapsed-show-title"),(0,l.Z)({lineHeight:"16px",gap:0},"&".concat(e.componentCls,"-item-title-collapsed"),(0,l.Z)((0,l.Z)({display:"flex"},"".concat(e.componentCls,"-item-icon"),{height:"16px",width:"16px",lineHeight:"16px !important",".anticon":{lineHeight:"16px!important",height:"16px"}}),"".concat(e.componentCls,"-item-text"),{opacity:"1 !important",display:"inline !important",textAlign:"center",fontSize:12,height:12,lineHeight:"12px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",width:"100%",margin:0,padding:0,marginBlockStart:4})))),"&-group",(0,l.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:12,color:e.colorTextLabel,".anticon":{marginInlineEnd:8}})),"&-group-divider",{color:e.colorTextSecondary,fontSize:12,lineHeight:20})),n.includes("horizontal")?{}:(0,l.Z)({},"".concat(e.antCls,"-menu-submenu").concat(e.antCls,"-menu-submenu-popup"),(0,l.Z)({},"".concat(e.componentCls,"-item-title"),{alignItems:"flex-start"}))),{},(0,l.Z)({},"".concat(e.antCls,"-menu-submenu-popup"),{backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"}))};function Wo(r,e){return(0,Ae.Xj)("ProLayoutBaseMenu"+e,function(n){var t=(0,d.Z)((0,d.Z)({},n),{},{componentCls:".".concat(r)});return[Fo(t,e||"inline")]})}var At=function(e){var n=(0,m.useState)(e.collapsed),t=(0,F.Z)(n,2),o=t[0],i=t[1],a=(0,m.useState)(!1),c=(0,F.Z)(a,2),u=c[0],p=c[1];return(0,m.useEffect)(function(){p(!1),setTimeout(function(){i(e.collapsed)},400)},[e.collapsed]),e.disable?e.children:(0,s.jsx)(Lo.Z,{title:e.title,open:o&&e.collapsed?u:!1,placement:"right",onOpenChange:p,children:e.children})},Ot=Rt({scriptUrl:Tt.iconfontUrl}),Nt=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"icon-",t=arguments.length>2?arguments[2]:void 0;if(typeof e=="string"&&e!==""){if(Gn(e)||jo(e))return(0,s.jsx)("img",{width:16,src:e,alt:"icon",className:t},e);if(e.startsWith(n))return(0,s.jsx)(Ot,{type:e})}return e},Zt=function(e){if(e&&typeof e=="string"){var n=e.substring(0,1).toUpperCase();return n}return null},Ho=(0,Ro.Z)(function r(e){var n=this;(0,To.Z)(this,r),(0,l.Z)(this,"props",void 0),(0,l.Z)(this,"getNavMenuItems",function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],o=arguments.length>1?arguments[1]:void 0,i=arguments.length>2?arguments[2]:void 0;return t.map(function(a){return n.getSubMenuOrItem(a,o,i)}).filter(function(a){return a}).flat(1)}),(0,l.Z)(this,"getSubMenuOrItem",function(t,o,i){var a=n.props,c=a.subMenuItemRender,u=a.baseClassName,p=a.prefixCls,v=a.collapsed,h=a.menu,y=a.iconPrefixes,R=a.layout,j=(h==null?void 0:h.type)==="group"&&R!=="top",N=n.props.token,T=n.getIntlName(t),V=(t==null?void 0:t.children)||(t==null?void 0:t.routes),B=j&&o===0?"group":void 0;if(Array.isArray(V)&&V.length>0){var z,Z,W,b,w,ae=o===0||j&&o===1,U=Nt(t.icon,y,"".concat(u,"-icon ").concat((z=n.props)===null||z===void 0?void 0:z.hashId)),L=v&&ae?Zt(T):null,ue=(0,s.jsxs)("div",{className:le()("".concat(u,"-item-title"),(Z=n.props)===null||Z===void 0?void 0:Z.hashId,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(u,"-item-title-collapsed"),v),"".concat(u,"-item-title-collapsed-level-").concat(i),v),"".concat(u,"-group-item-title"),B==="group"),"".concat(u,"-item-collapsed-show-title"),(h==null?void 0:h.collapsedShowTitle)&&v)),children:[B==="group"&&v?null:ae&&U?(0,s.jsx)("span",{className:"".concat(u,"-item-icon ").concat((W=n.props)===null||W===void 0?void 0:W.hashId).trim(),children:U}):L,(0,s.jsx)("span",{className:le()("".concat(u,"-item-text"),(b=n.props)===null||b===void 0?void 0:b.hashId,(0,l.Z)({},"".concat(u,"-item-text-has-icon"),B!=="group"&&ae&&(U||L))),children:T})]}),ye=c?c((0,d.Z)((0,d.Z)({},t),{},{isUrl:!1}),ue,n.props):ue;if(j&&o===0&&n.props.collapsed&&!h.collapsedShowGroupTitle)return n.getNavMenuItems(V,o+1,o);var x=n.getNavMenuItems(V,o+1,j&&o===0&&n.props.collapsed?o:o+1);return[{type:B,key:t.key||t.path,label:ye,onClick:j?void 0:t.onTitleClick,children:x,className:le()((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(u,"-group"),B==="group"),"".concat(u,"-submenu"),B!=="group"),"".concat(u,"-submenu-has-icon"),B!=="group"&&ae&&U))},j&&o===0?{type:"divider",prefixCls:p,className:"".concat(u,"-divider"),key:(t.key||t.path)+"-group-divider",style:{padding:0,borderBlockEnd:0,margin:n.props.collapsed?"4px":"6px 16px",marginBlockStart:n.props.collapsed?4:8,borderColor:N==null||(w=N.layout)===null||w===void 0||(w=w.sider)===null||w===void 0?void 0:w.colorMenuItemDivider}}:void 0].filter(Boolean)}return{className:"".concat(u,"-menu-item"),disabled:t.disabled,key:t.key||t.path,onClick:t.onTitleClick,label:n.getMenuItemPath(t,o,i)}}),(0,l.Z)(this,"getIntlName",function(t){var o=t.name,i=t.locale,a=n.props,c=a.menu,u=a.formatMessage,p=o;return i&&(c==null?void 0:c.locale)!==!1&&(p=u==null?void 0:u({id:i,defaultMessage:o})),n.props.menuTextRender?n.props.menuTextRender(t,p,n.props):p}),(0,l.Z)(this,"getMenuItemPath",function(t,o,i){var a,c,u,p,v=n.conversionPath(t.path||"/"),h=n.props,y=h.location,R=y===void 0?{pathname:"/"}:y,j=h.isMobile,N=h.onCollapse,T=h.menuItemRender,V=h.iconPrefixes,B=n.getIntlName(t),z=n.props,Z=z.baseClassName,W=z.menu,b=z.collapsed,w=(W==null?void 0:W.type)==="group",ae=o===0||w&&o===1,U=ae?Nt(t.icon,V,"".concat(Z,"-icon ").concat((a=n.props)===null||a===void 0?void 0:a.hashId)):null,L=b&&ae?Zt(B):null,ue=(0,s.jsxs)("div",{className:le()("".concat(Z,"-item-title"),(c=n.props)===null||c===void 0?void 0:c.hashId,(0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(Z,"-item-title-collapsed"),b),"".concat(Z,"-item-title-collapsed-level-").concat(i),b),"".concat(Z,"-item-collapsed-show-title"),(W==null?void 0:W.collapsedShowTitle)&&b)),children:[(0,s.jsx)("span",{className:"".concat(Z,"-item-icon ").concat((u=n.props)===null||u===void 0?void 0:u.hashId).trim(),style:{display:L===null&&!U?"none":""},children:U||(0,s.jsx)("span",{className:"anticon",children:L})}),(0,s.jsx)("span",{className:le()("".concat(Z,"-item-text"),(p=n.props)===null||p===void 0?void 0:p.hashId,(0,l.Z)({},"".concat(Z,"-item-text-has-icon"),ae&&(U||L))),children:B})]},v),ye=Gn(v);if(ye){var x,xe,A;ue=(0,s.jsxs)("span",{onClick:function(){var Se,he;(Se=window)===null||Se===void 0||(he=Se.open)===null||he===void 0||he.call(Se,v,"_blank")},className:le()("".concat(Z,"-item-title"),(x=n.props)===null||x===void 0?void 0:x.hashId,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(Z,"-item-title-collapsed"),b),"".concat(Z,"-item-title-collapsed-level-").concat(i),b),"".concat(Z,"-item-link"),!0),"".concat(Z,"-item-collapsed-show-title"),(W==null?void 0:W.collapsedShowTitle)&&b)),children:[(0,s.jsx)("span",{className:"".concat(Z,"-item-icon ").concat((xe=n.props)===null||xe===void 0?void 0:xe.hashId).trim(),style:{display:L===null&&!U?"none":""},children:U||(0,s.jsx)("span",{className:"anticon",children:L})}),(0,s.jsx)("span",{className:le()("".concat(Z,"-item-text"),(A=n.props)===null||A===void 0?void 0:A.hashId,(0,l.Z)({},"".concat(Z,"-item-text-has-icon"),ae&&(U||L))),children:B})]},v)}if(T){var ie=(0,d.Z)((0,d.Z)({},t),{},{isUrl:ye,itemPath:v,isMobile:j,replace:v===R.pathname,onClick:function(){return N&&N(!0)},children:void 0});return o===0?(0,s.jsx)(At,{collapsed:b,title:B,disable:t.disabledTooltip,children:T(ie,ue,n.props)}):T(ie,ue,n.props)}return o===0?(0,s.jsx)(At,{collapsed:b,title:B,disable:t.disabledTooltip,children:ue}):ue}),(0,l.Z)(this,"conversionPath",function(t){return t&&t.indexOf("http")===0?t:"/".concat(t||"").replace(/\/+/g,"/")}),this.props=e}),Uo=function(e,n){var t=n.layout,o=n.collapsed,i={};return e&&!o&&["side","mix"].includes(t||"mix")&&(i={openKeys:e}),i},wt=function(e){var n=e.mode,t=e.className,o=e.handleOpenChange,i=e.style,a=e.menuData,c=e.prefixCls,u=e.menu,p=e.matchMenuKeys,v=e.iconfontUrl,h=e.selectedKeys,y=e.onSelect,R=e.menuRenderType,j=e.openKeys,N=(0,m.useContext)(te.L_),T=N.dark,V=N.token,B="".concat(c,"-base-menu-").concat(n),z=(0,m.useRef)([]),Z=(0,Q.Z)(u==null?void 0:u.defaultOpenAll),W=(0,F.Z)(Z,2),b=W[0],w=W[1],ae=(0,Q.Z)(function(){return u!=null&&u.defaultOpenAll?_t(a)||[]:j===!1?!1:[]},{value:j===!1?void 0:j,onChange:o}),U=(0,F.Z)(ae,2),L=U[0],ue=U[1],ye=(0,Q.Z)([],{value:h,onChange:y?function(Pe){y&&Pe&&y(Pe)}:void 0}),x=(0,F.Z)(ye,2),xe=x[0],A=x[1];(0,m.useEffect)(function(){u!=null&&u.defaultOpenAll||j===!1||p&&(ue(p),A(p))},[p.join("-")]),(0,m.useEffect)(function(){v&&(Ot=Rt({scriptUrl:v}))},[v]),(0,m.useEffect)(function(){if(p.join("-")!==(xe||[]).join("-")&&A(p),!b&&j!==!1&&p.join("-")!==(L||[]).join("-")){var Pe=p;(u==null?void 0:u.autoClose)===!1&&(Pe=Array.from(new Set([].concat((0,Pn.Z)(p),(0,Pn.Z)(L||[]))))),ue(Pe)}else u!=null&&u.ignoreFlatMenu&&b?ue(_t(a)):w(!1)},[p.join("-")]);var ie=(0,m.useMemo)(function(){return Uo(L,e)},[L&&L.join(","),e.layout,e.collapsed]),fe=Wo(B,n),Se=fe.wrapSSR,he=fe.hashId,Ee=(0,m.useMemo)(function(){return new Ho((0,d.Z)((0,d.Z)({},e),{},{token:V,menuRenderType:R,baseClassName:B,hashId:he}))},[e,V,R,B,he]);if(u!=null&&u.loading)return(0,s.jsx)("div",{style:n!=null&&n.includes("inline")?{padding:24}:{marginBlockStart:16},children:(0,s.jsx)(Bo.Z,{active:!0,title:!1,paragraph:{rows:n!=null&&n.includes("inline")?6:1}})});e.openKeys===!1&&!e.handleOpenChange&&(z.current=p);var _e=e.postMenuData?e.postMenuData(a):a;return _e&&(_e==null?void 0:_e.length)<1?null:Se((0,m.createElement)(Mt.Z,(0,d.Z)((0,d.Z)({},ie),{},{_internalDisableMenuItemTitleTooltip:!0,key:"Menu",mode:n,inlineIndent:16,defaultOpenKeys:z.current,theme:T?"dark":"light",selectedKeys:xe,style:(0,d.Z)({backgroundColor:"transparent",border:"none"},i),className:le()(t,he,B,(0,l.Z)((0,l.Z)({},"".concat(B,"-horizontal"),n==="horizontal"),"".concat(B,"-collapsed"),e.collapsed)),items:Ee.getNavMenuItems(_e,0,0),onOpenChange:function(je){e.collapsed||ue(je)}},e.menuProps)))};function $o(r,e){var n=e.stylish,t=e.proLayoutCollapsedWidth;return(0,Ae.Xj)("ProLayoutSiderMenuStylish",function(o){var i=(0,d.Z)((0,d.Z)({},o),{},{componentCls:".".concat(r),proLayoutCollapsedWidth:t});return n?[(0,l.Z)({},"div".concat(o.proComponentsCls,"-layout"),(0,l.Z)({},"".concat(i.componentCls),n==null?void 0:n(i)))]:[]})}var Vo=["title","render"],zo=m.memo(function(r){return(0,s.jsx)(s.Fragment,{children:r.children})}),ko=Je.Z.Sider,jt=Je.Z._InternalSiderContext,Go=jt===void 0?{Provider:zo}:jt,Jn=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"menuHeaderRender",t=e.logo,o=e.title,i=e.layout,a=e[n];if(a===!1)return null;var c=Kn(t),u=(0,s.jsx)("h1",{children:o!=null?o:"Ant Design Pro"});return a?a(c,e.collapsed?null:u,e):e.isMobile?null:i==="mix"&&n==="menuHeaderRender"?!1:e.collapsed?(0,s.jsx)("a",{children:c},"title"):(0,s.jsxs)("a",{children:[c,u]},"title")},Lt=function(e){var n,t=e.collapsed,o=e.originCollapsed,i=e.fixSiderbar,a=e.menuFooterRender,c=e.onCollapse,u=e.theme,p=e.siderWidth,v=e.isMobile,h=e.onMenuHeaderClick,y=e.breakpoint,R=y===void 0?"lg":y,j=e.style,N=e.layout,T=e.menuExtraRender,V=T===void 0?!1:T,B=e.links,z=e.menuContentRender,Z=e.collapsedButtonRender,W=e.prefixCls,b=e.avatarProps,w=e.rightContentRender,ae=e.actionsRender,U=e.onOpenChange,L=e.stylish,ue=e.logoStyle,ye=(0,m.useContext)(te.L_),x=ye.hashId,xe=(0,m.useMemo)(function(){return!(v||N==="mix")},[v,N]),A="".concat(W,"-sider"),ie=64,fe=$o("".concat(A,".").concat(A,"-stylish"),{stylish:L,proLayoutCollapsedWidth:ie}),Se=le()("".concat(A),x,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(A,"-fixed"),i),"".concat(A,"-fixed-mix"),N==="mix"&&!v&&i),"".concat(A,"-collapsed"),e.collapsed),"".concat(A,"-layout-").concat(N),N&&!v),"".concat(A,"-light"),u!=="dark"),"".concat(A,"-mix"),N==="mix"&&!v),"".concat(A,"-stylish"),!!L)),he=Jn(e),Ee=V&&V(e),_e=(0,m.useMemo)(function(){return z!==!1&&(0,m.createElement)(wt,(0,d.Z)((0,d.Z)({},e),{},{key:"base-menu",mode:t&&!v?"vertical":"inline",handleOpenChange:U,style:{width:"100%"},className:"".concat(A,"-menu ").concat(x).trim()}))},[A,x,z,U,e]),Pe=(B||[]).map(function(De,We){return{className:"".concat(A,"-link"),label:De,key:We}}),je=(0,m.useMemo)(function(){return z?z(e,_e):_e},[z,_e,e]),Ne=(0,m.useMemo)(function(){if(!b)return null;var De=b.title,We=b.render,Le=(0,G.Z)(b,Vo),Dn=(0,s.jsxs)("div",{className:"".concat(A,"-actions-avatar"),children:[Le!=null&&Le.src||Le!=null&&Le.srcSet||Le.icon||Le.children?(0,s.jsx)(It.Z,(0,d.Z)({size:28},Le)):null,b.title&&!t&&(0,s.jsx)("span",{children:De})]});return We?We(b,Dn,e):Dn},[b,A,t]),we=(0,m.useMemo)(function(){return ae?(0,s.jsx)(_o.Z,{align:"center",size:4,direction:t?"vertical":"horizontal",className:le()(["".concat(A,"-actions-list"),t&&"".concat(A,"-actions-list-collapsed"),x]),children:[ae==null?void 0:ae(e)].flat(1).map(function(De,We){return(0,s.jsx)("div",{className:"".concat(A,"-actions-list-item ").concat(x).trim(),children:De},We)})}):null},[ae,A,t]),He=(0,m.useMemo)(function(){return(0,s.jsx)(Xn,{onItemClick:e.itemClick,appListRender:e.appListRender,appList:e.appList,prefixCls:e.prefixCls})},[e.appList,e.appListRender,e.prefixCls]),Ve=(0,m.useMemo)(function(){if(Z===!1)return null;var De=(0,s.jsx)(Do,{isMobile:v,collapsed:o,className:"".concat(A,"-collapsed-button"),onClick:function(){c==null||c(!o)}});return Z?Z(t,De):De},[Z,v,o,A,t,c]),ze=(0,m.useMemo)(function(){return!Ne&&!we?null:(0,s.jsxs)("div",{className:le()("".concat(A,"-actions"),x,t&&"".concat(A,"-actions-collapsed")),children:[Ne,we]})},[we,Ne,A,t,x]),ke=(0,m.useMemo)(function(){var De;return e!=null&&(De=e.menu)!==null&&De!==void 0&&De.hideMenuWhenCollapsed&&t?"".concat(A,"-hide-menu-collapsed"):null},[A,t,e==null||(n=e.menu)===null||n===void 0?void 0:n.hideMenuWhenCollapsed]),pn=a&&(a==null?void 0:a(e)),En=(0,s.jsxs)(s.Fragment,{children:[he&&(0,s.jsxs)("div",{className:le()([le()("".concat(A,"-logo"),x,(0,l.Z)({},"".concat(A,"-logo-collapsed"),t))]),onClick:xe?h:void 0,id:"logo",style:ue,children:[he,He]}),Ee&&(0,s.jsx)("div",{className:le()(["".concat(A,"-extra"),!he&&"".concat(A,"-extra-no-logo"),x]),children:Ee}),(0,s.jsx)("div",{style:{flex:1,overflowY:"auto",overflowX:"hidden"},children:je}),(0,s.jsxs)(Go.Provider,{value:{},children:[B?(0,s.jsx)("div",{className:"".concat(A,"-links ").concat(x).trim(),children:(0,s.jsx)(Mt.Z,{inlineIndent:16,className:"".concat(A,"-link-menu ").concat(x).trim(),selectedKeys:[],openKeys:[],theme:u,mode:"inline",items:Pe})}):null,xe&&(0,s.jsxs)(s.Fragment,{children:[ze,!we&&w?(0,s.jsx)("div",{className:le()("".concat(A,"-actions"),x,(0,l.Z)({},"".concat(A,"-actions-collapsed"),t)),children:w==null?void 0:w(e)}):null]}),pn&&(0,s.jsx)("div",{className:le()(["".concat(A,"-footer"),x,(0,l.Z)({},"".concat(A,"-footer-collapsed"),t)]),children:pn})]})]});return fe.wrapSSR((0,s.jsxs)(s.Fragment,{children:[i&&!v&&!ke&&(0,s.jsx)("div",{style:(0,d.Z)({width:t?ie:p,overflow:"hidden",flex:"0 0 ".concat(t?ie:p,"px"),maxWidth:t?ie:p,minWidth:t?ie:p,transition:"all 0.2s ease 0s"},j)}),(0,s.jsxs)(ko,{collapsible:!0,trigger:null,collapsed:t,breakpoint:R===!1?void 0:R,onCollapse:function(We){v||c==null||c(We)},collapsedWidth:ie,style:j,theme:u,width:p,className:le()(Se,x,ke),children:[ke?(0,s.jsx)("div",{className:"".concat(A,"-hide-when-collapsed ").concat(x).trim(),style:{height:"100%",width:"100%",opacity:ke?0:1},children:En}):En,Ve]})]}))},Ko=f(10178),Xo=f(9220),Jo=function(e){var n,t,o,i,a;return(0,l.Z)({},e.componentCls,{"&-header-actions":{display:"flex",height:"100%",alignItems:"center","&-item":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingBlock:0,paddingInline:2,color:(n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.colorTextRightActionsItem,fontSize:"16px",cursor:"pointer",borderRadius:e.borderRadius,"> *":{paddingInline:6,paddingBlock:6,borderRadius:e.borderRadius,"&:hover":{backgroundColor:(t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.colorBgRightActionsItemHover}}},"&-avatar":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingInlineStart:e.padding,paddingInlineEnd:e.padding,cursor:"pointer",color:(o=e.layout)===null||o===void 0||(o=o.header)===null||o===void 0?void 0:o.colorTextRightActionsItem,"> div":{height:"44px",color:(i=e.layout)===null||i===void 0||(i=i.header)===null||i===void 0?void 0:i.colorTextRightActionsItem,paddingInline:8,paddingBlock:8,cursor:"pointer",display:"flex",alignItems:"center",lineHeight:"44px",borderRadius:e.borderRadius,"&:hover":{backgroundColor:(a=e.layout)===null||a===void 0||(a=a.header)===null||a===void 0?void 0:a.colorBgRightActionsItemHover}}}}})};function Qo(r){return(0,Ae.Xj)("ProLayoutRightContent",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[Jo(n)]})}var Yo=["rightContentRender","avatarProps","actionsRender","headerContentRender"],qo=["title","render"],Bt=function(e){var n=e.rightContentRender,t=e.avatarProps,o=e.actionsRender,i=e.headerContentRender,a=(0,G.Z)(e,Yo),c=(0,m.useContext)(Fe.ZP.ConfigContext),u=c.getPrefixCls,p="".concat(u(),"-pro-global-header"),v=Qo(p),h=v.wrapSSR,y=v.hashId,R=(0,m.useState)("auto"),j=(0,F.Z)(R,2),N=j[0],T=j[1],V=(0,m.useMemo)(function(){if(!t)return null;var W=t.title,b=t.render,w=(0,G.Z)(t,qo),ae=[w!=null&&w.src||w!=null&&w.srcSet||w.icon||w.children?(0,m.createElement)(It.Z,(0,d.Z)((0,d.Z)({},w),{},{size:28,key:"avatar"})):null,W?(0,s.jsx)("span",{style:{marginInlineStart:8},children:W},"name"):void 0];return b?b(t,(0,s.jsx)("div",{children:ae}),a):(0,s.jsx)("div",{children:ae})},[t]),B=o||V?function(W){var b=o&&(o==null?void 0:o(W));return!b&&!V?null:Array.isArray(b)?h((0,s.jsxs)("div",{className:"".concat(p,"-header-actions ").concat(y).trim(),children:[b.filter(Boolean).map(function(w,ae){var U=!1;if(m.isValidElement(w)){var L;U=!!(w!=null&&(L=w.props)!==null&&L!==void 0&&L["aria-hidden"])}return(0,s.jsx)("div",{className:le()("".concat(p,"-header-actions-item ").concat(y),(0,l.Z)({},"".concat(p,"-header-actions-hover"),!U)),children:w},ae)}),V&&(0,s.jsx)("span",{className:"".concat(p,"-header-actions-avatar ").concat(y).trim(),children:V})]})):h((0,s.jsxs)("div",{className:"".concat(p,"-header-actions ").concat(y).trim(),children:[b,V&&(0,s.jsx)("span",{className:"".concat(p,"-header-actions-avatar ").concat(y).trim(),children:V})]}))}:void 0,z=(0,Ko.D)(function(){var W=(0,k.Z)((0,K.Z)().mark(function b(w){return(0,K.Z)().wrap(function(U){for(;;)switch(U.prev=U.next){case 0:T(w);case 1:case"end":return U.stop()}},b)}));return function(b){return W.apply(this,arguments)}}(),160),Z=B||n;return(0,s.jsx)("div",{className:"".concat(p,"-right-content ").concat(y).trim(),style:{minWidth:N,height:"100%"},children:(0,s.jsx)("div",{style:{height:"100%"},children:(0,s.jsx)(Xo.Z,{onResize:function(b){var w=b.width;z.run(w)},children:Z?(0,s.jsx)("div",{style:{display:"flex",alignItems:"center",height:"100%",justifyContent:"flex-end"},children:Z((0,d.Z)((0,d.Z)({},a),{},{rightContentSize:N}))}):null})})})},ea=function(e){var n,t;return(0,l.Z)({},e.componentCls,{position:"relative",width:"100%",height:"100%",backgroundColor:"transparent",".anticon":{color:"inherit"},"&-main":{display:"flex",height:"100%",paddingInlineStart:"16px","&-left":(0,l.Z)({display:"flex",alignItems:"center"},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16,marginInlineStart:-8})},"&-wide":{maxWidth:1152,margin:"0 auto"},"&-logo":{position:"relative",display:"flex",height:"100%",alignItems:"center",overflow:"hidden","> *:first-child":{display:"flex",alignItems:"center",minHeight:"22px",fontSize:"22px"},"> *:first-child > img":{display:"inline-block",height:"32px",verticalAlign:"middle"},"> *:first-child > h1":{display:"inline-block",marginBlock:0,marginInline:0,lineHeight:"24px",marginInlineStart:6,fontWeight:"600",fontSize:"16px",color:(n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.colorHeaderTitle,verticalAlign:"top"}},"&-menu":{minWidth:0,display:"flex",alignItems:"center",paddingInline:6,paddingBlock:6,lineHeight:"".concat(Math.max((((t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.heightLayoutHeader)||56)-12,40),"px")}})};function na(r){return(0,Ae.Xj)("ProLayoutTopNavHeader",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[ea(n)]})}var Ft=function(e){var n,t,o,i,a,c,u,p=(0,m.useRef)(null),v=e.onMenuHeaderClick,h=e.contentWidth,y=e.rightContentRender,R=e.className,j=e.style,N=e.headerContentRender,T=e.layout,V=e.actionsRender,B=(0,m.useContext)(Fe.ZP.ConfigContext),z=B.getPrefixCls,Z=(0,m.useContext)(te.L_),W=Z.dark,b="".concat(e.prefixCls||z("pro"),"-top-nav-header"),w=na(b),ae=w.wrapSSR,U=w.hashId,L=void 0;e.menuHeaderRender!==void 0?L="menuHeaderRender":(T==="mix"||T==="top")&&(L="headerTitleRender");var ue=Jn((0,d.Z)((0,d.Z)({},e),{},{collapsed:!1}),L),ye=(0,m.useContext)(te.L_),x=ye.token,xe=(0,m.useMemo)(function(){var A,ie,fe,Se,he,Ee,_e,Pe,je,Ne,we,He,Ve,ze=(0,s.jsx)(Fe.ZP,{theme:{hashed:(0,te.nu)(),components:{Layout:{headerBg:"transparent",bodyBg:"transparent"},Menu:(0,d.Z)({},X({colorItemBg:((A=x.layout)===null||A===void 0||(A=A.header)===null||A===void 0?void 0:A.colorBgHeader)||"transparent",colorSubItemBg:((ie=x.layout)===null||ie===void 0||(ie=ie.header)===null||ie===void 0?void 0:ie.colorBgHeader)||"transparent",radiusItem:x.borderRadius,colorItemBgSelected:((fe=x.layout)===null||fe===void 0||(fe=fe.header)===null||fe===void 0?void 0:fe.colorBgMenuItemSelected)||(x==null?void 0:x.colorBgTextHover),itemHoverBg:((Se=x.layout)===null||Se===void 0||(Se=Se.header)===null||Se===void 0?void 0:Se.colorBgMenuItemHover)||(x==null?void 0:x.colorBgTextHover),colorItemBgSelectedHorizontal:((he=x.layout)===null||he===void 0||(he=he.header)===null||he===void 0?void 0:he.colorBgMenuItemSelected)||(x==null?void 0:x.colorBgTextHover),colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:((Ee=x.layout)===null||Ee===void 0||(Ee=Ee.header)===null||Ee===void 0?void 0:Ee.colorTextMenu)||(x==null?void 0:x.colorTextSecondary),colorItemTextHoverHorizontal:((_e=x.layout)===null||_e===void 0||(_e=_e.header)===null||_e===void 0?void 0:_e.colorTextMenuActive)||(x==null?void 0:x.colorText),colorItemTextSelectedHorizontal:((Pe=x.layout)===null||Pe===void 0||(Pe=Pe.header)===null||Pe===void 0?void 0:Pe.colorTextMenuSelected)||(x==null?void 0:x.colorTextBase),horizontalItemBorderRadius:4,colorItemTextHover:((je=x.layout)===null||je===void 0||(je=je.header)===null||je===void 0?void 0:je.colorTextMenuActive)||"rgba(0, 0, 0, 0.85)",horizontalItemHoverBg:((Ne=x.layout)===null||Ne===void 0||(Ne=Ne.header)===null||Ne===void 0?void 0:Ne.colorBgMenuItemHover)||"rgba(0, 0, 0, 0.04)",colorItemTextSelected:((we=x.layout)===null||we===void 0||(we=we.header)===null||we===void 0?void 0:we.colorTextMenuSelected)||"rgba(0, 0, 0, 1)",popupBg:x==null?void 0:x.colorBgElevated,subMenuItemBg:x==null?void 0:x.colorBgElevated,darkSubMenuItemBg:"transparent",darkPopupBg:x==null?void 0:x.colorBgElevated}))},token:{colorBgElevated:((He=x.layout)===null||He===void 0||(He=He.header)===null||He===void 0?void 0:He.colorBgHeader)||"transparent"}},children:(0,s.jsx)(wt,(0,d.Z)((0,d.Z)((0,d.Z)({theme:W?"dark":"light"},e),{},{className:"".concat(b,"-base-menu ").concat(U).trim()},e.menuProps),{},{style:(0,d.Z)({width:"100%"},(Ve=e.menuProps)===null||Ve===void 0?void 0:Ve.style),collapsed:!1,menuRenderType:"header",mode:"horizontal"}))});return N?N(e,ze):ze},[(n=x.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.colorBgHeader,(t=x.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.colorBgMenuItemSelected,(o=x.layout)===null||o===void 0||(o=o.header)===null||o===void 0?void 0:o.colorBgMenuItemHover,(i=x.layout)===null||i===void 0||(i=i.header)===null||i===void 0?void 0:i.colorTextMenu,(a=x.layout)===null||a===void 0||(a=a.header)===null||a===void 0?void 0:a.colorTextMenuActive,(c=x.layout)===null||c===void 0||(c=c.header)===null||c===void 0?void 0:c.colorTextMenuSelected,(u=x.layout)===null||u===void 0||(u=u.header)===null||u===void 0?void 0:u.colorBgMenuElevated,x.borderRadius,x==null?void 0:x.colorBgTextHover,x==null?void 0:x.colorTextSecondary,x==null?void 0:x.colorText,x==null?void 0:x.colorTextBase,x.colorBgElevated,W,e,b,U,N]);return ae((0,s.jsx)("div",{className:le()(b,U,R,(0,l.Z)({},"".concat(b,"-light"),!0)),style:j,children:(0,s.jsxs)("div",{ref:p,className:le()("".concat(b,"-main"),U,(0,l.Z)({},"".concat(b,"-wide"),h==="Fixed"&&T==="top")),children:[ue&&(0,s.jsxs)("div",{className:le()("".concat(b,"-main-left ").concat(U)),onClick:v,children:[(0,s.jsx)(Xn,(0,d.Z)({},e)),(0,s.jsx)("div",{className:"".concat(b,"-logo ").concat(U).trim(),id:"logo",children:ue},"logo")]}),(0,s.jsx)("div",{style:{flex:1},className:"".concat(b,"-menu ").concat(U).trim(),children:xe}),(y||V||e.avatarProps)&&(0,s.jsx)(Bt,(0,d.Z)((0,d.Z)({rightContentRender:y},e),{},{prefixCls:b}))]})}))},ta=function(e){var n,t,o;return(0,l.Z)({},e.componentCls,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({position:"relative",background:"transparent",display:"flex",alignItems:"center",marginBlock:0,marginInline:16,height:((n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.heightLayoutHeader)||56,boxSizing:"border-box","> a":{height:"100%"}},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16}),"&-collapsed-button",{minHeight:"22px",color:(t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.colorHeaderTitle,fontSize:"18px",marginInlineEnd:"16px"}),"&-logo",{position:"relative",marginInlineEnd:"16px",a:{display:"flex",alignItems:"center",height:"100%",minHeight:"22px",fontSize:"20px"},img:{height:"28px"},h1:{height:"32px",marginBlock:0,marginInline:0,marginInlineStart:8,fontWeight:"600",color:((o=e.layout)===null||o===void 0||(o=o.header)===null||o===void 0?void 0:o.colorHeaderTitle)||e.colorTextHeading,fontSize:"18px",lineHeight:"32px"},"&-mix":{display:"flex",alignItems:"center"}}),"&-logo-mobile",{minWidth:"24px",marginInlineEnd:0}))};function ra(r){return(0,Ae.Xj)("ProLayoutGlobalHeader",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[ta(n)]})}var oa=function(e,n){return e===!1?null:e?e(n,null):n},aa=function(e){var n=e.isMobile,t=e.logo,o=e.collapsed,i=e.onCollapse,a=e.rightContentRender,c=e.menuHeaderRender,u=e.onMenuHeaderClick,p=e.className,v=e.style,h=e.layout,y=e.children,R=e.splitMenus,j=e.menuData,N=e.prefixCls,T=(0,m.useContext)(Fe.ZP.ConfigContext),V=T.getPrefixCls,B=T.direction,z="".concat(N||V("pro"),"-global-header"),Z=ra(z),W=Z.wrapSSR,b=Z.hashId,w=le()(p,z,b);if(h==="mix"&&!n&&R){var ae=(j||[]).map(function(ye){return(0,d.Z)((0,d.Z)({},ye),{},{children:void 0,routes:void 0})}),U=_n(ae);return(0,s.jsx)(Ft,(0,d.Z)((0,d.Z)({mode:"horizontal"},e),{},{splitMenus:!1,menuData:U}))}var L=le()("".concat(z,"-logo"),b,(0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(z,"-logo-rtl"),B==="rtl"),"".concat(z,"-logo-mix"),h==="mix"),"".concat(z,"-logo-mobile"),n)),ue=(0,s.jsx)("span",{className:L,children:(0,s.jsx)("a",{children:Kn(t)})},"logo");return W((0,s.jsxs)("div",{className:w,style:(0,d.Z)({},v),children:[n&&(0,s.jsx)("span",{className:"".concat(z,"-collapsed-button ").concat(b).trim(),onClick:function(){i==null||i(!o)},children:(0,s.jsx)(po,{})}),n&&oa(c,ue),h==="mix"&&!n&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(Xn,(0,d.Z)({},e)),(0,s.jsx)("div",{className:L,onClick:u,children:Jn((0,d.Z)((0,d.Z)({},e),{},{collapsed:!1}),"headerTitleRender")})]}),(0,s.jsx)("div",{style:{flex:1},children:y}),(a||e.actionsRender||e.avatarProps)&&(0,s.jsx)(Bt,(0,d.Z)({rightContentRender:a},e))]}))},ia=function(e){var n,t,o,i;return(0,l.Z)({},"".concat(e.proComponentsCls,"-layout"),(0,l.Z)({},"".concat(e.antCls,"-layout-header").concat(e.componentCls),{height:((n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.heightLayoutHeader)||56,lineHeight:"".concat(((t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.heightLayoutHeader)||56,"px"),zIndex:19,width:"100%",paddingBlock:0,paddingInline:0,borderBlockEnd:"1px solid ".concat(e.colorSplit),backgroundColor:((o=e.layout)===null||o===void 0||(o=o.header)===null||o===void 0?void 0:o.colorBgHeader)||"rgba(255, 255, 255, 0.4)",WebkitBackdropFilter:"blur(8px)",backdropFilter:"blur(8px)",transition:"background-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)","&-fixed-header":{position:"fixed",insetBlockStart:0,width:"100%",zIndex:100,insetInlineEnd:0},"&-fixed-header-scroll":{backgroundColor:((i=e.layout)===null||i===void 0||(i=i.header)===null||i===void 0?void 0:i.colorBgScrollHeader)||"rgba(255, 255, 255, 0.8)"},"&-header-actions":{display:"flex",alignItems:"center",fontSize:"16",cursor:"pointer","& &-item":{paddingBlock:0,paddingInline:8,"&:hover":{color:e.colorText}}},"&-header-realDark":{boxShadow:"0 2px 8px 0 rgba(0, 0, 0, 65%)"},"&-header-actions-header-action":{transition:"width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)"}}))};function la(r){return(0,Ae.Xj)("ProLayoutHeader",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[ia(n)]})}function sa(r,e){var n=e.stylish,t=e.proLayoutCollapsedWidth;return(0,Ae.Xj)("ProLayoutHeaderStylish",function(o){var i=(0,d.Z)((0,d.Z)({},o),{},{componentCls:".".concat(r),proLayoutCollapsedWidth:t});return n?[(0,l.Z)({},"div".concat(o.proComponentsCls,"-layout"),(0,l.Z)({},"".concat(i.componentCls),n==null?void 0:n(i)))]:[]})}var Wt=Je.Z.Header,ca=function(e){var n,t,o,i=e.isMobile,a=e.fixedHeader,c=e.className,u=e.style,p=e.collapsed,v=e.prefixCls,h=e.onCollapse,y=e.layout,R=e.headerRender,j=e.headerContentRender,N=(0,m.useContext)(te.L_),T=N.token,V=(0,m.useContext)(Fe.ZP.ConfigContext),B=(0,m.useState)(!1),z=(0,F.Z)(B,2),Z=z[0],W=z[1],b=a||y==="mix",w=(0,m.useCallback)(function(){var A=y==="top",ie=_n(e.menuData||[]),fe=(0,s.jsx)(aa,(0,d.Z)((0,d.Z)({onCollapse:h},e),{},{menuData:ie,children:j&&j(e,null)}));return A&&!i&&(fe=(0,s.jsx)(Ft,(0,d.Z)((0,d.Z)({mode:"horizontal",onCollapse:h},e),{},{menuData:ie}))),R&&typeof R=="function"?R(e,fe):fe},[j,R,i,y,h,e]);(0,m.useEffect)(function(){var A,ie=(V==null||(A=V.getTargetContainer)===null||A===void 0?void 0:A.call(V))||document.body,fe=function(){var he,Ee=ie.scrollTop;return Ee>(((he=T.layout)===null||he===void 0||(he=he.header)===null||he===void 0?void 0:he.heightLayoutHeader)||56)&&!Z?(W(!0),!0):(Z&&W(!1),!1)};if(b&&typeof window!="undefined")return ie.addEventListener("scroll",fe,{passive:!0}),function(){ie.removeEventListener("scroll",fe)}},[(n=T.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.heightLayoutHeader,b,Z]);var ae=y==="top",U="".concat(v,"-layout-header"),L=la(U),ue=L.wrapSSR,ye=L.hashId,x=sa("".concat(U,".").concat(U,"-stylish"),{proLayoutCollapsedWidth:64,stylish:e.stylish}),xe=le()(c,ye,U,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(U,"-fixed-header"),b),"".concat(U,"-fixed-header-scroll"),Z),"".concat(U,"-mix"),y==="mix"),"".concat(U,"-fixed-header-action"),!p),"".concat(U,"-top-menu"),ae),"".concat(U,"-header"),!0),"".concat(U,"-stylish"),!!e.stylish));return y==="side"&&!i?null:x.wrapSSR(ue((0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(Fe.ZP,{theme:{hashed:(0,te.nu)(),components:{Layout:{headerBg:"transparent",bodyBg:"transparent"}}},children:[b&&(0,s.jsx)(Wt,{style:(0,d.Z)({height:((t=T.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.heightLayoutHeader)||56,lineHeight:"".concat(((o=T.layout)===null||o===void 0||(o=o.header)===null||o===void 0?void 0:o.heightLayoutHeader)||56,"px"),backgroundColor:"transparent",zIndex:19},u)}),(0,s.jsx)(Wt,{className:xe,style:u,children:w()})]})})))},ua=f(83832),da=f(85265),ma=f(11568),Ht=new ma.E4("antBadgeLoadingCircle",{"0%":{display:"none",opacity:0,overflow:"hidden"},"80%":{overflow:"hidden"},"100%":{display:"unset",opacity:1}}),pa=function(e){var n,t,o,i,a,c,u,p,v,h,y,R;return(0,l.Z)({},"".concat(e.proComponentsCls,"-layout"),(0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(e.antCls,"-layout-sider").concat(e.componentCls),{background:((n=e.layout)===null||n===void 0||(n=n.sider)===null||n===void 0?void 0:n.colorMenuBackground)||"transparent"}),e.componentCls,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({position:"relative",boxSizing:"border-box","&-menu":{position:"relative",zIndex:10,minHeight:"100%"}},"& ".concat(e.antCls,"-layout-sider-children"),{position:"relative",display:"flex",flexDirection:"column",height:"100%",paddingInline:(t=e.layout)===null||t===void 0||(t=t.sider)===null||t===void 0?void 0:t.paddingInlineLayoutMenu,paddingBlock:(o=e.layout)===null||o===void 0||(o=o.sider)===null||o===void 0?void 0:o.paddingBlockLayoutMenu,borderInlineEnd:"1px solid ".concat(e.colorSplit),marginInlineEnd:-1}),"".concat(e.antCls,"-menu"),(0,l.Z)((0,l.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:e.fontSizeSM,paddingBottom:4}),"".concat(e.antCls,"-menu-item:not(").concat(e.antCls,"-menu-item-selected):hover"),{color:(i=e.layout)===null||i===void 0||(i=i.sider)===null||i===void 0?void 0:i.colorTextMenuItemHover})),"&-logo",{position:"relative",display:"flex",alignItems:"center",justifyContent:"space-between",paddingInline:12,paddingBlock:16,color:(a=e.layout)===null||a===void 0||(a=a.sider)===null||a===void 0?void 0:a.colorTextMenu,cursor:"pointer",borderBlockEnd:"1px solid ".concat((c=e.layout)===null||c===void 0||(c=c.sider)===null||c===void 0?void 0:c.colorMenuItemDivider),"> a":{display:"flex",alignItems:"center",justifyContent:"center",minHeight:22,fontSize:22,"> img":{display:"inline-block",height:22,verticalAlign:"middle"},"> h1":{display:"inline-block",height:22,marginBlock:0,marginInlineEnd:0,marginInlineStart:6,color:(u=e.layout)===null||u===void 0||(u=u.sider)===null||u===void 0?void 0:u.colorTextMenuTitle,animationName:Ht,animationDuration:".4s",animationTimingFunction:"ease",fontWeight:600,fontSize:16,lineHeight:"22px",verticalAlign:"middle"}},"&-collapsed":(0,l.Z)({flexDirection:"column-reverse",margin:0,padding:12},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginBlockEnd:8,fontSize:16,transition:"font-size 0.2s ease-in-out,color 0.2s ease-in-out"})}),"&-actions",{display:"flex",alignItems:"center",justifyContent:"space-between",marginBlock:4,marginInline:0,color:(p=e.layout)===null||p===void 0||(p=p.sider)===null||p===void 0?void 0:p.colorTextMenu,"&-collapsed":{flexDirection:"column-reverse",paddingBlock:0,paddingInline:8,fontSize:16,transition:"font-size 0.3s ease-in-out"},"&-list":{color:(v=e.layout)===null||v===void 0||(v=v.sider)===null||v===void 0?void 0:v.colorTextMenuSecondary,"&-collapsed":{marginBlockEnd:8,animationName:"none"},"&-item":{paddingInline:6,paddingBlock:6,lineHeight:"16px",fontSize:16,cursor:"pointer",borderRadius:e.borderRadius,"&:hover":{background:e.colorBgTextHover}}},"&-avatar":{fontSize:14,paddingInline:8,paddingBlock:8,display:"flex",alignItems:"center",gap:e.marginXS,borderRadius:e.borderRadius,"& *":{cursor:"pointer"},"&:hover":{background:e.colorBgTextHover}}}),"&-hide-menu-collapsed",{insetInlineStart:"-".concat(e.proLayoutCollapsedWidth-12,"px"),position:"absolute"}),"&-extra",{marginBlockEnd:16,marginBlock:0,marginInline:16,"&-no-logo":{marginBlockStart:16}}),"&-links",{width:"100%",ul:{height:"auto"}}),"&-link-menu",{border:"none",boxShadow:"none",background:"transparent"}),"&-footer",{color:(h=e.layout)===null||h===void 0||(h=h.sider)===null||h===void 0?void 0:h.colorTextMenuSecondary,paddingBlockEnd:16,fontSize:e.fontSize,animationName:Ht,animationDuration:".4s",animationTimingFunction:"ease"})),"".concat(e.componentCls).concat(e.componentCls,"-fixed"),{position:"fixed",insetBlockStart:0,insetInlineStart:0,zIndex:"100",height:"100%","&-mix":{height:"calc(100% - ".concat(((y=e.layout)===null||y===void 0||(y=y.header)===null||y===void 0?void 0:y.heightLayoutHeader)||56,"px)"),insetBlockStart:"".concat(((R=e.layout)===null||R===void 0||(R=R.header)===null||R===void 0?void 0:R.heightLayoutHeader)||56,"px")}}))};function fa(r,e){var n=e.proLayoutCollapsedWidth;return(0,Ae.Xj)("ProLayoutSiderMenu",function(t){var o=(0,d.Z)((0,d.Z)({},t),{},{componentCls:".".concat(r),proLayoutCollapsedWidth:n});return[pa(o)]})}var Ut=function(e){var n,t=e.isMobile,o=e.siderWidth,i=e.collapsed,a=e.onCollapse,c=e.style,u=e.className,p=e.hide,v=e.prefixCls,h=e.getContainer,y=(0,m.useContext)(te.L_),R=y.token;(0,m.useEffect)(function(){t===!0&&(a==null||a(!0))},[t]);var j=(0,pt.Z)(e,["className","style"]),N=m.useContext(Fe.ZP.ConfigContext),T=N.direction,V=fa("".concat(v,"-sider"),{proLayoutCollapsedWidth:64}),B=V.wrapSSR,z=V.hashId,Z=le()("".concat(v,"-sider"),u,z);if(p)return null;var W=(0,M.X)(!i,function(){return a==null?void 0:a(!0)});return B(t?(0,s.jsx)(da.Z,(0,d.Z)((0,d.Z)({placement:T==="rtl"?"right":"left",className:le()("".concat(v,"-drawer-sider"),u)},W),{},{style:(0,d.Z)({padding:0,height:"100vh"},c),onClose:function(){a==null||a(!0)},maskClosable:!0,closable:!1,getContainer:h||!1,width:o,styles:{body:{height:"100vh",padding:0,display:"flex",flexDirection:"row",backgroundColor:(n=R.layout)===null||n===void 0||(n=n.sider)===null||n===void 0?void 0:n.colorMenuBackground}},children:(0,s.jsx)(Lt,(0,d.Z)((0,d.Z)({},j),{},{isMobile:!0,className:Z,collapsed:t?!1:i,splitMenus:!1,originCollapsed:i}))})):(0,s.jsx)(Lt,(0,d.Z)((0,d.Z)({className:Z,originCollapsed:i},j),{},{style:c})))},$t=f(76509),Qn=f(16305),va=function(e,n,t){if(t){var o=(0,Pn.Z)(t.keys()).find(function(a){try{return a.startsWith("http")?!1:(0,Qn.EQ)(a)(e)}catch(c){return console.log("key",a,c),!1}});if(o)return t.get(o)}if(n){var i=Object.keys(n).find(function(a){try{return a!=null&&a.startsWith("http")?!1:(0,Qn.EQ)(a)(e)}catch(c){return console.log("key",a,c),!1}});if(i)return n[i]}return{path:""}},Yn=function(e,n){var t=e.pathname,o=t===void 0?"/":t,i=e.breadcrumb,a=e.breadcrumbMap,c=e.formatMessage,u=e.title,p=e.menu,v=p===void 0?{locale:!1}:p,h=n?"":u||"",y=va(o,i,a);if(!y)return{title:h,id:"",pageName:h};var R=y.name;return v.locale!==!1&&y.locale&&c&&(R=c({id:y.locale||"",defaultMessage:y.name})),R?n||!u?{title:R,id:y.locale||"",pageName:R}:{title:"".concat(R," - ").concat(u),id:y.locale||"",pageName:R}:{title:h,id:y.locale||"",pageName:h}},Ci=function(e,n){return Yn(e,n).title},ga={"app.setting.pagestyle":"Page style setting","app.setting.pagestyle.dark":"Dark Menu style","app.setting.pagestyle.light":"Light Menu style","app.setting.pagestyle.realdark":"Dark style (Beta)","app.setting.content-width":"Content Width","app.setting.content-width.fixed":"Fixed","app.setting.content-width.fluid":"Fluid","app.setting.themecolor":"Theme Color","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.techBlue":"Tech Blue (default)","app.setting.themecolor.daybreak":"Daybreak Blue","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.sidermenutype":"SideMenu Type","app.setting.sidermenutype-sub":"Classic","app.setting.sidermenutype-group":"Grouping","app.setting.navigationmode":"Navigation Mode","app.setting.regionalsettings":"Regional Settings","app.setting.regionalsettings.header":"Header","app.setting.regionalsettings.menu":"Menu","app.setting.regionalsettings.footer":"Footer","app.setting.regionalsettings.menuHeader":"Menu Header","app.setting.sidemenu":"Side Menu Layout","app.setting.topmenu":"Top Menu Layout","app.setting.mixmenu":"Mix Menu Layout","app.setting.splitMenus":"Split Menus","app.setting.fixedheader":"Fixed Header","app.setting.fixedsidebar":"Fixed Sidebar","app.setting.fixedsidebar.hint":"Works on Side Menu Layout","app.setting.hideheader":"Hidden Header when scrolling","app.setting.hideheader.hint":"Works when Hidden Header is enabled","app.setting.othersettings":"Other Settings","app.setting.weakmode":"Weak Mode","app.setting.copy":"Copy Setting","app.setting.loading":"Loading theme","app.setting.copyinfo":"copy success\uFF0Cplease replace defaultSettings in src/models/setting.js","app.setting.production.hint":"Setting panel shows in development environment only, please manually modify"},ha=(0,d.Z)({},ga),ya={"app.setting.pagestyle":"Impostazioni di stile","app.setting.pagestyle.dark":"Tema scuro","app.setting.pagestyle.light":"Tema chiaro","app.setting.content-width":"Largezza contenuto","app.setting.content-width.fixed":"Fissa","app.setting.content-width.fluid":"Fluida","app.setting.themecolor":"Colore del tema","app.setting.themecolor.dust":"Rosso polvere","app.setting.themecolor.volcano":"Vulcano","app.setting.themecolor.sunset":"Arancione tramonto","app.setting.themecolor.cyan":"Ciano","app.setting.themecolor.green":"Verde polare","app.setting.themecolor.techBlue":"Tech Blu (default)","app.setting.themecolor.daybreak":"Blu cielo mattutino","app.setting.themecolor.geekblue":"Blu geek","app.setting.themecolor.purple":"Viola dorato","app.setting.navigationmode":"Modalit\xE0 di navigazione","app.setting.sidemenu":"Menu laterale","app.setting.topmenu":"Menu in testata","app.setting.mixmenu":"Menu misto","app.setting.splitMenus":"Menu divisi","app.setting.fixedheader":"Testata fissa","app.setting.fixedsidebar":"Menu laterale fisso","app.setting.fixedsidebar.hint":"Solo se selezionato Menu laterale","app.setting.hideheader":"Nascondi testata durante lo scorrimento","app.setting.hideheader.hint":"Solo se abilitato Nascondi testata durante lo scorrimento","app.setting.othersettings":"Altre impostazioni","app.setting.weakmode":"Inverti colori","app.setting.copy":"Copia impostazioni","app.setting.loading":"Carico tema...","app.setting.copyinfo":"Impostazioni copiate con successo! Incolla il contenuto in config/defaultSettings.js","app.setting.production.hint":"Questo pannello \xE8 visibile solo durante lo sviluppo. Le impostazioni devono poi essere modificate manulamente"},Ca=(0,d.Z)({},ya),xa={"app.setting.pagestyle":"\uC2A4\uD0C0\uC77C \uC124\uC815","app.setting.pagestyle.dark":"\uB2E4\uD06C \uBAA8\uB4DC","app.setting.pagestyle.light":"\uB77C\uC774\uD2B8 \uBAA8\uB4DC","app.setting.content-width":"\uCEE8\uD150\uCE20 \uB108\uBE44","app.setting.content-width.fixed":"\uACE0\uC815","app.setting.content-width.fluid":"\uD750\uB984","app.setting.themecolor":"\uD14C\uB9C8 \uC0C9\uC0C1","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.techBlue":"Tech Blu (default)","app.setting.themecolor.daybreak":"Daybreak Blue","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.navigationmode":"\uB124\uBE44\uAC8C\uC774\uC158 \uBAA8\uB4DC","app.setting.regionalsettings":"\uC601\uC5ED\uBCC4 \uC124\uC815","app.setting.regionalsettings.header":"\uD5E4\uB354","app.setting.regionalsettings.menu":"\uBA54\uB274","app.setting.regionalsettings.footer":"\uBC14\uB2E5\uAE00","app.setting.regionalsettings.menuHeader":"\uBA54\uB274 \uD5E4\uB354","app.setting.sidemenu":"\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58","app.setting.topmenu":"\uBA54\uB274 \uC0C1\uB2E8 \uBC30\uCE58","app.setting.mixmenu":"\uD63C\uD569\uD615 \uBC30\uCE58","app.setting.splitMenus":"\uBA54\uB274 \uBD84\uB9AC","app.setting.fixedheader":"\uD5E4\uB354 \uACE0\uC815","app.setting.fixedsidebar":"\uC0AC\uC774\uB4DC\uBC14 \uACE0\uC815","app.setting.fixedsidebar.hint":"'\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58'\uB97C \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.hideheader":"\uC2A4\uD06C\uB864 \uC911 \uD5E4\uB354 \uAC10\uCD94\uAE30","app.setting.hideheader.hint":"'\uD5E4\uB354 \uAC10\uCD94\uAE30 \uC635\uC158'\uC744 \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.othersettings":"\uB2E4\uB978 \uC124\uC815","app.setting.weakmode":"\uACE0\uB300\uBE44 \uBAA8\uB4DC","app.setting.copy":"\uC124\uC815\uAC12 \uBCF5\uC0AC","app.setting.loading":"\uD14C\uB9C8 \uB85C\uB529 \uC911","app.setting.copyinfo":"\uBCF5\uC0AC \uC131\uACF5. src/models/settings.js\uC5D0 \uC788\uB294 defaultSettings\uB97C \uAD50\uCCB4\uD574 \uC8FC\uC138\uC694.","app.setting.production.hint":"\uC124\uC815 \uD310\uB12C\uC740 \uAC1C\uBC1C \uD658\uACBD\uC5D0\uC11C\uB9CC \uBCF4\uC5EC\uC9D1\uB2C8\uB2E4. \uC9C1\uC811 \uC218\uB3D9\uC73C\uB85C \uBCC0\uACBD\uBC14\uB78D\uB2C8\uB2E4."},ba=(0,d.Z)({},xa),Sa={"app.setting.pagestyle":"\u6574\u4F53\u98CE\u683C\u8BBE\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u5355\u98CE\u683C","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u5355\u98CE\u683C","app.setting.pagestyle.realdark":"\u6697\u8272\u98CE\u683C(\u5B9E\u9A8C\u529F\u80FD)","app.setting.content-width":"\u5185\u5BB9\u533A\u57DF\u5BBD\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BBD","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u9898\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6781\u5149\u7EFF","app.setting.themecolor.techBlue":"\u79D1\u6280\u84DD\uFF08\u9ED8\u8BA4\uFF09","app.setting.themecolor.daybreak":"\u62C2\u6653","app.setting.themecolor.geekblue":"\u6781\u5BA2\u84DD","app.setting.themecolor.purple":"\u9171\u7D2B","app.setting.navigationmode":"\u5BFC\u822A\u6A21\u5F0F","app.setting.sidermenutype":"\u4FA7\u8FB9\u83DC\u5355\u7C7B\u578B","app.setting.sidermenutype-sub":"\u7ECF\u5178\u6A21\u5F0F","app.setting.sidermenutype-group":"\u5206\u7EC4\u6A21\u5F0F","app.setting.regionalsettings":"\u5185\u5BB9\u533A\u57DF","app.setting.regionalsettings.header":"\u9876\u680F","app.setting.regionalsettings.menu":"\u83DC\u5355","app.setting.regionalsettings.footer":"\u9875\u811A","app.setting.regionalsettings.menuHeader":"\u83DC\u5355\u5934","app.setting.sidemenu":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40","app.setting.topmenu":"\u9876\u90E8\u83DC\u5355\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u5355\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u4FA7\u8FB9\u83DC\u5355","app.setting.fixedsidebar.hint":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40\u65F6\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u65F6\u9690\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u65F6\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8BBE\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8D1D\u8BBE\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F7D\u4E3B\u9898","app.setting.copyinfo":"\u62F7\u8D1D\u6210\u529F\uFF0C\u8BF7\u5230 src/defaultSettings.js \u4E2D\u66FF\u6362\u9ED8\u8BA4\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u680F\u53EA\u5728\u5F00\u53D1\u73AF\u5883\u7528\u4E8E\u9884\u89C8\uFF0C\u751F\u4EA7\u73AF\u5883\u4E0D\u4F1A\u5C55\u73B0\uFF0C\u8BF7\u62F7\u8D1D\u540E\u624B\u52A8\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"},_a=(0,d.Z)({},Sa),Pa={"app.setting.pagestyle":"\u6574\u9AD4\u98A8\u683C\u8A2D\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u55AE\u98A8\u683C","app.setting.pagestyle.realdark":"\u6697\u8272\u98A8\u683C(\u5B9E\u9A8C\u529F\u80FD)","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u55AE\u98A8\u683C","app.setting.content-width":"\u5167\u5BB9\u5340\u57DF\u5BEC\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BEC","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u984C\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6975\u5149\u7DA0","app.setting.themecolor.techBlue":"\u79D1\u6280\u84DD\uFF08\u9ED8\u8A8D\uFF09","app.setting.themecolor.daybreak":"\u62C2\u66C9\u85CD","app.setting.themecolor.geekblue":"\u6975\u5BA2\u85CD","app.setting.themecolor.purple":"\u91AC\u7D2B","app.setting.navigationmode":"\u5C0E\u822A\u6A21\u5F0F","app.setting.sidemenu":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40","app.setting.topmenu":"\u9802\u90E8\u83DC\u55AE\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u55AE\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u5074\u908A\u83DC\u55AE","app.setting.fixedsidebar.hint":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40\u6642\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u6642\u96B1\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u6642\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8A2D\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8C9D\u8A2D\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F09\u4E3B\u984C","app.setting.copyinfo":"\u62F7\u8C9D\u6210\u529F\uFF0C\u8ACB\u5230 src/defaultSettings.js \u4E2D\u66FF\u63DB\u9ED8\u8A8D\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u6B04\u53EA\u5728\u958B\u767C\u74B0\u5883\u7528\u65BC\u9810\u89BD\uFF0C\u751F\u7522\u74B0\u5883\u4E0D\u6703\u5C55\u73FE\uFF0C\u8ACB\u62F7\u8C9D\u5F8C\u624B\u52D5\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"},Ia=(0,d.Z)({},Pa),Vt={"zh-CN":_a,"zh-TW":Ia,"en-US":ha,"it-IT":Ca,"ko-KR":ba},Ma=function(){if(!(0,ee.j)())return"zh-CN";var e=window.localStorage.getItem("umi_locale");return e||window.g_locale||navigator.language},Ea=function(){var e=Ma();return Vt[e]||Vt["zh-CN"]},Mn=f(67159),Ye=f(34155),Da=function(){var e;return typeof Ye=="undefined"?Mn.Z:((e=Ye)===null||Ye===void 0||(Ye={ALLUSERSPROFILE:"C:\\ProgramData",APPDATA:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133655684414351508",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_18964_RFUUGCWTXLXIVTVZ",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"\u5C0F\u4E38\u728A\u5B50",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",CUDA_PATH:"D:\\CUDA",CUDA_PATH_V11_0:"D:\\CUDA",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_6444_1262719628:"1",EFC_6444_1592913036:"1",EFC_6444_2283032206:"1",EFC_6444_2775293581:"1",EFC_6444_3789132940:"1",ELINK_INSTALL_PATH:"C:\\Program Files (x86)\\Ecloud\\CloudComputer\\drivers\\CMSS",FPS_BROWSER_APP_PROFILE_STRING:"Internet Explorer",FPS_BROWSER_USER_PROFILE_STRING:"Default",GIT_ASKPASS:"d:\\Program\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",HOME:"C:\\Users\\\<USER>\u82CF",HOMEDRIVE:"C:",HOMEPATH:"\\Users\\\u82CF\u82CF",INIT_CWD:"D:\\project\\web_app_0527v2\\web",JAVA_HOME:"D:\\Program Files\\javajdk",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\\<USER>\u82CF\\AppData\\Local",LOGONSERVER:"\\\\\u5C0F\u4E38\u728A\u5B50",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"production",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NODE_PATH:"D:\\\u524D\u7AEF\\nodejs\\node_modules",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_cache:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\npm-cache",npm_config_globalconfig:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\\<USER>\u82CF\\.npm-init.js",npm_config_local_prefix:"D:\\project\\web_app_0527v2\\web",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_noproxy:"",npm_config_npm_version:"10.7.0",npm_config_prefix:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm",npm_config_userconfig:"C:\\Users\\\<USER>\u82CF\\.npmrc",npm_config_user_agent:"npm/10.7.0 node/v20.15.1 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build:bj",npm_lifecycle_script:"cross-env REACT_APP_ENV=bj UMI_ENV=bj max build",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_engines_node:">=12.0.0",npm_package_json:"D:\\project\\web_app_0527v2\\web\\package.json",npm_package_name:"ant-design-pro",npm_package_version:"6.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\\<USER>\u5FDA\u5AC3\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"16",NVCUDASAMPLES11_0_ROOT:"D:\\CUDA",NVCUDASAMPLES_ROOT:"D:\\CUDA",NVTOOLSEXT_PATH:"C:\\Program Files\\NVIDIA Corporation\\NvToolsExt\\",OneDrive:"C:\\Users\\\<USER>\u82CF\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"D:\\project\\web_app_0527v2\\web\\node_modules\\.bin;D:\\project\\web_app_0527v2\\node_modules\\.bin;D:\\project\\node_modules\\.bin;D:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;D:\\Program Files\\javajdk\\bin;C:\\Python312\\Scripts\\;C:\\Python312\\;D:\\CUDA\\bin;D:\\CUDA\\libnvvp;D:\\Anaconda;D:\\Anaconda\\Library\\mingw-w64\\bin;D:\\Anaconda\\Library\\usr\\bin;D:\\Anaconda\\Library\\bin;D:\\Anaconda\\Scripts;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;D:\\\u524D\u7AEF\\nodejs\\node_global;D;\\\u524D\u7AEF\\mongoDB\\server\\bin;C;\\Program Files\\NVIDIA Corporation\\Nsight Compute 2020.1.1\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;D:\\\u524D\u7AEF\\\u9879\u76EE\\2\u5FAE\u4FE1\u5C0F\u7A0B\u5E8F\\\u5FAE\u4FE1web\u5F00\u53D1\u8005\u5DE5\u5177\\dll;C:\\Program File;\\MySQL\\MySQL Server 8.0\\bin;C:\\Program Files (x86)\\PuTTY\\;D:\\Program Files\\TortoiseGit\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;D:\\Program\\Git\\cmd;D:\\Program Files\\javajdk\\bin;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\;C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Microsoft\\WindowsApps;D:\\\u524D\u7AEF\\webstorm\\WebStorm 2021.2.3\\bin;;D:\\\u524D\u7AEF\\nodejs\\node_global;C:\\Program Files\\JetBrains\\PyCharm Community Edition 2024.2.0.1\\bin;;C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm;D:\\Program\\Microsoft VS Code\\bin;C:\\MinGW\\bin;D:\\Program\\cursor\\resources\\app\\bin",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"AMD64 Family 25 Model 80 Stepping 0, AuthenticAMD",PROCESSOR_LEVEL:"25",PROCESSOR_REVISION:"5000",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\\<USER>\u82CF\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\Program Files\\JetBrains\\PyCharm Community Edition 2024.2.0.1\\bin;",REACT_APP_ENV:"bj",REACT_APP_WEB_SERVER_SOCKET_PORT:"http://*************:9044/",SESSIONNAME:"Console",SystemDrive:"C:",SystemRoot:"C:\\WINDOWS",TEMP:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.100.2",TMP:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Temp",UMI_APP_BACKEND_URL:"http://localhost:8001",UMI_DIR:"D:\\project\\web_app_0527v2\\web\\node_modules\\umi",UMI_ENV:"bj",UMI_PRESETS:"D:\\project\\web_app_0527v2\\web\\node_modules\\@umijs\\max\\dist\\preset.js",USERDOMAIN:"\u5C0F\u4E38\u728A\u5B50",USERDOMAIN_ROAMINGPROFILE:"\u5C0F\u4E38\u728A\u5B50",USERNAME:"\u82CF\u82CF",USERPROFILE:"C:\\Users\\\<USER>\u82CF",VSCODE_GIT_ASKPASS_EXTRA_ARGS:"",VSCODE_GIT_ASKPASS_MAIN:"d:\\Program\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"D:\\Program\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-c52bfb7121-sock",VSCODE_INJECTION:"1",WebStorm:"D:\\\u524D\u7AEF\\webstorm\\WebStorm 2021.2.3\\bin;",windir:"C:\\WINDOWS",ZXCLOUD_INSTALL_PATH:"C:\\Program Files (x86)\\uSmartView",ZXVE_CLIENT_AGENT_UUID:"3577B2A6-F8E2-4987-9B98-4748E6A96F38",ZXVE_IRAI:"C:\\Program Files (x86)\\uSmartView\\client\\uSmartView.exe",ZXVE_NEW_UDS:"0x00000001"})===null||Ye===void 0?void 0:Ye.ANTD_VERSION)||Mn.Z},Ra=function(e){var n,t,o,i,a,c,u,p,v,h,y,R,j,N,T,V,B,z,Z,W,b,w,ae,U,L,ue,ye,x,xe,A,ie,fe;return(n=Da())!==null&&n!==void 0&&n.startsWith("5")?{}:(0,l.Z)((0,l.Z)((0,l.Z)({},e.componentCls,(0,l.Z)((0,l.Z)({width:"100%",height:"100%"},"".concat(e.proComponentsCls,"-base-menu"),(b={color:(t=e.layout)===null||t===void 0||(t=t.sider)===null||t===void 0?void 0:t.colorTextMenu},(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)(b,"".concat(e.antCls,"-menu-sub"),{backgroundColor:"transparent!important",color:(o=e.layout)===null||o===void 0||(o=o.sider)===null||o===void 0?void 0:o.colorTextMenu}),"& ".concat(e.antCls,"-layout"),{backgroundColor:"transparent",width:"100%"}),"".concat(e.antCls,"-menu-submenu-expand-icon, ").concat(e.antCls,"-menu-submenu-arrow"),{color:"inherit"}),"&".concat(e.antCls,"-menu"),(0,l.Z)((0,l.Z)({color:(i=e.layout)===null||i===void 0||(i=i.sider)===null||i===void 0?void 0:i.colorTextMenu},"".concat(e.antCls,"-menu-item"),{"*":{transition:"none !important"}}),"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),"&".concat(e.antCls,"-menu-inline"),(0,l.Z)({},"".concat(e.antCls,"-menu-selected::after,").concat(e.antCls,"-menu-item-selected::after"),{display:"none"})),"".concat(e.antCls,"-menu-sub ").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),"".concat(e.antCls,`-menu-item:active, 
        `).concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"}),"&".concat(e.antCls,"-menu-light"),(0,l.Z)({},"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,`-menu-submenu-active, 
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,l.Z)({color:(a=e.layout)===null||a===void 0||(a=a.sider)===null||a===void 0?void 0:a.colorTextMenuActive,borderRadius:e.borderRadius},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(c=e.layout)===null||c===void 0||(c=c.sider)===null||c===void 0?void 0:c.colorTextMenuActive}))),"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(0,l.Z)((0,l.Z)({},"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:(u=e.layout)===null||u===void 0||(u=u.sider)===null||u===void 0?void 0:u.colorBgMenuItemSelected,borderRadius:e.borderRadius}),"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,l.Z)({color:(p=e.layout)===null||p===void 0||(p=p.sider)===null||p===void 0?void 0:p.colorTextMenuActive,borderRadius:e.borderRadius,backgroundColor:"".concat((v=e.layout)===null||v===void 0||(v=v.header)===null||v===void 0?void 0:v.colorBgMenuItemHover," !important")},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(h=e.layout)===null||h===void 0||(h=h.sider)===null||h===void 0?void 0:h.colorTextMenuActive}))),"".concat(e.antCls,"-menu-item-selected"),{color:(y=e.layout)===null||y===void 0||(y=y.sider)===null||y===void 0?void 0:y.colorTextMenuSelected}),(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)(b,"".concat(e.antCls,"-menu-submenu-selected"),{color:(R=e.layout)===null||R===void 0||(R=R.sider)===null||R===void 0?void 0:R.colorTextMenuSelected}),"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-inline) ").concat(e.antCls,"-menu-submenu-open"),{color:(j=e.layout)===null||j===void 0||(j=j.sider)===null||j===void 0?void 0:j.colorTextMenuSelected}),"&".concat(e.antCls,"-menu-vertical"),(0,l.Z)({},"".concat(e.antCls,"-menu-submenu-selected"),{borderRadius:e.borderRadius,color:(N=e.layout)===null||N===void 0||(N=N.sider)===null||N===void 0?void 0:N.colorTextMenuSelected})),"".concat(e.antCls,"-menu-submenu:hover > ").concat(e.antCls,"-menu-submenu-title > ").concat(e.antCls,"-menu-submenu-arrow"),{color:(T=e.layout)===null||T===void 0||(T=T.sider)===null||T===void 0?void 0:T.colorTextMenuActive}),"&".concat(e.antCls,"-menu-horizontal"),(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(e.antCls,`-menu-item:hover,
          `).concat(e.antCls,`-menu-submenu:hover,
          `).concat(e.antCls,`-menu-item-active,
          `).concat(e.antCls,"-menu-submenu-active"),{borderRadius:4,transition:"none",color:(V=e.layout)===null||V===void 0||(V=V.header)===null||V===void 0?void 0:V.colorTextMenuActive,backgroundColor:"".concat((B=e.layout)===null||B===void 0||(B=B.header)===null||B===void 0?void 0:B.colorBgMenuItemHover," !important")}),"".concat(e.antCls,`-menu-item-open,
          `).concat(e.antCls,`-menu-submenu-open,
          `).concat(e.antCls,`-menu-item-selected,
          `).concat(e.antCls,"-menu-submenu-selected"),(0,l.Z)({backgroundColor:(z=e.layout)===null||z===void 0||(z=z.header)===null||z===void 0?void 0:z.colorBgMenuItemSelected,borderRadius:e.borderRadius,transition:"none",color:"".concat((Z=e.layout)===null||Z===void 0||(Z=Z.header)===null||Z===void 0?void 0:Z.colorTextMenuSelected," !important")},"".concat(e.antCls,"-menu-submenu-arrow"),{color:"".concat((W=e.layout)===null||W===void 0||(W=W.header)===null||W===void 0?void 0:W.colorTextMenuSelected," !important")})),"> ".concat(e.antCls,"-menu-item, > ").concat(e.antCls,"-menu-submenu"),{paddingInline:16,marginInline:4}),"> ".concat(e.antCls,"-menu-item::after, > ").concat(e.antCls,"-menu-submenu::after"),{display:"none"})))),"".concat(e.proComponentsCls,"-top-nav-header-base-menu"),(0,l.Z)((0,l.Z)({},"&".concat(e.antCls,"-menu"),(0,l.Z)({color:(w=e.layout)===null||w===void 0||(w=w.header)===null||w===void 0?void 0:w.colorTextMenu},"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),"&".concat(e.antCls,"-menu-light"),(0,l.Z)((0,l.Z)({},"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,`-menu-submenu-active, 
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,l.Z)({color:(ae=e.layout)===null||ae===void 0||(ae=ae.header)===null||ae===void 0?void 0:ae.colorTextMenuActive,borderRadius:e.borderRadius,transition:"none",backgroundColor:(U=e.layout)===null||U===void 0||(U=U.header)===null||U===void 0?void 0:U.colorBgMenuItemSelected},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(L=e.layout)===null||L===void 0||(L=L.header)===null||L===void 0?void 0:L.colorTextMenuActive})),"".concat(e.antCls,"-menu-item-selected"),{color:(ue=e.layout)===null||ue===void 0||(ue=ue.header)===null||ue===void 0?void 0:ue.colorTextMenuSelected,borderRadius:e.borderRadius,backgroundColor:(ye=e.layout)===null||ye===void 0||(ye=ye.header)===null||ye===void 0?void 0:ye.colorBgMenuItemSelected})))),"".concat(e.antCls,"-menu-sub").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),"".concat(e.antCls,"-menu-submenu-popup"),(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"},"".concat(e.antCls,"-menu"),(0,l.Z)({background:"transparent !important",backgroundColor:"transparent !important"},"".concat(e.antCls,`-menu-item:active, 
        `).concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"})),"".concat(e.antCls,"-menu-item-selected"),{color:(x=e.layout)===null||x===void 0||(x=x.sider)===null||x===void 0?void 0:x.colorTextMenuSelected}),"".concat(e.antCls,"-menu-submenu-selected"),{color:(xe=e.layout)===null||xe===void 0||(xe=xe.sider)===null||xe===void 0?void 0:xe.colorTextMenuSelected}),"".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(0,l.Z)((0,l.Z)({},"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:"rgba(0, 0, 0, 0.04)",borderRadius:e.borderRadius,color:(A=e.layout)===null||A===void 0||(A=A.sider)===null||A===void 0?void 0:A.colorTextMenuSelected}),"".concat(e.antCls,`-menu-item:hover, 
          `).concat(e.antCls,`-menu-item-active,
          `).concat(e.antCls,"-menu-submenu-title:hover"),(0,l.Z)({color:(ie=e.layout)===null||ie===void 0||(ie=ie.sider)===null||ie===void 0?void 0:ie.colorTextMenuActive,borderRadius:e.borderRadius},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(fe=e.layout)===null||fe===void 0||(fe=fe.sider)===null||fe===void 0?void 0:fe.colorTextMenuActive}))))},Ta=function(e){var n,t,o,i;return(0,l.Z)((0,l.Z)({},"".concat(e.antCls,"-layout"),{backgroundColor:"transparent !important"}),e.componentCls,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"& ".concat(e.antCls,"-layout"),{display:"flex",backgroundColor:"transparent",width:"100%"}),"".concat(e.componentCls,"-content"),{display:"flex",flexDirection:"column",width:"100%",backgroundColor:((n=e.layout)===null||n===void 0||(n=n.pageContainer)===null||n===void 0?void 0:n.colorBgPageContainer)||"transparent",position:"relative",paddingBlock:(t=e.layout)===null||t===void 0||(t=t.pageContainer)===null||t===void 0?void 0:t.paddingBlockPageContainerContent,paddingInline:(o=e.layout)===null||o===void 0||(o=o.pageContainer)===null||o===void 0?void 0:o.paddingInlinePageContainerContent,"&-has-page-container":{padding:0}}),"".concat(e.componentCls,"-container"),{width:"100%",display:"flex",flexDirection:"column",minWidth:0,minHeight:0,backgroundColor:"transparent"}),"".concat(e.componentCls,"-bg-list"),{pointerEvents:"none",position:"fixed",overflow:"hidden",insetBlockStart:0,insetInlineStart:0,zIndex:0,height:"100%",width:"100%",background:(i=e.layout)===null||i===void 0?void 0:i.bgLayout}))};function Aa(r){return(0,Ae.Xj)("ProLayout",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[Ta(n),Ra(n)]})}function Oa(r){if(!r||r==="/")return["/"];var e=r.split("/").filter(function(n){return n});return e.map(function(n,t){return"/".concat(e.slice(0,t+1).join("/"))})}var qe=f(34155),Na=function(){var e;return typeof qe=="undefined"?Mn.Z:((e=qe)===null||qe===void 0||(qe={ALLUSERSPROFILE:"C:\\ProgramData",APPDATA:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133655684414351508",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_18964_RFUUGCWTXLXIVTVZ",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"\u5C0F\u4E38\u728A\u5B50",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",CUDA_PATH:"D:\\CUDA",CUDA_PATH_V11_0:"D:\\CUDA",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_6444_1262719628:"1",EFC_6444_1592913036:"1",EFC_6444_2283032206:"1",EFC_6444_2775293581:"1",EFC_6444_3789132940:"1",ELINK_INSTALL_PATH:"C:\\Program Files (x86)\\Ecloud\\CloudComputer\\drivers\\CMSS",FPS_BROWSER_APP_PROFILE_STRING:"Internet Explorer",FPS_BROWSER_USER_PROFILE_STRING:"Default",GIT_ASKPASS:"d:\\Program\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",HOME:"C:\\Users\\\<USER>\u82CF",HOMEDRIVE:"C:",HOMEPATH:"\\Users\\\u82CF\u82CF",INIT_CWD:"D:\\project\\web_app_0527v2\\web",JAVA_HOME:"D:\\Program Files\\javajdk",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\\<USER>\u82CF\\AppData\\Local",LOGONSERVER:"\\\\\u5C0F\u4E38\u728A\u5B50",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"production",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NODE_PATH:"D:\\\u524D\u7AEF\\nodejs\\node_modules",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_cache:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\npm-cache",npm_config_globalconfig:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\\<USER>\u82CF\\.npm-init.js",npm_config_local_prefix:"D:\\project\\web_app_0527v2\\web",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_noproxy:"",npm_config_npm_version:"10.7.0",npm_config_prefix:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm",npm_config_userconfig:"C:\\Users\\\<USER>\u82CF\\.npmrc",npm_config_user_agent:"npm/10.7.0 node/v20.15.1 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build:bj",npm_lifecycle_script:"cross-env REACT_APP_ENV=bj UMI_ENV=bj max build",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_engines_node:">=12.0.0",npm_package_json:"D:\\project\\web_app_0527v2\\web\\package.json",npm_package_name:"ant-design-pro",npm_package_version:"6.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\\<USER>\u5FDA\u5AC3\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"16",NVCUDASAMPLES11_0_ROOT:"D:\\CUDA",NVCUDASAMPLES_ROOT:"D:\\CUDA",NVTOOLSEXT_PATH:"C:\\Program Files\\NVIDIA Corporation\\NvToolsExt\\",OneDrive:"C:\\Users\\\<USER>\u82CF\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"D:\\project\\web_app_0527v2\\web\\node_modules\\.bin;D:\\project\\web_app_0527v2\\node_modules\\.bin;D:\\project\\node_modules\\.bin;D:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;D:\\Program Files\\javajdk\\bin;C:\\Python312\\Scripts\\;C:\\Python312\\;D:\\CUDA\\bin;D:\\CUDA\\libnvvp;D:\\Anaconda;D:\\Anaconda\\Library\\mingw-w64\\bin;D:\\Anaconda\\Library\\usr\\bin;D:\\Anaconda\\Library\\bin;D:\\Anaconda\\Scripts;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;D:\\\u524D\u7AEF\\nodejs\\node_global;D;\\\u524D\u7AEF\\mongoDB\\server\\bin;C;\\Program Files\\NVIDIA Corporation\\Nsight Compute 2020.1.1\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;D:\\\u524D\u7AEF\\\u9879\u76EE\\2\u5FAE\u4FE1\u5C0F\u7A0B\u5E8F\\\u5FAE\u4FE1web\u5F00\u53D1\u8005\u5DE5\u5177\\dll;C:\\Program File;\\MySQL\\MySQL Server 8.0\\bin;C:\\Program Files (x86)\\PuTTY\\;D:\\Program Files\\TortoiseGit\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;D:\\Program\\Git\\cmd;D:\\Program Files\\javajdk\\bin;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\;C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Microsoft\\WindowsApps;D:\\\u524D\u7AEF\\webstorm\\WebStorm 2021.2.3\\bin;;D:\\\u524D\u7AEF\\nodejs\\node_global;C:\\Program Files\\JetBrains\\PyCharm Community Edition 2024.2.0.1\\bin;;C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm;D:\\Program\\Microsoft VS Code\\bin;C:\\MinGW\\bin;D:\\Program\\cursor\\resources\\app\\bin",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"AMD64 Family 25 Model 80 Stepping 0, AuthenticAMD",PROCESSOR_LEVEL:"25",PROCESSOR_REVISION:"5000",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\\<USER>\u82CF\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\Program Files\\JetBrains\\PyCharm Community Edition 2024.2.0.1\\bin;",REACT_APP_ENV:"bj",REACT_APP_WEB_SERVER_SOCKET_PORT:"http://*************:9044/",SESSIONNAME:"Console",SystemDrive:"C:",SystemRoot:"C:\\WINDOWS",TEMP:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.100.2",TMP:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Temp",UMI_APP_BACKEND_URL:"http://localhost:8001",UMI_DIR:"D:\\project\\web_app_0527v2\\web\\node_modules\\umi",UMI_ENV:"bj",UMI_PRESETS:"D:\\project\\web_app_0527v2\\web\\node_modules\\@umijs\\max\\dist\\preset.js",USERDOMAIN:"\u5C0F\u4E38\u728A\u5B50",USERDOMAIN_ROAMINGPROFILE:"\u5C0F\u4E38\u728A\u5B50",USERNAME:"\u82CF\u82CF",USERPROFILE:"C:\\Users\\\<USER>\u82CF",VSCODE_GIT_ASKPASS_EXTRA_ARGS:"",VSCODE_GIT_ASKPASS_MAIN:"d:\\Program\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"D:\\Program\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-c52bfb7121-sock",VSCODE_INJECTION:"1",WebStorm:"D:\\\u524D\u7AEF\\webstorm\\WebStorm 2021.2.3\\bin;",windir:"C:\\WINDOWS",ZXCLOUD_INSTALL_PATH:"C:\\Program Files (x86)\\uSmartView",ZXVE_CLIENT_AGENT_UUID:"3577B2A6-F8E2-4987-9B98-4748E6A96F38",ZXVE_IRAI:"C:\\Program Files (x86)\\uSmartView\\client\\uSmartView.exe",ZXVE_NEW_UDS:"0x00000001"})===null||qe===void 0?void 0:qe.ANTD_VERSION)||Mn.Z},Za=function(e,n,t){var o=e,i=o.breadcrumbName,a=o.title,c=o.path,u=t.findIndex(function(p){return p.linkPath===e.path})===t.length-1;return u?(0,s.jsx)("span",{children:a||i}):(0,s.jsx)("span",{onClick:c?function(){return location.href=c}:void 0,children:a||i})},wa=function(e,n){var t=n.formatMessage,o=n.menu;return e.locale&&t&&(o==null?void 0:o.locale)!==!1?t({id:e.locale,defaultMessage:e.name}):e.name},ja=function(e,n){var t=e.get(n);if(!t){var o=Array.from(e.keys())||[],i=o.find(function(a){try{return a!=null&&a.startsWith("http")?!1:(0,Qn.EQ)(a.replace("?",""))(n)}catch(c){return console.log("path",a,c),!1}});i&&(t=e.get(i))}return t||{path:""}},La=function(e){var n=e.location,t=e.breadcrumbMap;return{location:n,breadcrumbMap:t}},Ba=function(e,n,t){var o=Oa(e==null?void 0:e.pathname),i=o.map(function(a){var c=ja(n,a),u=wa(c,t),p=c.hideInBreadcrumb;return u&&!p?{linkPath:a,breadcrumbName:u,title:u,component:c.component}:{linkPath:"",breadcrumbName:"",title:""}}).filter(function(a){return a&&a.linkPath});return i},Fa=function(e){var n=La(e),t=n.location,o=n.breadcrumbMap;return t&&t.pathname&&o?Ba(t,o,e):[]},Wa=function(e,n){var t=e.breadcrumbRender,o=e.itemRender,i=n.breadcrumbProps||{},a=i.minLength,c=a===void 0?2:a,u=Fa(e),p=function(y){for(var R=o||Za,j=arguments.length,N=new Array(j>1?j-1:0),T=1;T<j;T++)N[T-1]=arguments[T];return R==null?void 0:R.apply(void 0,[(0,d.Z)((0,d.Z)({},y),{},{path:y.linkPath||y.path})].concat(N))},v=u;return t&&(v=t(v||[])||void 0),(v&&v.length<c||t===!1)&&(v=void 0),(0,E.n)(Na(),"5.3.0")>-1?{items:v,itemRender:p}:{routes:v,itemRender:p}};function Ha(r){return(0,Pn.Z)(r).reduce(function(e,n){var t=(0,F.Z)(n,2),o=t[0],i=t[1];return e[o]=i,e},{})}var Ua=function r(e,n,t,o){var i=Dr(e,(n==null?void 0:n.locale)||!1,t,!0),a=i.menuData,c=i.breadcrumb;return o?r(o(a),n,t,void 0):{breadcrumb:Ha(c),breadcrumbMap:c,menuData:a}},$a=f(51812),Va=function(e){var n=(0,m.useState)({}),t=(0,F.Z)(n,2),o=t[0],i=t[1];return(0,m.useEffect)(function(){i((0,$a.Y)({layout:(0,$n.Z)(e.layout)!=="object"?e.layout:void 0,navTheme:e.navTheme,menuRender:e.menuRender,footerRender:e.footerRender,menuHeaderRender:e.menuHeaderRender,headerRender:e.headerRender,fixSiderbar:e.fixSiderbar}))},[e.layout,e.navTheme,e.menuRender,e.footerRender,e.menuHeaderRender,e.headerRender,e.fixSiderbar]),o},za=["id","defaultMessage"],ka=["fixSiderbar","navTheme","layout"],zt=0,Ga=function(e,n){var t;return e.headerRender===!1||e.pure?null:(0,s.jsx)(ca,(0,d.Z)((0,d.Z)({matchMenuKeys:n},e),{},{stylish:(t=e.stylish)===null||t===void 0?void 0:t.header}))},Ka=function(e){return e.footerRender===!1||e.pure?null:e.footerRender?e.footerRender((0,d.Z)({},e),(0,s.jsx)(lo,{})):null},Xa=function(e,n){var t,o=e.layout,i=e.isMobile,a=e.selectedKeys,c=e.openKeys,u=e.splitMenus,p=e.suppressSiderWhenMenuEmpty,v=e.menuRender;if(e.menuRender===!1||e.pure)return null;var h=e.menuData;if(u&&(c!==!1||o==="mix")&&!i){var y=a||n,R=(0,F.Z)(y,1),j=R[0];if(j){var N;h=((N=e.menuData)===null||N===void 0||(N=N.find(function(z){return z.key===j}))===null||N===void 0?void 0:N.children)||[]}else h=[]}var T=_n(h||[]);if(T&&(T==null?void 0:T.length)<1&&(u||p))return null;if(o==="top"&&!i){var V;return(0,s.jsx)(Ut,(0,d.Z)((0,d.Z)({matchMenuKeys:n},e),{},{hide:!0,stylish:(V=e.stylish)===null||V===void 0?void 0:V.sider}))}var B=(0,s.jsx)(Ut,(0,d.Z)((0,d.Z)({matchMenuKeys:n},e),{},{menuData:T,stylish:(t=e.stylish)===null||t===void 0?void 0:t.sider}));return v?v(e,B):B},Ja=function(e,n){var t=n.pageTitleRender,o=Yn(e);if(t===!1)return{title:n.title||"",id:"",pageName:""};if(t){var i=t(e,o.title,o);if(typeof i=="string")return Yn((0,d.Z)((0,d.Z)({},o),{},{title:i}));(0,ft.ZP)(typeof i=="string","pro-layout: renderPageTitle return value should be a string")}return o},Qa=function(e,n,t){return e?n?64:t:0},Ya=function(e){var n,t,o,i,a,c,u,p,v,h,y,R,j,N,T=e||{},V=T.children,B=T.onCollapse,z=T.location,Z=z===void 0?{pathname:"/"}:z,W=T.contentStyle,b=T.route,w=T.defaultCollapsed,ae=T.style,U=T.siderWidth,L=T.menu,ue=T.siderMenuType,ye=T.isChildrenLayout,x=T.menuDataRender,xe=T.actionRef,A=T.bgLayoutImgList,ie=T.formatMessage,fe=T.loading,Se=(0,m.useMemo)(function(){return U||(e.layout==="mix"?215:256)},[e.layout,U]),he=(0,m.useContext)(Fe.ZP.ConfigContext),Ee=(n=e.prefixCls)!==null&&n!==void 0?n:he.getPrefixCls("pro"),_e=(0,Q.Z)(!1,{value:L==null?void 0:L.loading,onChange:L==null?void 0:L.onLoadingChange}),Pe=(0,F.Z)(_e,2),je=Pe[0],Ne=Pe[1],we=(0,m.useState)(function(){return zt+=1,"pro-layout-".concat(zt)}),He=(0,F.Z)(we,1),Ve=He[0],ze=(0,m.useCallback)(function(Re){var Ge=Re.id,An=Re.defaultMessage,yn=(0,G.Z)(Re,za);if(ie)return ie((0,d.Z)({id:Ge,defaultMessage:An},yn));var Cn=Ea();return Cn[Ge]?Cn[Ge]:An},[ie]),ke=(0,jr.ZP)([Ve,L==null?void 0:L.params],function(){var Re=(0,k.Z)((0,K.Z)().mark(function Ge(An){var yn,Cn,ir,lr;return(0,K.Z)().wrap(function(rn){for(;;)switch(rn.prev=rn.next){case 0:return Cn=(0,F.Z)(An,2),ir=Cn[1],Ne(!0),rn.next=4,L==null||(yn=L.request)===null||yn===void 0?void 0:yn.call(L,ir||{},(b==null?void 0:b.children)||(b==null?void 0:b.routes)||[]);case 4:return lr=rn.sent,Ne(!1),rn.abrupt("return",lr);case 7:case"end":return rn.stop()}},Ge)}));return function(Ge){return Re.apply(this,arguments)}}(),{revalidateOnFocus:!1,shouldRetryOnError:!1,revalidateOnReconnect:!1}),pn=ke.data,En=ke.mutate,De=ke.isLoading;(0,m.useEffect)(function(){Ne(De)},[De]);var We=(0,Lr.kY)(),Le=We.cache;(0,m.useEffect)(function(){return function(){Le instanceof Map&&Le.delete(Ve)}},[]);var Dn=(0,m.useMemo)(function(){return Ua(pn||(b==null?void 0:b.children)||(b==null?void 0:b.routes)||[],L,ze,x)},[ze,L,x,pn,b==null?void 0:b.children,b==null?void 0:b.routes]),qn=Dn||{},ei=qn.breadcrumb,kt=qn.breadcrumbMap,Gt=qn.menuData,fn=Gt===void 0?[]:Gt;xe&&L!==null&&L!==void 0&&L.request&&(xe.current={reload:function(){En()}});var vn=(0,m.useMemo)(function(){return Zr(Z.pathname||"/",fn||[],!0)},[Z.pathname,fn]),et=(0,m.useMemo)(function(){return Array.from(new Set(vn.map(function(Re){return Re.key||Re.path||""})))},[vn]),Kt=vn[vn.length-1]||{},Xt=Va(Kt),Rn=(0,d.Z)((0,d.Z)({},e),Xt),ni=Rn.fixSiderbar,xi=Rn.navTheme,gn=Rn.layout,ti=(0,G.Z)(Rn,ka),en=me(),nn=(0,m.useMemo)(function(){return(en==="sm"||en==="xs")&&!e.disableMobile},[en,e.disableMobile]),ri=gn!=="top"&&!nn,oi=(0,Q.Z)(function(){return w!==void 0?w:!!(nn||en==="md")},{value:e.collapsed,onChange:B}),Jt=(0,F.Z)(oi,2),hn=Jt[0],Qt=Jt[1],tn=(0,pt.Z)((0,d.Z)((0,d.Z)((0,d.Z)({prefixCls:Ee},e),{},{siderWidth:Se},Xt),{},{formatMessage:ze,breadcrumb:ei,menu:(0,d.Z)((0,d.Z)({},L),{},{type:ue||(L==null?void 0:L.type),loading:je}),layout:gn}),["className","style","breadcrumbRender"]),nt=Ja((0,d.Z)((0,d.Z)({pathname:Z.pathname},tn),{},{breadcrumbMap:kt}),e),ai=Wa((0,d.Z)((0,d.Z)({},tn),{},{breadcrumbRender:e.breadcrumbRender,breadcrumbMap:kt}),e),Tn=Xa((0,d.Z)((0,d.Z)({},tn),{},{menuData:fn,onCollapse:Qt,isMobile:nn,collapsed:hn}),et),tt=Ga((0,d.Z)((0,d.Z)({},tn),{},{children:null,hasSiderMenu:!!Tn,menuData:fn,isMobile:nn,collapsed:hn,onCollapse:Qt}),et),Yt=Ka((0,d.Z)({isMobile:nn,collapsed:hn},tn)),ii=(0,m.useContext)($t.X),li=ii.isChildrenLayout,rt=ye!==void 0?ye:li,Ue="".concat(Ee,"-layout"),qt=Aa(Ue),si=qt.wrapSSR,ot=qt.hashId,ci=le()(e.className,ot,"ant-design-pro",Ue,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"screen-".concat(en),en),"".concat(Ue,"-top-menu"),gn==="top"),"".concat(Ue,"-is-children"),rt),"".concat(Ue,"-fix-siderbar"),ni),"".concat(Ue,"-").concat(gn),gn)),ui=Qa(!!ri,hn,Se),er={position:"relative"};(rt||W&&W.minHeight)&&(er.minHeight=0),(0,m.useEffect)(function(){var Re;(Re=e.onPageChange)===null||Re===void 0||Re.call(e,e.location)},[Z.pathname,(t=Z.pathname)===null||t===void 0?void 0:t.search]);var di=(0,m.useState)(!1),nr=(0,F.Z)(di,2),tr=nr[0],mi=nr[1],pi=(0,m.useState)(0),rr=(0,F.Z)(pi,2),or=rr[0],fi=rr[1];P(nt,e.title||!1);var vi=(0,m.useContext)(te.L_),re=vi.token,ar=(0,m.useMemo)(function(){return A&&A.length>0?A==null?void 0:A.map(function(Re,Ge){return(0,s.jsx)("img",{src:Re.src,style:(0,d.Z)({position:"absolute"},Re)},Ge)}):null},[A]);return si((0,s.jsx)($t.X.Provider,{value:(0,d.Z)((0,d.Z)({},tn),{},{breadcrumb:ai,menuData:fn,isMobile:nn,collapsed:hn,hasPageContainer:or,setHasPageContainer:fi,isChildrenLayout:!0,title:nt.pageName,hasSiderMenu:!!Tn,hasHeader:!!tt,siderWidth:ui,hasFooter:!!Yt,hasFooterToolbar:tr,setHasFooterToolbar:mi,pageTitleInfo:nt,matchMenus:vn,matchMenuKeys:et,currentMenu:Kt}),children:e.pure?(0,s.jsx)(s.Fragment,{children:V}):(0,s.jsxs)("div",{className:ci,children:[ar||(o=re.layout)!==null&&o!==void 0&&o.bgLayout?(0,s.jsx)("div",{className:le()("".concat(Ue,"-bg-list"),ot),children:ar}):null,(0,s.jsxs)(Je.Z,{style:(0,d.Z)({minHeight:"100%",flexDirection:Tn?"row":void 0},ae),children:[(0,s.jsx)(Fe.ZP,{theme:{hashed:(0,te.nu)(),token:{controlHeightLG:((i=re.layout)===null||i===void 0||(i=i.sider)===null||i===void 0?void 0:i.menuHeight)||(re==null?void 0:re.controlHeightLG)},components:{Menu:X({colorItemBg:((a=re.layout)===null||a===void 0||(a=a.sider)===null||a===void 0?void 0:a.colorMenuBackground)||"transparent",colorSubItemBg:((c=re.layout)===null||c===void 0||(c=c.sider)===null||c===void 0?void 0:c.colorMenuBackground)||"transparent",radiusItem:re.borderRadius,colorItemBgSelected:((u=re.layout)===null||u===void 0||(u=u.sider)===null||u===void 0?void 0:u.colorBgMenuItemSelected)||(re==null?void 0:re.colorBgTextHover),colorItemBgHover:((p=re.layout)===null||p===void 0||(p=p.sider)===null||p===void 0?void 0:p.colorBgMenuItemHover)||(re==null?void 0:re.colorBgTextHover),colorItemBgActive:((v=re.layout)===null||v===void 0||(v=v.sider)===null||v===void 0?void 0:v.colorBgMenuItemActive)||(re==null?void 0:re.colorBgTextActive),colorItemBgSelectedHorizontal:((h=re.layout)===null||h===void 0||(h=h.sider)===null||h===void 0?void 0:h.colorBgMenuItemSelected)||(re==null?void 0:re.colorBgTextHover),colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:((y=re.layout)===null||y===void 0||(y=y.sider)===null||y===void 0?void 0:y.colorTextMenu)||(re==null?void 0:re.colorTextSecondary),colorItemTextHover:((R=re.layout)===null||R===void 0||(R=R.sider)===null||R===void 0?void 0:R.colorTextMenuItemHover)||"rgba(0, 0, 0, 0.85)",colorItemTextSelected:((j=re.layout)===null||j===void 0||(j=j.sider)===null||j===void 0?void 0:j.colorTextMenuSelected)||"rgba(0, 0, 0, 1)",popupBg:re==null?void 0:re.colorBgElevated,subMenuItemBg:re==null?void 0:re.colorBgElevated,darkSubMenuItemBg:"transparent",darkPopupBg:re==null?void 0:re.colorBgElevated})}},children:Tn}),(0,s.jsxs)("div",{style:er,className:"".concat(Ue,"-container ").concat(ot).trim(),children:[tt,(0,s.jsx)(Fr,(0,d.Z)((0,d.Z)({hasPageContainer:or,isChildrenLayout:rt},ti),{},{hasHeader:!!tt,prefixCls:Ue,style:W,children:fe?(0,s.jsx)(ua.S,{}):V})),Yt,tr&&(0,s.jsx)("div",{className:"".concat(Ue,"-has-footer"),style:{height:64,marginBlockStart:(N=re.layout)===null||N===void 0||(N=N.pageContainer)===null||N===void 0?void 0:N.paddingBlockPageContainerContent}})]})]})]})}))},qa=function(e){var n=e.colorPrimary,t=e.navTheme!==void 0?{dark:e.navTheme==="realDark"}:{};return(0,s.jsx)(Fe.ZP,{theme:n?{token:{colorPrimary:n}}:void 0,children:(0,s.jsx)(te._Y,(0,d.Z)((0,d.Z)({},t),{},{token:e.token,prefixCls:e.prefixCls,children:(0,s.jsx)(Ya,(0,d.Z)((0,d.Z)({logo:(0,s.jsx)(Wr,{})},Tt),{},{location:(0,ee.j)()?window.location:void 0},e))}))})}},90743:function(ve,ne){var f;function l(P){"@babel/helpers - typeof";return l=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(E){return typeof E}:function(E){return E&&typeof Symbol=="function"&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},l(P)}f={value:!0},ne.Bo=f=f=f=f=f=f=void 0;function K(P){for(var E=[],M=0;M<P.length;){var X=P[M];if(X==="*"||X==="+"||X==="?"){E.push({type:"MODIFIER",index:M,value:P[M++]});continue}if(X==="\\"){E.push({type:"ESCAPED_CHAR",index:M++,value:P[M++]});continue}if(X==="{"){E.push({type:"OPEN",index:M,value:P[M++]});continue}if(X==="}"){E.push({type:"CLOSE",index:M,value:P[M++]});continue}if(X===":"){for(var ce="",J=M+1;J<P.length;){var oe=P.charCodeAt(J);if(oe>=48&&oe<=57||oe>=65&&oe<=90||oe>=97&&oe<=122||oe===95){ce+=P[J++];continue}break}if(!ce)throw new TypeError("Missing parameter name at "+M);E.push({type:"NAME",index:M,value:ce}),M=J;continue}if(X==="("){var se=1,q="",J=M+1;if(P[J]==="?")throw new TypeError('Pattern cannot start with "?" at '+J);for(;J<P.length;){if(P[J]==="\\"){q+=P[J++]+P[J++];continue}if(P[J]===")"){if(se--,se===0){J++;break}}else if(P[J]==="("&&(se++,P[J+1]!=="?"))throw new TypeError("Capturing groups are not allowed at "+J);q+=P[J++]}if(se)throw new TypeError("Unbalanced pattern at "+M);if(!q)throw new TypeError("Missing pattern at "+M);E.push({type:"PATTERN",index:M,value:q}),M=J;continue}E.push({type:"CHAR",index:M,value:P[M++]})}return E.push({type:"END",index:M,value:""}),E}function k(P,E){E===void 0&&(E={});for(var M=K(P),X=E.prefixes,ce=X===void 0?"./":X,J="[^"+Q(E.delimiter||"/#?")+"]+?",oe=[],se=0,q=0,_="",C=function(Oe){if(q<M.length&&M[q].type===Oe)return M[q++].value},g=function(Oe){var Be=C(Oe);if(Be!==void 0)return Be;var $e=M[q],on=$e.type,Ke=$e.index;throw new TypeError("Unexpected "+on+" at "+Ke+", expected "+Oe)},O=function(){for(var Oe="",Be;Be=C("CHAR")||C("ESCAPED_CHAR");)Oe+=Be;return Oe};q<M.length;){var D=C("CHAR"),S=C("NAME"),H=C("PATTERN");if(S||H){var I=D||"";ce.indexOf(I)===-1&&(_+=I,I=""),_&&(oe.push(_),_=""),oe.push({name:S||se++,prefix:I,suffix:"",pattern:H||J,modifier:C("MODIFIER")||""});continue}var pe=D||C("ESCAPED_CHAR");if(pe){_+=pe;continue}_&&(oe.push(_),_="");var ge=C("OPEN");if(ge){var I=O(),Ie=C("NAME")||"",Ce=C("PATTERN")||"",Te=O();g("CLOSE"),oe.push({name:Ie||(Ce?se++:""),pattern:Ie&&!Ce?J:Ce,prefix:I,suffix:Te,modifier:C("MODIFIER")||""});continue}g("END")}return oe}f=k;function G(P,E){return F(k(P,E),E)}f=G;function F(P,E){E===void 0&&(E={});var M=m(E),X=E.encode,ce=X===void 0?function(q){return q}:X,J=E.validate,oe=J===void 0?!0:J,se=P.map(function(q){if(l(q)==="object")return new RegExp("^(?:"+q.pattern+")$",M)});return function(q){for(var _="",C=0;C<P.length;C++){var g=P[C];if(typeof g=="string"){_+=g;continue}var O=q?q[g.name]:void 0,D=g.modifier==="?"||g.modifier==="*",S=g.modifier==="*"||g.modifier==="+";if(Array.isArray(O)){if(!S)throw new TypeError('Expected "'+g.name+'" to not repeat, but got an array');if(O.length===0){if(D)continue;throw new TypeError('Expected "'+g.name+'" to not be empty')}for(var H=0;H<O.length;H++){var I=ce(O[H],g);if(oe&&!se[C].test(I))throw new TypeError('Expected all "'+g.name+'" to match "'+g.pattern+'", but got "'+I+'"');_+=g.prefix+I+g.suffix}continue}if(typeof O=="string"||typeof O=="number"){var I=ce(String(O),g);if(oe&&!se[C].test(I))throw new TypeError('Expected "'+g.name+'" to match "'+g.pattern+'", but got "'+I+'"');_+=g.prefix+I+g.suffix;continue}if(!D){var pe=S?"an array":"a string";throw new TypeError('Expected "'+g.name+'" to be '+pe)}}return _}}f=F;function d(P,E){var M=[],X=ee(P,M,E);return te(X,M,E)}f=d;function te(P,E,M){M===void 0&&(M={});var X=M.decode,ce=X===void 0?function(J){return J}:X;return function(J){var oe=P.exec(J);if(!oe)return!1;for(var se=oe[0],q=oe.index,_=Object.create(null),C=function(D){if(oe[D]===void 0)return"continue";var S=E[D-1];S.modifier==="*"||S.modifier==="+"?_[S.name]=oe[D].split(S.prefix+S.suffix).map(function(H){return ce(H,S)}):_[S.name]=ce(oe[D],S)},g=1;g<oe.length;g++)C(g);return{path:se,index:q,params:_}}}f=te;function Q(P){return P.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function m(P){return P&&P.sensitive?"":"i"}function $(P,E){if(!E)return P;var M=P.source.match(/\((?!\?)/g);if(M)for(var X=0;X<M.length;X++)E.push({name:X,prefix:"",suffix:"",modifier:"",pattern:""});return P}function Y(P,E,M){var X=P.map(function(ce){return ee(ce,E,M).source});return new RegExp("(?:"+X.join("|")+")",m(M))}function de(P,E,M){return me(k(P,M),E,M)}function me(P,E,M){M===void 0&&(M={});for(var X=M.strict,ce=X===void 0?!1:X,J=M.start,oe=J===void 0?!0:J,se=M.end,q=se===void 0?!0:se,_=M.encode,C=_===void 0?function(Me){return Me}:_,g="["+Q(M.endsWith||"")+"]|$",O="["+Q(M.delimiter||"/#?")+"]",D=oe?"^":"",S=0,H=P;S<H.length;S++){var I=H[S];if(typeof I=="string")D+=Q(C(I));else{var pe=Q(C(I.prefix)),ge=Q(C(I.suffix));if(I.pattern)if(E&&E.push(I),pe||ge)if(I.modifier==="+"||I.modifier==="*"){var Ie=I.modifier==="*"?"?":"";D+="(?:"+pe+"((?:"+I.pattern+")(?:"+ge+pe+"(?:"+I.pattern+"))*)"+ge+")"+Ie}else D+="(?:"+pe+"("+I.pattern+")"+ge+")"+I.modifier;else D+="("+I.pattern+")"+I.modifier;else D+="(?:"+pe+ge+")"+I.modifier}}if(q)ce||(D+=O+"?"),D+=M.endsWith?"(?="+g+")":"$";else{var Ce=P[P.length-1],Te=typeof Ce=="string"?O.indexOf(Ce[Ce.length-1])>-1:Ce===void 0;ce||(D+="(?:"+O+"(?="+g+"))?"),Te||(D+="(?="+O+"|"+g+")")}return new RegExp(D,m(M))}f=me;function ee(P,E,M){return P instanceof RegExp?$(P,E):Array.isArray(P)?Y(P,E,M):de(P,E,M)}ne.Bo=ee},73177:function(ve,ne,f){"use strict";f.d(ne,{X:function(){return d},b:function(){return F}});var l=f(67159),K=f(51812),k=f(1977),G=f(34155),F=function(){var Q;return typeof G=="undefined"?l.Z:((Q=G)===null||G===void 0||(G={ALLUSERSPROFILE:"C:\\ProgramData",APPDATA:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133655684414351508",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_18964_RFUUGCWTXLXIVTVZ",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"\u5C0F\u4E38\u728A\u5B50",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",CUDA_PATH:"D:\\CUDA",CUDA_PATH_V11_0:"D:\\CUDA",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_6444_1262719628:"1",EFC_6444_1592913036:"1",EFC_6444_2283032206:"1",EFC_6444_2775293581:"1",EFC_6444_3789132940:"1",ELINK_INSTALL_PATH:"C:\\Program Files (x86)\\Ecloud\\CloudComputer\\drivers\\CMSS",FPS_BROWSER_APP_PROFILE_STRING:"Internet Explorer",FPS_BROWSER_USER_PROFILE_STRING:"Default",GIT_ASKPASS:"d:\\Program\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",HOME:"C:\\Users\\\<USER>\u82CF",HOMEDRIVE:"C:",HOMEPATH:"\\Users\\\u82CF\u82CF",INIT_CWD:"D:\\project\\web_app_0527v2\\web",JAVA_HOME:"D:\\Program Files\\javajdk",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\\<USER>\u82CF\\AppData\\Local",LOGONSERVER:"\\\\\u5C0F\u4E38\u728A\u5B50",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"production",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NODE_PATH:"D:\\\u524D\u7AEF\\nodejs\\node_modules",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_cache:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\npm-cache",npm_config_globalconfig:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\\<USER>\u82CF\\.npm-init.js",npm_config_local_prefix:"D:\\project\\web_app_0527v2\\web",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_noproxy:"",npm_config_npm_version:"10.7.0",npm_config_prefix:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm",npm_config_userconfig:"C:\\Users\\\<USER>\u82CF\\.npmrc",npm_config_user_agent:"npm/10.7.0 node/v20.15.1 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build:bj",npm_lifecycle_script:"cross-env REACT_APP_ENV=bj UMI_ENV=bj max build",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_engines_node:">=12.0.0",npm_package_json:"D:\\project\\web_app_0527v2\\web\\package.json",npm_package_name:"ant-design-pro",npm_package_version:"6.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\\<USER>\u5FDA\u5AC3\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"16",NVCUDASAMPLES11_0_ROOT:"D:\\CUDA",NVCUDASAMPLES_ROOT:"D:\\CUDA",NVTOOLSEXT_PATH:"C:\\Program Files\\NVIDIA Corporation\\NvToolsExt\\",OneDrive:"C:\\Users\\\<USER>\u82CF\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"D:\\project\\web_app_0527v2\\web\\node_modules\\.bin;D:\\project\\web_app_0527v2\\node_modules\\.bin;D:\\project\\node_modules\\.bin;D:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;D:\\Program Files\\javajdk\\bin;C:\\Python312\\Scripts\\;C:\\Python312\\;D:\\CUDA\\bin;D:\\CUDA\\libnvvp;D:\\Anaconda;D:\\Anaconda\\Library\\mingw-w64\\bin;D:\\Anaconda\\Library\\usr\\bin;D:\\Anaconda\\Library\\bin;D:\\Anaconda\\Scripts;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;D:\\\u524D\u7AEF\\nodejs\\node_global;D;\\\u524D\u7AEF\\mongoDB\\server\\bin;C;\\Program Files\\NVIDIA Corporation\\Nsight Compute 2020.1.1\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;D:\\\u524D\u7AEF\\\u9879\u76EE\\2\u5FAE\u4FE1\u5C0F\u7A0B\u5E8F\\\u5FAE\u4FE1web\u5F00\u53D1\u8005\u5DE5\u5177\\dll;C:\\Program File;\\MySQL\\MySQL Server 8.0\\bin;C:\\Program Files (x86)\\PuTTY\\;D:\\Program Files\\TortoiseGit\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;D:\\Program\\Git\\cmd;D:\\Program Files\\javajdk\\bin;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\;C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Microsoft\\WindowsApps;D:\\\u524D\u7AEF\\webstorm\\WebStorm 2021.2.3\\bin;;D:\\\u524D\u7AEF\\nodejs\\node_global;C:\\Program Files\\JetBrains\\PyCharm Community Edition 2024.2.0.1\\bin;;C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm;D:\\Program\\Microsoft VS Code\\bin;C:\\MinGW\\bin;D:\\Program\\cursor\\resources\\app\\bin",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"AMD64 Family 25 Model 80 Stepping 0, AuthenticAMD",PROCESSOR_LEVEL:"25",PROCESSOR_REVISION:"5000",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\\<USER>\u82CF\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\Program Files\\JetBrains\\PyCharm Community Edition 2024.2.0.1\\bin;",REACT_APP_ENV:"bj",REACT_APP_WEB_SERVER_SOCKET_PORT:"http://*************:9044/",SESSIONNAME:"Console",SystemDrive:"C:",SystemRoot:"C:\\WINDOWS",TEMP:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.100.2",TMP:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Temp",UMI_APP_BACKEND_URL:"http://localhost:8001",UMI_DIR:"D:\\project\\web_app_0527v2\\web\\node_modules\\umi",UMI_ENV:"bj",UMI_PRESETS:"D:\\project\\web_app_0527v2\\web\\node_modules\\@umijs\\max\\dist\\preset.js",USERDOMAIN:"\u5C0F\u4E38\u728A\u5B50",USERDOMAIN_ROAMINGPROFILE:"\u5C0F\u4E38\u728A\u5B50",USERNAME:"\u82CF\u82CF",USERPROFILE:"C:\\Users\\\<USER>\u82CF",VSCODE_GIT_ASKPASS_EXTRA_ARGS:"",VSCODE_GIT_ASKPASS_MAIN:"d:\\Program\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"D:\\Program\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-c52bfb7121-sock",VSCODE_INJECTION:"1",WebStorm:"D:\\\u524D\u7AEF\\webstorm\\WebStorm 2021.2.3\\bin;",windir:"C:\\WINDOWS",ZXCLOUD_INSTALL_PATH:"C:\\Program Files (x86)\\uSmartView",ZXVE_CLIENT_AGENT_UUID:"3577B2A6-F8E2-4987-9B98-4748E6A96F38",ZXVE_IRAI:"C:\\Program Files (x86)\\uSmartView\\client\\uSmartView.exe",ZXVE_NEW_UDS:"0x00000001"})===null||G===void 0?void 0:G.ANTD_VERSION)||l.Z},d=function(Q,m){var $=(0,k.n)(F(),"4.23.0")>-1?{open:Q,onOpenChange:m}:{visible:Q,onVisibleChange:m};return(0,K.Y)($)}},78164:function(ve,ne,f){"use strict";f.d(ne,{S:function(){return $}});var l=f(15671),K=f(43144),k=f(97326),G=f(60136),F=f(29388),d=f(4942),te=f(29905),Q=f(67294),m=f(85893),$=function(Y){(0,G.Z)(me,Y);var de=(0,F.Z)(me);function me(){var ee;(0,l.Z)(this,me);for(var P=arguments.length,E=new Array(P),M=0;M<P;M++)E[M]=arguments[M];return ee=de.call.apply(de,[this].concat(E)),(0,d.Z)((0,k.Z)(ee),"state",{hasError:!1,errorInfo:""}),ee}return(0,K.Z)(me,[{key:"componentDidCatch",value:function(P,E){console.log(P,E)}},{key:"render",value:function(){return this.state.hasError?(0,m.jsx)(te.ZP,{status:"error",title:"Something went wrong.",extra:this.state.errorInfo}):this.props.children}}],[{key:"getDerivedStateFromError",value:function(P){return{hasError:!0,errorInfo:P.message}}}]),me}(Q.Component)},10178:function(ve,ne,f){"use strict";f.d(ne,{D:function(){return F}});var l=f(74165),K=f(15861),k=f(67294),G=f(48171);function F(d,te){var Q=(0,G.J)(d),m=(0,k.useRef)(),$=(0,k.useCallback)(function(){m.current&&(clearTimeout(m.current),m.current=null)},[]),Y=(0,k.useCallback)((0,K.Z)((0,l.Z)().mark(function de(){var me,ee,P,E=arguments;return(0,l.Z)().wrap(function(X){for(;;)switch(X.prev=X.next){case 0:for(me=E.length,ee=new Array(me),P=0;P<me;P++)ee[P]=E[P];if(!(te===0||te===void 0)){X.next=3;break}return X.abrupt("return",Q.apply(void 0,ee));case 3:return $(),X.abrupt("return",new Promise(function(ce){m.current=setTimeout((0,K.Z)((0,l.Z)().mark(function J(){return(0,l.Z)().wrap(function(se){for(;;)switch(se.prev=se.next){case 0:return se.t0=ce,se.next=3,Q.apply(void 0,ee);case 3:return se.t1=se.sent,(0,se.t0)(se.t1),se.abrupt("return");case 6:case"end":return se.stop()}},J)})),te)}));case 5:case"end":return X.stop()}},de)})),[Q,$,te]);return(0,k.useEffect)(function(){return $},[$]),{run:Y,cancel:$}}},48171:function(ve,ne,f){"use strict";f.d(ne,{J:function(){return k}});var l=f(74902),K=f(67294),k=function(F){var d=(0,K.useRef)(null);return d.current=F,(0,K.useCallback)(function(){for(var te,Q=arguments.length,m=new Array(Q),$=0;$<Q;$++)m[$]=arguments[$];return(te=d.current)===null||te===void 0?void 0:te.call.apply(te,[d].concat((0,l.Z)(m)))},[])}},51812:function(ve,ne,f){"use strict";f.d(ne,{Y:function(){return l}});var l=function(k){var G={};if(Object.keys(k||{}).forEach(function(F){k[F]!==void 0&&(G[F]=k[F])}),!(Object.keys(G).length<1))return G}},57838:function(ve,ne,f){"use strict";f.d(ne,{Z:function(){return K}});var l=f(67294);function K(){const[,k]=l.useReducer(G=>G+1,0);return k}},74443:function(ve,ne,f){"use strict";f.d(ne,{c4:function(){return G},m9:function(){return te}});var l=f(67294),K=f(29691),k=f(85849);const G=["xxl","xl","lg","md","sm","xs"],F=m=>({xs:`(max-width: ${m.screenXSMax}px)`,sm:`(min-width: ${m.screenSM}px)`,md:`(min-width: ${m.screenMD}px)`,lg:`(min-width: ${m.screenLG}px)`,xl:`(min-width: ${m.screenXL}px)`,xxl:`(min-width: ${m.screenXXL}px)`}),d=m=>{const $=m,Y=[].concat(G).reverse();return Y.forEach((de,me)=>{const ee=de.toUpperCase(),P=`screen${ee}Min`,E=`screen${ee}`;if(!($[P]<=$[E]))throw new Error(`${P}<=${E} fails : !(${$[P]}<=${$[E]})`);if(me<Y.length-1){const M=`screen${ee}Max`;if(!($[E]<=$[M]))throw new Error(`${E}<=${M} fails : !(${$[E]}<=${$[M]})`);const ce=`screen${Y[me+1].toUpperCase()}Min`;if(!($[M]<=$[ce]))throw new Error(`${M}<=${ce} fails : !(${$[M]}<=${$[ce]})`)}}),m},te=(m,$)=>{if($){for(const Y of G)if(m[Y]&&($==null?void 0:$[Y])!==void 0)return $[Y]}},Q=()=>{const[,m]=(0,K.ZP)(),$=F(d(m));return l.useMemo(()=>{const Y=new Map;let de=-1,me={};return{responsiveMap:$,matchHandlers:{},dispatch(ee){return me=ee,Y.forEach(P=>P(me)),Y.size>=1},subscribe(ee){return Y.size||this.register(),de+=1,Y.set(de,ee),ee(me),de},unsubscribe(ee){Y.delete(ee),Y.size||this.unregister()},register(){Object.entries($).forEach(([ee,P])=>{const E=({matches:X})=>{this.dispatch(Object.assign(Object.assign({},me),{[ee]:X}))},M=window.matchMedia(P);(0,k.x)(M,E),this.matchHandlers[P]={mql:M,listener:E},E(M)})},unregister(){Object.values($).forEach(ee=>{const P=this.matchHandlers[ee];(0,k.h)(P==null?void 0:P.mql,P==null?void 0:P.listener)}),Y.clear()}}},[m])};ne.ZP=Q},25378:function(ve,ne,f){"use strict";var l=f(67294),K=f(8410),k=f(57838),G=f(74443);function F(d=!0,te={}){const Q=(0,l.useRef)(te),m=(0,k.Z)(),$=(0,G.ZP)();return(0,K.Z)(()=>{const Y=$.subscribe(de=>{Q.current=de,d&&m()});return()=>$.unsubscribe(Y)},[]),Q.current}ne.Z=F},26058:function(ve,ne,f){"use strict";f.d(ne,{Z:function(){return se}});var l=f(74902),K=f(67294),k=f(93967),G=f.n(k),F=f(98423),d=f(53124),te=f(82401),Q=f(50344),m=f(70985);function $(q,_,C){return typeof C=="boolean"?C:q.length?!0:(0,Q.Z)(_).some(O=>O.type===m.Z)}var Y=f(24793),de=function(q,_){var C={};for(var g in q)Object.prototype.hasOwnProperty.call(q,g)&&_.indexOf(g)<0&&(C[g]=q[g]);if(q!=null&&typeof Object.getOwnPropertySymbols=="function")for(var O=0,g=Object.getOwnPropertySymbols(q);O<g.length;O++)_.indexOf(g[O])<0&&Object.prototype.propertyIsEnumerable.call(q,g[O])&&(C[g[O]]=q[g[O]]);return C};function me({suffixCls:q,tagName:_,displayName:C}){return g=>K.forwardRef((D,S)=>K.createElement(g,Object.assign({ref:S,suffixCls:q,tagName:_},D)))}const ee=K.forwardRef((q,_)=>{const{prefixCls:C,suffixCls:g,className:O,tagName:D}=q,S=de(q,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:H}=K.useContext(d.E_),I=H("layout",C),[pe,ge,Ie]=(0,Y.ZP)(I),Ce=g?`${I}-${g}`:I;return pe(K.createElement(D,Object.assign({className:G()(C||Ce,O,ge,Ie),ref:_},S)))}),P=K.forwardRef((q,_)=>{const{direction:C}=K.useContext(d.E_),[g,O]=K.useState([]),{prefixCls:D,className:S,rootClassName:H,children:I,hasSider:pe,tagName:ge,style:Ie}=q,Ce=de(q,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),Te=(0,F.Z)(Ce,["suffixCls"]),{getPrefixCls:Me,className:Oe,style:Be}=(0,d.dj)("layout"),$e=Me("layout",D),on=$(g,I,pe),[Ke,On,Nn]=(0,Y.ZP)($e),Zn=G()($e,{[`${$e}-has-sider`]:on,[`${$e}-rtl`]:C==="rtl"},Oe,S,H,On,Nn),wn=K.useMemo(()=>({siderHook:{addSider:an=>{O(ln=>[].concat((0,l.Z)(ln),[an]))},removeSider:an=>{O(ln=>ln.filter(jn=>jn!==an))}}}),[]);return Ke(K.createElement(te.V.Provider,{value:wn},K.createElement(ge,Object.assign({ref:_,className:Zn,style:Object.assign(Object.assign({},Be),Ie)},Te),I)))}),E=me({tagName:"div",displayName:"Layout"})(P),M=me({suffixCls:"header",tagName:"header",displayName:"Header"})(ee),X=me({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(ee),ce=me({suffixCls:"content",tagName:"main",displayName:"Content"})(ee);var J=E;const oe=J;oe.Header=M,oe.Footer=X,oe.Content=ce,oe.Sider=m.Z,oe._InternalSiderContext=m.D;var se=oe},16305:function(ve,ne){"use strict";var f;f={value:!0},f=void 0,f=de,f=me,ne.EQ=E,f=M,f=oe;const l="/",K=_=>_,k=/^[$_\p{ID_Start}]$/u,G=/^[$\u200c\u200d\p{ID_Continue}]$/u,F="https://git.new/pathToRegexpError",d={"{":"{","}":"}","(":"(",")":")","[":"[","]":"]","+":"+","?":"?","!":"!"};function te(_){return _.replace(/[{}()\[\]+?!:*]/g,"\\$&")}function Q(_){return _.replace(/[.+*?^${}()[\]|/\\]/g,"\\$&")}function*m(_){const C=[..._];let g=0;function O(){let D="";if(k.test(C[++g]))for(D+=C[g];G.test(C[++g]);)D+=C[g];else if(C[g]==='"'){let S=g;for(;g<C.length;){if(C[++g]==='"'){g++,S=0;break}C[g]==="\\"?D+=C[++g]:D+=C[g]}if(S)throw new TypeError(`Unterminated quote at ${S}: ${F}`)}if(!D)throw new TypeError(`Missing parameter name at ${g}: ${F}`);return D}for(;g<C.length;){const D=C[g],S=d[D];if(S)yield{type:S,index:g++,value:D};else if(D==="\\")yield{type:"ESCAPED",index:g++,value:C[g++]};else if(D===":"){const H=O();yield{type:"PARAM",index:g,value:H}}else if(D==="*"){const H=O();yield{type:"WILDCARD",index:g,value:H}}else yield{type:"CHAR",index:g,value:C[g++]}}return{type:"END",index:g,value:""}}class ${constructor(C){this.tokens=C}peek(){if(!this._peek){const C=this.tokens.next();this._peek=C.value}return this._peek}tryConsume(C){const g=this.peek();if(g.type===C)return this._peek=void 0,g.value}consume(C){const g=this.tryConsume(C);if(g!==void 0)return g;const{type:O,index:D}=this.peek();throw new TypeError(`Unexpected ${O} at ${D}, expected ${C}: ${F}`)}text(){let C="",g;for(;g=this.tryConsume("CHAR")||this.tryConsume("ESCAPED");)C+=g;return C}}class Y{constructor(C){this.tokens=C}}f=Y;function de(_,C={}){const{encodePath:g=K}=C,O=new $(m(_));function D(H){const I=[];for(;;){const pe=O.text();pe&&I.push({type:"text",value:g(pe)});const ge=O.tryConsume("PARAM");if(ge){I.push({type:"param",name:ge});continue}const Ie=O.tryConsume("WILDCARD");if(Ie){I.push({type:"wildcard",name:Ie});continue}if(O.tryConsume("{")){I.push({type:"group",tokens:D("}")});continue}return O.consume(H),I}}const S=D("END");return new Y(S)}function me(_,C={}){const{encode:g=encodeURIComponent,delimiter:O=l}=C,D=_ instanceof Y?_:de(_,C),S=ee(D.tokens,O,g);return function(I={}){const[pe,...ge]=S(I);if(ge.length)throw new TypeError(`Missing parameters: ${ge.join(", ")}`);return pe}}function ee(_,C,g){const O=_.map(D=>P(D,C,g));return D=>{const S=[""];for(const H of O){const[I,...pe]=H(D);S[0]+=I,S.push(...pe)}return S}}function P(_,C,g){if(_.type==="text")return()=>[_.value];if(_.type==="group"){const D=ee(_.tokens,C,g);return S=>{const[H,...I]=D(S);return I.length?[""]:[H]}}const O=g||K;return _.type==="wildcard"&&g!==!1?D=>{const S=D[_.name];if(S==null)return["",_.name];if(!Array.isArray(S)||S.length===0)throw new TypeError(`Expected "${_.name}" to be a non-empty array`);return[S.map((H,I)=>{if(typeof H!="string")throw new TypeError(`Expected "${_.name}/${I}" to be a string`);return O(H)}).join(C)]}:D=>{const S=D[_.name];if(S==null)return["",_.name];if(typeof S!="string")throw new TypeError(`Expected "${_.name}" to be a string`);return[O(S)]}}function E(_,C={}){const{decode:g=decodeURIComponent,delimiter:O=l}=C,{regexp:D,keys:S}=M(_,C),H=S.map(I=>g===!1?K:I.type==="param"?g:pe=>pe.split(O).map(g));return function(pe){const ge=D.exec(pe);if(!ge)return!1;const Ie=ge[0],Ce=Object.create(null);for(let Te=1;Te<ge.length;Te++){if(ge[Te]===void 0)continue;const Me=S[Te-1],Oe=H[Te-1];Ce[Me.name]=Oe(ge[Te])}return{path:Ie,params:Ce}}}function M(_,C={}){const{delimiter:g=l,end:O=!0,sensitive:D=!1,trailing:S=!0}=C,H=[],I=[],pe=D?"":"i",Ie=(Array.isArray(_)?_:[_]).map(Me=>Me instanceof Y?Me:de(Me,C));for(const{tokens:Me}of Ie)for(const Oe of X(Me,0,[])){const Be=ce(Oe,g,H);I.push(Be)}let Ce=`^(?:${I.join("|")})`;return S&&(Ce+=`(?:${Q(g)}$)?`),Ce+=O?"$":`(?=${Q(g)}|$)`,{regexp:new RegExp(Ce,pe),keys:H}}function*X(_,C,g){if(C===_.length)return yield g;const O=_[C];if(O.type==="group"){const D=g.slice();for(const S of X(O.tokens,0,D))yield*it(X(_,C+1,S))}else g.push(O);yield*it(X(_,C+1,g))}function ce(_,C,g){let O="",D="",S=!0;for(let H=0;H<_.length;H++){const I=_[H];if(I.type==="text"){O+=Q(I.value),D+=I.value,S||(S=I.value.includes(C));continue}if(I.type==="param"||I.type==="wildcard"){if(!S&&!D)throw new TypeError(`Missing text after "${I.name}": ${F}`);I.type==="param"?O+=`(${J(C,S?"":D)}+)`:O+="([\\s\\S]+)",g.push(I),D="",S=!1;continue}}return O}function J(_,C){return C.length<2?_.length<2?`[^${Q(_+C)}]`:`(?:(?!${Q(_)})[^${Q(C)}])`:_.length<2?`(?:(?!${Q(C)})[^${Q(_)}])`:`(?:(?!${Q(C)}|${Q(_)})[\\s\\S])`}function oe(_){return _.tokens.map(function C(g,O,D){if(g.type==="text")return te(g.value);if(g.type==="group")return`{${g.tokens.map(C).join("")}}`;const H=se(g.name)&&q(D[O+1])?g.name:JSON.stringify(g.name);if(g.type==="param")return`:${H}`;if(g.type==="wildcard")return`*${H}`;throw new TypeError(`Unexpected token: ${g}`)}).join("")}function se(_){const[C,...g]=_;return k.test(C)?g.every(O=>G.test(O)):!1}function q(_){return(_==null?void 0:_.type)!=="text"?!0:!G.test(_.value[0])}},64599:function(ve,ne,f){var l=f(96263);function K(k,G){var F=typeof Symbol!="undefined"&&k[Symbol.iterator]||k["@@iterator"];if(!F){if(Array.isArray(k)||(F=l(k))||G&&k&&typeof k.length=="number"){F&&(k=F);var d=0,te=function(){};return{s:te,n:function(){return d>=k.length?{done:!0}:{done:!1,value:k[d++]}},e:function(de){throw de},f:te}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var Q=!0,m=!1,$;return{s:function(){F=F.call(k)},n:function(){var de=F.next();return Q=de.done,de},e:function(de){m=!0,$=de},f:function(){try{!Q&&F.return!=null&&F.return()}finally{if(m)throw $}}}}ve.exports=K,ve.exports.__esModule=!0,ve.exports.default=ve.exports}}]);
}());