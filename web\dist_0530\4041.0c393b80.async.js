"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4041],{88258:function(Zn,pt,i){var C=i(67294),ue=i(53124),P=i(32983);const R=A=>{const{componentName:qe}=A,{getPrefixCls:Et}=(0,C.useContext)(ue.E_),ke=Et("empty");switch(qe){case"Table":case"List":return C.createElement(P.Z,{image:P.Z.PRESENTED_IMAGE_SIMPLE});case"Select":case"TreeSelect":case"Cascader":case"Transfer":case"Mentions":return C.createElement(P.Z,{image:P.Z.PRESENTED_IMAGE_SIMPLE,className:`${ke}-small`});case"Table.filter":return null;default:return C.createElement(P.Z,null)}};pt.Z=R},32983:function(Zn,pt,i){i.d(pt,{Z:function(){return hn}});var C=i(67294),ue=i(93967),P=i.n(ue),R=i(10110),A=i(15063),qe=i(29691),ke=()=>{const[,J]=(0,qe.ZP)(),[ge]=(0,R.Z)("Empty"),p=new A.t(J.colorBgBase).toHsl().l<.5?{opacity:.65}:{};return C.createElement("svg",{style:p,width:"184",height:"152",viewBox:"0 0 184 152",xmlns:"http://www.w3.org/2000/svg"},C.createElement("title",null,(ge==null?void 0:ge.description)||"Empty"),C.createElement("g",{fill:"none",fillRule:"evenodd"},C.createElement("g",{transform:"translate(24 31.67)"},C.createElement("ellipse",{fillOpacity:".8",fill:"#F5F5F7",cx:"67.797",cy:"106.89",rx:"67.797",ry:"12.668"}),C.createElement("path",{d:"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z",fill:"#AEB8C2"}),C.createElement("path",{d:"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z",fill:"url(#linearGradient-1)",transform:"translate(13.56)"}),C.createElement("path",{d:"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z",fill:"#F5F5F7"}),C.createElement("path",{d:"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z",fill:"#DCE0E6"})),C.createElement("path",{d:"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z",fill:"#DCE0E6"}),C.createElement("g",{transform:"translate(149.65 15.383)",fill:"#FFF"},C.createElement("ellipse",{cx:"20.654",cy:"3.167",rx:"2.849",ry:"2.815"}),C.createElement("path",{d:"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"}))))},r=()=>{const[,J]=(0,qe.ZP)(),[ge]=(0,R.Z)("Empty"),{colorFill:Be,colorFillTertiary:p,colorFillQuaternary:Le,colorBgContainer:Ne}=J,{borderColor:Nt,shadowColor:We,contentColor:At}=(0,C.useMemo)(()=>({borderColor:new A.t(Be).onBackground(Ne).toHexString(),shadowColor:new A.t(p).onBackground(Ne).toHexString(),contentColor:new A.t(Le).onBackground(Ne).toHexString()}),[Be,p,Le,Ne]);return C.createElement("svg",{width:"64",height:"41",viewBox:"0 0 64 41",xmlns:"http://www.w3.org/2000/svg"},C.createElement("title",null,(ge==null?void 0:ge.description)||"Empty"),C.createElement("g",{transform:"translate(0 1)",fill:"none",fillRule:"evenodd"},C.createElement("ellipse",{fill:We,cx:"32",cy:"33",rx:"32",ry:"7"}),C.createElement("g",{fillRule:"nonzero",stroke:Nt},C.createElement("path",{d:"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"}),C.createElement("path",{d:"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z",fill:At}))))},we=i(83559),s=i(83262);const vn=J=>{const{componentCls:ge,margin:Be,marginXS:p,marginXL:Le,fontSize:Ne,lineHeight:Nt}=J;return{[ge]:{marginInline:p,fontSize:Ne,lineHeight:Nt,textAlign:"center",[`${ge}-image`]:{height:J.emptyImgHeight,marginBottom:p,opacity:J.opacityImage,img:{height:"100%"},svg:{maxWidth:"100%",height:"100%",margin:"auto"}},[`${ge}-description`]:{color:J.colorTextDescription},[`${ge}-footer`]:{marginTop:Be},"&-normal":{marginBlock:Le,color:J.colorTextDescription,[`${ge}-description`]:{color:J.colorTextDescription},[`${ge}-image`]:{height:J.emptyImgHeightMD}},"&-small":{marginBlock:p,color:J.colorTextDescription,[`${ge}-image`]:{height:J.emptyImgHeightSM}}}}};var mn=(0,we.I$)("Empty",J=>{const{componentCls:ge,controlHeightLG:Be,calc:p}=J,Le=(0,s.IX)(J,{emptyImgCls:`${ge}-img`,emptyImgHeight:p(Be).mul(2.5).equal(),emptyImgHeightMD:Be,emptyImgHeightSM:p(Be).mul(.875).equal()});return[vn(Le)]}),on=i(53124),gn=function(J,ge){var Be={};for(var p in J)Object.prototype.hasOwnProperty.call(J,p)&&ge.indexOf(p)<0&&(Be[p]=J[p]);if(J!=null&&typeof Object.getOwnPropertySymbols=="function")for(var Le=0,p=Object.getOwnPropertySymbols(J);Le<p.length;Le++)ge.indexOf(p[Le])<0&&Object.prototype.propertyIsEnumerable.call(J,p[Le])&&(Be[p[Le]]=J[p[Le]]);return Be};const _t=C.createElement(ke,null),pn=C.createElement(r,null),Ut=J=>{const{className:ge,rootClassName:Be,prefixCls:p,image:Le=_t,description:Ne,children:Nt,imageStyle:We,style:At,classNames:De,styles:ce}=J,e=gn(J,["className","rootClassName","prefixCls","image","description","children","imageStyle","style","classNames","styles"]),{getPrefixCls:g,direction:f,className:pe,style:Ze,classNames:Ae,styles:et}=(0,on.dj)("empty"),Ie=g("empty",p),[zt,Sn,wn]=mn(Ie),[Dt]=(0,R.Z)("Empty"),Vt=typeof Ne!="undefined"?Ne:Dt==null?void 0:Dt.description,an=typeof Vt=="string"?Vt:"empty";let Ht=null;return typeof Le=="string"?Ht=C.createElement("img",{alt:an,src:Le}):Ht=Le,zt(C.createElement("div",Object.assign({className:P()(Sn,wn,Ie,pe,{[`${Ie}-normal`]:Le===pn,[`${Ie}-rtl`]:f==="rtl"},ge,Be,Ae.root,De==null?void 0:De.root),style:Object.assign(Object.assign(Object.assign(Object.assign({},et.root),Ze),ce==null?void 0:ce.root),At)},e),C.createElement("div",{className:P()(`${Ie}-image`,Ae.image,De==null?void 0:De.image),style:Object.assign(Object.assign(Object.assign({},We),et.image),ce==null?void 0:ce.image)},Ht),Vt&&C.createElement("div",{className:P()(`${Ie}-description`,Ae.description,De==null?void 0:De.description),style:Object.assign(Object.assign({},et.description),ce==null?void 0:ce.description)},Vt),Nt&&C.createElement("div",{className:P()(`${Ie}-footer`,Ae.footer,De==null?void 0:De.footer),style:Object.assign(Object.assign({},et.footer),ce==null?void 0:ce.footer)},Nt)))};Ut.PRESENTED_IMAGE_DEFAULT=_t,Ut.PRESENTED_IMAGE_SIMPLE=pn;var hn=Ut},34041:function(Zn,pt,i){var C=i(67294),ue=i(93967),P=i.n(ue),R=i(50089),A=i(98423),qe=i(87263),Et=i(33603),ke=i(8745),Kt=i(9708),r=i(53124),we=i(88258),s=i(98866),vn=i(35792),mn=i(98675),on=i(65223),gn=i(27833),_t=i(4173),pn=i(29691),Ut=i(30307),hn=i(15030),J=i(43277),ge=i(78642),Be=function(We,At){var De={};for(var ce in We)Object.prototype.hasOwnProperty.call(We,ce)&&At.indexOf(ce)<0&&(De[ce]=We[ce]);if(We!=null&&typeof Object.getOwnPropertySymbols=="function")for(var e=0,ce=Object.getOwnPropertySymbols(We);e<ce.length;e++)At.indexOf(ce[e])<0&&Object.prototype.propertyIsEnumerable.call(We,ce[e])&&(De[ce[e]]=We[ce[e]]);return De};const p="SECRET_COMBOBOX_MODE_DO_NOT_USE",Le=(We,At)=>{var De,ce,e,g,f;const{prefixCls:pe,bordered:Ze,className:Ae,rootClassName:et,getPopupContainer:Ie,popupClassName:zt,dropdownClassName:Sn,listHeight:wn=256,placement:Dt,listItemHeight:Vt,size:an,disabled:Ht,notFoundContent:$n,status:Tn,builtinPlacements:zn,dropdownMatchSelectWidth:Vn,popupMatchSelectWidth:a,direction:D,style:I,allowClear:O,variant:z,dropdownStyle:F,transitionName:N,tagRender:Z,maxCount:ne,prefix:U,dropdownRender:te,popupRender:$,onDropdownVisibleChange:Y,onOpenChange:L,styles:T,classNames:M}=We,ie=Be(We,["prefixCls","bordered","className","rootClassName","getPopupContainer","popupClassName","dropdownClassName","listHeight","placement","listItemHeight","size","disabled","notFoundContent","status","builtinPlacements","dropdownMatchSelectWidth","popupMatchSelectWidth","direction","style","allowClear","variant","dropdownStyle","transitionName","tagRender","maxCount","prefix","dropdownRender","popupRender","onDropdownVisibleChange","onOpenChange","styles","classNames"]),{getPopupContainer:de,getPrefixCls:j,renderEmpty:fe,direction:Ce,virtual:it,popupMatchSelectWidth:ut,popupOverflow:ye}=C.useContext(r.E_),{showSearch:ct,style:wt,styles:Zt,className:ht,classNames:It}=(0,r.dj)("select"),[,Rt]=(0,pn.ZP)(),tn=Vt!=null?Vt:Rt==null?void 0:Rt.controlHeight,Ge=j("select",pe),st=j(),St=D!=null?D:Ce,{compactSize:vt,compactItemClassnames:Xt}=(0,_t.ri)(Ge,St),[$t,Ot]=(0,gn.Z)("select",z,Ze),Gt=(0,vn.Z)(Ge),[Ye,Tt,ln]=(0,hn.Z)(Ge,Gt),Yt=C.useMemo(()=>{const{mode:Q}=We;if(Q!=="combobox")return Q===p?"combobox":Q},[We.mode]),bn=Yt==="multiple"||Yt==="tags",Qt=(0,ge.Z)(We.suffixIcon,We.showArrow),Fe=(De=a!=null?a:Vn)!==null&&De!==void 0?De:ut,xt=((ce=T==null?void 0:T.popup)===null||ce===void 0?void 0:ce.root)||((e=Zt.popup)===null||e===void 0?void 0:e.root)||F,Mt=$||te,at=L||Y,{status:tt,hasFeedback:nn,isFormItemInput:In,feedbackIcon:Cn}=C.useContext(on.aM),Rn=(0,Kt.F)(tt,Tn);let un;$n!==void 0?un=$n:Yt==="combobox"?un=null:un=(fe==null?void 0:fe("Select"))||C.createElement(we.Z,{componentName:"Select"});const{suffixIcon:Bn,itemIcon:n,removeIcon:t,clearIcon:c}=(0,J.Z)(Object.assign(Object.assign({},ie),{multiple:bn,hasFeedback:nn,feedbackIcon:Cn,showSuffixIcon:Qt,prefixCls:Ge,componentName:"Select"})),o=O===!0?{clearIcon:c}:O,u=(0,A.Z)(ie,["suffixIcon","itemIcon"]),m=P()(((g=M==null?void 0:M.popup)===null||g===void 0?void 0:g.root)||((f=It==null?void 0:It.popup)===null||f===void 0?void 0:f.root)||zt||Sn,{[`${Ge}-dropdown-${St}`]:St==="rtl"},et,It.root,M==null?void 0:M.root,ln,Gt,Tt),v=(0,mn.Z)(Q=>{var S;return(S=an!=null?an:vt)!==null&&S!==void 0?S:Q}),h=C.useContext(s.Z),y=Ht!=null?Ht:h,E=P()({[`${Ge}-lg`]:v==="large",[`${Ge}-sm`]:v==="small",[`${Ge}-rtl`]:St==="rtl",[`${Ge}-${$t}`]:Ot,[`${Ge}-in-form-item`]:In},(0,Kt.Z)(Ge,Rn,nn),Xt,ht,Ae,It.root,M==null?void 0:M.root,et,ln,Gt,Tt),x=C.useMemo(()=>Dt!==void 0?Dt:St==="rtl"?"bottomRight":"bottomLeft",[Dt,St]),[B]=(0,qe.Cn)("SelectLike",xt==null?void 0:xt.zIndex);return Ye(C.createElement(R.ZP,Object.assign({ref:At,virtual:it,showSearch:ct},u,{style:Object.assign(Object.assign(Object.assign(Object.assign({},Zt.root),T==null?void 0:T.root),wt),I),dropdownMatchSelectWidth:Fe,transitionName:(0,Et.m)(st,"slide-up",N),builtinPlacements:(0,Ut.Z)(zn,ye),listHeight:wn,listItemHeight:tn,mode:Yt,prefixCls:Ge,placement:x,direction:St,prefix:U,suffixIcon:Bn,menuItemSelectedIcon:n,removeIcon:t,allowClear:o,notFoundContent:un,className:E,getPopupContainer:Ie||de,dropdownClassName:m,disabled:y,dropdownStyle:Object.assign(Object.assign({},xt),{zIndex:B}),maxCount:bn?ne:void 0,tagRender:bn?Z:void 0,dropdownRender:Mt,onDropdownVisibleChange:at})))},Ne=C.forwardRef(Le),Nt=(0,ke.Z)(Ne,"dropdownAlign");Ne.SECRET_COMBOBOX_MODE_DO_NOT_USE=p,Ne.Option=R.Wx,Ne.OptGroup=R.Xo,Ne._InternalPanelDoNotUseOrYouWillBeFired=Nt,pt.Z=Ne},30307:function(Zn,pt){const i=ue=>{const R={overflow:{adjustX:!0,adjustY:!0,shiftY:!0},htmlRegion:ue==="scroll"?"scroll":"visible",dynamicInset:!0};return{bottomLeft:Object.assign(Object.assign({},R),{points:["tl","bl"],offset:[0,4]}),bottomRight:Object.assign(Object.assign({},R),{points:["tr","br"],offset:[0,4]}),topLeft:Object.assign(Object.assign({},R),{points:["bl","tl"],offset:[0,-4]}),topRight:Object.assign(Object.assign({},R),{points:["br","tr"],offset:[0,-4]})}};function C(ue,P){return ue||i(P)}pt.Z=C},15030:function(Zn,pt,i){i.d(pt,{Z:function(){return ce}});var C=i(14747),ue=i(80110),P=i(83559),R=i(83262),A=i(67771),qe=i(33297);const Et=e=>{const{optionHeight:g,optionFontSize:f,optionLineHeight:pe,optionPadding:Ze}=e;return{position:"relative",display:"block",minHeight:g,padding:Ze,color:e.colorText,fontWeight:"normal",fontSize:f,lineHeight:pe,boxSizing:"border-box"}};var Kt=e=>{const{antCls:g,componentCls:f}=e,pe=`${f}-item`,Ze=`&${g}-slide-up-enter${g}-slide-up-enter-active`,Ae=`&${g}-slide-up-appear${g}-slide-up-appear-active`,et=`&${g}-slide-up-leave${g}-slide-up-leave-active`,Ie=`${f}-dropdown-placement-`,zt=`${pe}-option-selected`;return[{[`${f}-dropdown`]:Object.assign(Object.assign({},(0,C.Wf)(e)),{position:"absolute",top:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",padding:e.paddingXXS,overflow:"hidden",fontSize:e.fontSize,fontVariant:"initial",backgroundColor:e.colorBgElevated,borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,[`
          ${Ze}${Ie}bottomLeft,
          ${Ae}${Ie}bottomLeft
        `]:{animationName:A.fJ},[`
          ${Ze}${Ie}topLeft,
          ${Ae}${Ie}topLeft,
          ${Ze}${Ie}topRight,
          ${Ae}${Ie}topRight
        `]:{animationName:A.Qt},[`${et}${Ie}bottomLeft`]:{animationName:A.Uw},[`
          ${et}${Ie}topLeft,
          ${et}${Ie}topRight
        `]:{animationName:A.ly},"&-hidden":{display:"none"},[pe]:Object.assign(Object.assign({},Et(e)),{cursor:"pointer",transition:`background ${e.motionDurationSlow} ease`,borderRadius:e.borderRadiusSM,"&-group":{color:e.colorTextDescription,fontSize:e.fontSizeSM,cursor:"default"},"&-option":{display:"flex","&-content":Object.assign({flex:"auto"},C.vS),"&-state":{flex:"none",display:"flex",alignItems:"center"},[`&-active:not(${pe}-option-disabled)`]:{backgroundColor:e.optionActiveBg},[`&-selected:not(${pe}-option-disabled)`]:{color:e.optionSelectedColor,fontWeight:e.optionSelectedFontWeight,backgroundColor:e.optionSelectedBg,[`${pe}-option-state`]:{color:e.colorPrimary}},"&-disabled":{[`&${pe}-option-selected`]:{backgroundColor:e.colorBgContainerDisabled},color:e.colorTextDisabled,cursor:"not-allowed"},"&-grouped":{paddingInlineStart:e.calc(e.controlPaddingHorizontal).mul(2).equal()}},"&-empty":Object.assign(Object.assign({},Et(e)),{color:e.colorTextDisabled})}),[`${zt}:has(+ ${zt})`]:{borderEndStartRadius:0,borderEndEndRadius:0,[`& + ${zt}`]:{borderStartStartRadius:0,borderStartEndRadius:0}},"&-rtl":{direction:"rtl"}})},(0,A.oN)(e,"slide-up"),(0,A.oN)(e,"slide-down"),(0,qe.Fm)(e,"move-up"),(0,qe.Fm)(e,"move-down")]},r=i(16928),we=i(11568);function s(e,g){const{componentCls:f,inputPaddingHorizontalBase:pe,borderRadius:Ze}=e,Ae=e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),et=g?`${f}-${g}`:"";return{[`${f}-single${et}`]:{fontSize:e.fontSize,height:e.controlHeight,[`${f}-selector`]:Object.assign(Object.assign({},(0,C.Wf)(e,!0)),{display:"flex",borderRadius:Ze,flex:"1 1 auto",[`${f}-selection-wrap:after`]:{lineHeight:(0,we.bf)(Ae)},[`${f}-selection-search`]:{position:"absolute",inset:0,width:"100%","&-input":{width:"100%",WebkitAppearance:"textfield"}},[`
          ${f}-selection-item,
          ${f}-selection-placeholder
        `]:{display:"block",padding:0,lineHeight:(0,we.bf)(Ae),transition:`all ${e.motionDurationSlow}, visibility 0s`,alignSelf:"center"},[`${f}-selection-placeholder`]:{transition:"none",pointerEvents:"none"},[["&:after",`${f}-selection-item:empty:after`,`${f}-selection-placeholder:empty:after`].join(",")]:{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'}}),[`
        &${f}-show-arrow ${f}-selection-item,
        &${f}-show-arrow ${f}-selection-search,
        &${f}-show-arrow ${f}-selection-placeholder
      `]:{paddingInlineEnd:e.showArrowPaddingInlineEnd},[`&${f}-open ${f}-selection-item`]:{color:e.colorTextPlaceholder},[`&:not(${f}-customize-input)`]:{[`${f}-selector`]:{width:"100%",height:"100%",alignItems:"center",padding:`0 ${(0,we.bf)(pe)}`,[`${f}-selection-search-input`]:{height:Ae,fontSize:e.fontSize},"&:after":{lineHeight:(0,we.bf)(Ae)}}},[`&${f}-customize-input`]:{[`${f}-selector`]:{"&:after":{display:"none"},[`${f}-selection-search`]:{position:"static",width:"100%"},[`${f}-selection-placeholder`]:{position:"absolute",insetInlineStart:0,insetInlineEnd:0,padding:`0 ${(0,we.bf)(pe)}`,"&:after":{display:"none"}}}}}}}function vn(e){const{componentCls:g}=e,f=e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal();return[s(e),s((0,R.IX)(e,{controlHeight:e.controlHeightSM,borderRadius:e.borderRadiusSM}),"sm"),{[`${g}-single${g}-sm`]:{[`&:not(${g}-customize-input)`]:{[`${g}-selector`]:{padding:`0 ${(0,we.bf)(f)}`},[`&${g}-show-arrow ${g}-selection-search`]:{insetInlineEnd:e.calc(f).add(e.calc(e.fontSize).mul(1.5)).equal()},[`
            &${g}-show-arrow ${g}-selection-item,
            &${g}-show-arrow ${g}-selection-placeholder
          `]:{paddingInlineEnd:e.calc(e.fontSize).mul(1.5).equal()}}}},s((0,R.IX)(e,{controlHeight:e.singleItemHeightLG,fontSize:e.fontSizeLG,borderRadius:e.borderRadiusLG}),"lg")]}const mn=e=>{const{fontSize:g,lineHeight:f,lineWidth:pe,controlHeight:Ze,controlHeightSM:Ae,controlHeightLG:et,paddingXXS:Ie,controlPaddingHorizontal:zt,zIndexPopupBase:Sn,colorText:wn,fontWeightStrong:Dt,controlItemBgActive:Vt,controlItemBgHover:an,colorBgContainer:Ht,colorFillSecondary:$n,colorBgContainerDisabled:Tn,colorTextDisabled:zn,colorPrimaryHover:Vn,colorPrimary:a,controlOutline:D}=e,I=Ie*2,O=pe*2,z=Math.min(Ze-I,Ze-O),F=Math.min(Ae-I,Ae-O),N=Math.min(et-I,et-O);return{INTERNAL_FIXED_ITEM_MARGIN:Math.floor(Ie/2),zIndexPopup:Sn+50,optionSelectedColor:wn,optionSelectedFontWeight:Dt,optionSelectedBg:Vt,optionActiveBg:an,optionPadding:`${(Ze-g*f)/2}px ${zt}px`,optionFontSize:g,optionLineHeight:f,optionHeight:Ze,selectorBg:Ht,clearBg:Ht,singleItemHeightLG:et,multipleItemBg:$n,multipleItemBorderColor:"transparent",multipleItemHeight:z,multipleItemHeightSM:F,multipleItemHeightLG:N,multipleSelectorBgDisabled:Tn,multipleItemColorDisabled:zn,multipleItemBorderColorDisabled:"transparent",showArrowPaddingInlineEnd:Math.ceil(e.fontSize*1.25),hoverBorderColor:Vn,activeBorderColor:a,activeOutlineColor:D,selectAffixPadding:Ie}},on=(e,g)=>{const{componentCls:f,antCls:pe,controlOutlineWidth:Ze}=e;return{[`&:not(${f}-customize-input) ${f}-selector`]:{border:`${(0,we.bf)(e.lineWidth)} ${e.lineType} ${g.borderColor}`,background:e.selectorBg},[`&:not(${f}-disabled):not(${f}-customize-input):not(${pe}-pagination-size-changer)`]:{[`&:hover ${f}-selector`]:{borderColor:g.hoverBorderHover},[`${f}-focused& ${f}-selector`]:{borderColor:g.activeBorderColor,boxShadow:`0 0 0 ${(0,we.bf)(Ze)} ${g.activeOutlineColor}`,outline:0},[`${f}-prefix`]:{color:g.color}}}},gn=(e,g)=>({[`&${e.componentCls}-status-${g.status}`]:Object.assign({},on(e,g))}),_t=e=>({"&-outlined":Object.assign(Object.assign(Object.assign(Object.assign({},on(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),gn(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),gn(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${(0,we.bf)(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})}),pn=(e,g)=>{const{componentCls:f,antCls:pe}=e;return{[`&:not(${f}-customize-input) ${f}-selector`]:{background:g.bg,border:`${(0,we.bf)(e.lineWidth)} ${e.lineType} transparent`,color:g.color},[`&:not(${f}-disabled):not(${f}-customize-input):not(${pe}-pagination-size-changer)`]:{[`&:hover ${f}-selector`]:{background:g.hoverBg},[`${f}-focused& ${f}-selector`]:{background:e.selectorBg,borderColor:g.activeBorderColor,outline:0}}}},Ut=(e,g)=>({[`&${e.componentCls}-status-${g.status}`]:Object.assign({},pn(e,g))}),hn=e=>({"&-filled":Object.assign(Object.assign(Object.assign(Object.assign({},pn(e,{bg:e.colorFillTertiary,hoverBg:e.colorFillSecondary,activeBorderColor:e.activeBorderColor,color:e.colorText})),Ut(e,{status:"error",bg:e.colorErrorBg,hoverBg:e.colorErrorBgHover,activeBorderColor:e.colorError,color:e.colorError})),Ut(e,{status:"warning",bg:e.colorWarningBg,hoverBg:e.colorWarningBgHover,activeBorderColor:e.colorWarning,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{borderColor:e.colorBorder,background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.colorBgContainer,border:`${(0,we.bf)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`}})}),J=e=>({"&-borderless":{[`${e.componentCls}-selector`]:{background:"transparent",border:`${(0,we.bf)(e.lineWidth)} ${e.lineType} transparent`},[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${(0,we.bf)(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`},[`&${e.componentCls}-status-error`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorError}},[`&${e.componentCls}-status-warning`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorWarning}}}}),ge=(e,g)=>{const{componentCls:f,antCls:pe}=e;return{[`&:not(${f}-customize-input) ${f}-selector`]:{borderWidth:`0 0 ${(0,we.bf)(e.lineWidth)} 0`,borderStyle:`none none ${e.lineType} none`,borderColor:g.borderColor,background:e.selectorBg,borderRadius:0},[`&:not(${f}-disabled):not(${f}-customize-input):not(${pe}-pagination-size-changer)`]:{[`&:hover ${f}-selector`]:{borderColor:g.hoverBorderHover},[`${f}-focused& ${f}-selector`]:{borderColor:g.activeBorderColor,outline:0},[`${f}-prefix`]:{color:g.color}}}},Be=(e,g)=>({[`&${e.componentCls}-status-${g.status}`]:Object.assign({},ge(e,g))}),p=e=>({"&-underlined":Object.assign(Object.assign(Object.assign(Object.assign({},ge(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),Be(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),Be(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${(0,we.bf)(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})});var Ne=e=>({[e.componentCls]:Object.assign(Object.assign(Object.assign(Object.assign({},_t(e)),hn(e)),J(e)),p(e))});const Nt=e=>{const{componentCls:g}=e;return{position:"relative",transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,input:{cursor:"pointer"},[`${g}-show-search&`]:{cursor:"text",input:{cursor:"auto",color:"inherit",height:"100%"}},[`${g}-disabled&`]:{cursor:"not-allowed",input:{cursor:"not-allowed"}}}},We=e=>{const{componentCls:g}=e;return{[`${g}-selection-search-input`]:{margin:0,padding:0,background:"transparent",border:"none",outline:"none",appearance:"none",fontFamily:"inherit","&::-webkit-search-cancel-button":{display:"none",appearance:"none"}}}},At=e=>{const{antCls:g,componentCls:f,inputPaddingHorizontalBase:pe,iconCls:Ze}=e,Ae={[`${f}-clear`]:{opacity:1,background:e.colorBgBase,borderRadius:"50%"}};return{[f]:Object.assign(Object.assign({},(0,C.Wf)(e)),{position:"relative",display:"inline-flex",cursor:"pointer",[`&:not(${f}-customize-input) ${f}-selector`]:Object.assign(Object.assign({},Nt(e)),We(e)),[`${f}-selection-item`]:Object.assign(Object.assign({flex:1,fontWeight:"normal",position:"relative",userSelect:"none"},C.vS),{[`> ${g}-typography`]:{display:"inline"}}),[`${f}-selection-placeholder`]:Object.assign(Object.assign({},C.vS),{flex:1,color:e.colorTextPlaceholder,pointerEvents:"none"}),[`${f}-arrow`]:Object.assign(Object.assign({},(0,C.Ro)()),{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:pe,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,lineHeight:1,textAlign:"center",pointerEvents:"none",display:"flex",alignItems:"center",transition:`opacity ${e.motionDurationSlow} ease`,[Ze]:{verticalAlign:"top",transition:`transform ${e.motionDurationSlow}`,"> svg":{verticalAlign:"top"},[`&:not(${f}-suffix)`]:{pointerEvents:"auto"}},[`${f}-disabled &`]:{cursor:"not-allowed"},"> *:not(:last-child)":{marginInlineEnd:8}}),[`${f}-selection-wrap`]:{display:"flex",width:"100%",position:"relative",minWidth:0,"&:after":{content:'"\\a0"',width:0,overflow:"hidden"}},[`${f}-prefix`]:{flex:"none",marginInlineEnd:e.selectAffixPadding},[`${f}-clear`]:{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:pe,zIndex:1,display:"inline-block",width:e.fontSizeIcon,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",cursor:"pointer",opacity:0,transition:`color ${e.motionDurationMid} ease, opacity ${e.motionDurationSlow} ease`,textRendering:"auto","&:before":{display:"block"},"&:hover":{color:e.colorIcon}},"@media(hover:none)":Ae,"&:hover":Ae}),[`${f}-status`]:{"&-error, &-warning, &-success, &-validating":{[`&${f}-has-feedback`]:{[`${f}-clear`]:{insetInlineEnd:e.calc(pe).add(e.fontSize).add(e.paddingXS).equal()}}}}}},De=e=>{const{componentCls:g}=e;return[{[g]:{[`&${g}-in-form-item`]:{width:"100%"}}},At(e),vn(e),(0,r.ZP)(e),Kt(e),{[`${g}-rtl`]:{direction:"rtl"}},(0,ue.c)(e,{borderElCls:`${g}-selector`,focusElCls:`${g}-focused`})]};var ce=(0,P.I$)("Select",(e,{rootPrefixCls:g})=>{const f=(0,R.IX)(e,{rootPrefixCls:g,inputPaddingHorizontalBase:e.calc(e.paddingSM).sub(1).equal(),multipleSelectItemHeight:e.multipleItemHeight,selectHeight:e.controlHeight});return[De(f),Ne(f)]},mn,{unitless:{optionLineHeight:!0,optionSelectedFontWeight:!0}})},78642:function(Zn,pt,i){i.d(pt,{Z:function(){return C}});function C(ue,P){return P!==void 0?P:ue!==null}},88708:function(Zn,pt,i){i.d(pt,{ZP:function(){return Et}});var C=i(97685),ue=i(67294),P=i(98924),R=0,A=(0,P.Z)();function qe(){var ke;return A?(ke=R,R+=1):ke="TEST_OR_SSR",ke}function Et(ke){var Kt=ue.useState(),r=(0,C.Z)(Kt,2),we=r[0],s=r[1];return ue.useEffect(function(){s("rc_select_".concat(qe()))},[]),ke||we}},50089:function(Zn,pt,i){i.d(pt,{Ac:function(){return Ce},Xo:function(){return ut},Wx:function(){return ct},ZP:function(){return Bn},lk:function(){return hn}});var C=i(87462),ue=i(74902),P=i(4942),R=i(1413),A=i(97685),qe=i(91),Et=i(71002),ke=i(21770),Kt=i(80334),r=i(67294),we=i(93967),s=i.n(we),vn=i(8410),mn=i(31131),on=i(42550),gn=function(t){var c=t.className,o=t.customizeIcon,u=t.customizeIconProps,m=t.children,v=t.onMouseDown,h=t.onClick,y=typeof o=="function"?o(u):o;return r.createElement("span",{className:c,onMouseDown:function(x){x.preventDefault(),v==null||v(x)},style:{userSelect:"none",WebkitUserSelect:"none"},unselectable:"on",onClick:h,"aria-hidden":!0},y!==void 0?y:r.createElement("span",{className:s()(c.split(/\s+/).map(function(E){return"".concat(E,"-icon")}))},m))},_t=gn,pn=function(t,c,o,u,m){var v=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!1,h=arguments.length>6?arguments[6]:void 0,y=arguments.length>7?arguments[7]:void 0,E=r.useMemo(function(){if((0,Et.Z)(u)==="object")return u.clearIcon;if(m)return m},[u,m]),x=r.useMemo(function(){return!!(!v&&u&&(o.length||h)&&!(y==="combobox"&&h===""))},[u,v,o.length,h,y]);return{allowClear:x,clearIcon:r.createElement(_t,{className:"".concat(t,"-clear"),onMouseDown:c,customizeIcon:E},"\xD7")}},Ut=r.createContext(null);function hn(){return r.useContext(Ut)}function J(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:10,t=r.useState(!1),c=(0,A.Z)(t,2),o=c[0],u=c[1],m=r.useRef(null),v=function(){window.clearTimeout(m.current)};r.useEffect(function(){return v},[]);var h=function(E,x){v(),m.current=window.setTimeout(function(){u(E),x&&x()},n)};return[o,h,v]}function ge(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:250,t=r.useRef(null),c=r.useRef(null);r.useEffect(function(){return function(){window.clearTimeout(c.current)}},[]);function o(u){(u||t.current===null)&&(t.current=u),window.clearTimeout(c.current),c.current=window.setTimeout(function(){t.current=null},n)}return[function(){return t.current},o]}function Be(n,t,c,o){var u=r.useRef(null);u.current={open:t,triggerOpen:c,customizedTrigger:o},r.useEffect(function(){function m(v){var h;if(!((h=u.current)!==null&&h!==void 0&&h.customizedTrigger)){var y=v.target;y.shadowRoot&&v.composed&&(y=v.composedPath()[0]||y),u.current.open&&n().filter(function(E){return E}).every(function(E){return!E.contains(y)&&E!==y})&&u.current.triggerOpen(!1)}}return window.addEventListener("mousedown",m),function(){return window.removeEventListener("mousedown",m)}},[])}var p=i(15105);function Le(n){return n&&![p.Z.ESC,p.Z.SHIFT,p.Z.BACKSPACE,p.Z.TAB,p.Z.WIN_KEY,p.Z.ALT,p.Z.META,p.Z.WIN_KEY_RIGHT,p.Z.CTRL,p.Z.SEMICOLON,p.Z.EQUALS,p.Z.CAPS_LOCK,p.Z.CONTEXT_MENU,p.Z.F1,p.Z.F2,p.Z.F3,p.Z.F4,p.Z.F5,p.Z.F6,p.Z.F7,p.Z.F8,p.Z.F9,p.Z.F10,p.Z.F11,p.Z.F12].includes(n)}var Ne=i(64217),Nt=i(39983);function We(n,t,c){var o=(0,R.Z)((0,R.Z)({},n),c?t:{});return Object.keys(t).forEach(function(u){var m=t[u];typeof m=="function"&&(o[u]=function(){for(var v,h=arguments.length,y=new Array(h),E=0;E<h;E++)y[E]=arguments[E];return m.apply(void 0,y),(v=n[u])===null||v===void 0?void 0:v.call.apply(v,[n].concat(y))})}),o}var At=We,De=["prefixCls","id","inputElement","autoFocus","autoComplete","editable","activeDescendantId","value","open","attrs"],ce=function(t,c){var o=t.prefixCls,u=t.id,m=t.inputElement,v=t.autoFocus,h=t.autoComplete,y=t.editable,E=t.activeDescendantId,x=t.value,B=t.open,Q=t.attrs,S=(0,qe.Z)(t,De),G=m||r.createElement("input",null),V=G,K=V.ref,oe=V.props;return(0,Kt.Kp)(!("maxLength"in G.props),"Passing 'maxLength' to input element directly may not work because input in BaseSelect is controlled."),G=r.cloneElement(G,(0,R.Z)((0,R.Z)((0,R.Z)({type:"search"},At(S,oe,!0)),{},{id:u,ref:(0,on.sQ)(c,K),autoComplete:h||"off",autoFocus:v,className:s()("".concat(o,"-selection-search-input"),oe==null?void 0:oe.className),role:"combobox","aria-expanded":B||!1,"aria-haspopup":"listbox","aria-owns":"".concat(u,"_list"),"aria-autocomplete":"list","aria-controls":"".concat(u,"_list"),"aria-activedescendant":B?E:void 0},Q),{},{value:y?x:"",readOnly:!y,unselectable:y?null:"on",style:(0,R.Z)((0,R.Z)({},oe.style),{},{opacity:y?null:0})})),G},e=r.forwardRef(ce),g=e;function f(n){return Array.isArray(n)?n:n!==void 0?[n]:[]}var pe=typeof window!="undefined"&&window.document&&window.document.documentElement,Ze=pe;function Ae(n){return n!=null}function et(n){return!n&&n!==0}function Ie(n){return["string","number"].includes((0,Et.Z)(n))}function zt(n){var t=void 0;return n&&(Ie(n.title)?t=n.title.toString():Ie(n.label)&&(t=n.label.toString())),t}function Sn(n,t){Ze?r.useLayoutEffect(n,t):r.useEffect(n,t)}function wn(n){var t;return(t=n.key)!==null&&t!==void 0?t:n.value}var Dt=function(t){t.preventDefault(),t.stopPropagation()},Vt=function(t){var c=t.id,o=t.prefixCls,u=t.values,m=t.open,v=t.searchValue,h=t.autoClearSearchValue,y=t.inputRef,E=t.placeholder,x=t.disabled,B=t.mode,Q=t.showSearch,S=t.autoFocus,G=t.autoComplete,V=t.activeDescendantId,K=t.tabIndex,oe=t.removeIcon,je=t.maxTagCount,Oe=t.maxTagTextLength,le=t.maxTagPlaceholder,$e=le===void 0?function(w){return"+ ".concat(w.length," ...")}:le,ee=t.tagRender,xe=t.onToggleOpen,lt=t.onRemove,Ee=t.onInputChange,Me=t.onInputPaste,Ke=t.onInputKeyDown,Te=t.onInputMouseDown,_e=t.onInputCompositionStart,Ue=t.onInputCompositionEnd,Re=t.onInputBlur,ze=r.useRef(null),nt=(0,r.useState)(0),Xe=(0,A.Z)(nt,2),Pe=Xe[0],ve=Xe[1],ae=(0,r.useState)(!1),rt=(0,A.Z)(ae,2),bt=rt[0],Ct=rt[1],Ve="".concat(o,"-selection"),Jt=m||B==="multiple"&&h===!1||B==="tags"?v:"",Qe=B==="tags"||B==="multiple"&&h===!1||Q&&(m||bt);Sn(function(){ve(ze.current.scrollWidth)},[Jt]);var cn=function(b,H,me,Se,he){return r.createElement("span",{title:zt(b),className:s()("".concat(Ve,"-item"),(0,P.Z)({},"".concat(Ve,"-item-disabled"),me))},r.createElement("span",{className:"".concat(Ve,"-item-content")},H),Se&&r.createElement(_t,{className:"".concat(Ve,"-item-remove"),onMouseDown:Dt,onClick:he,customizeIcon:oe},"\xD7"))},qt=function(b,H,me,Se,he,mt){var Bt=function(On){Dt(On),xe(!m)};return r.createElement("span",{onMouseDown:Bt},ee({label:H,value:b,disabled:me,closable:Se,onClose:he,isMaxTag:!!mt}))},Je=function(b){var H=b.disabled,me=b.label,Se=b.value,he=!x&&!H,mt=me;if(typeof Oe=="number"&&(typeof me=="string"||typeof me=="number")){var Bt=String(mt);Bt.length>Oe&&(mt="".concat(Bt.slice(0,Oe),"..."))}var Wt=function(ot){ot&&ot.stopPropagation(),lt(b)};return typeof ee=="function"?qt(Se,mt,H,he,Wt):cn(b,mt,H,he,Wt)},q=function(b){if(!u.length)return null;var H=typeof $e=="function"?$e(b):$e;return typeof ee=="function"?qt(void 0,H,!1,!1,void 0,!0):cn({title:H},H,!1)},l=r.createElement("div",{className:"".concat(Ve,"-search"),style:{width:Pe},onFocus:function(){Ct(!0)},onBlur:function(){Ct(!1)}},r.createElement(g,{ref:y,open:m,prefixCls:o,id:c,inputElement:null,disabled:x,autoFocus:S,autoComplete:G,editable:Qe,activeDescendantId:V,value:Jt,onKeyDown:Ke,onMouseDown:Te,onChange:Ee,onPaste:Me,onCompositionStart:_e,onCompositionEnd:Ue,onBlur:Re,tabIndex:K,attrs:(0,Ne.Z)(t,!0)}),r.createElement("span",{ref:ze,className:"".concat(Ve,"-search-mirror"),"aria-hidden":!0},Jt,"\xA0")),d=r.createElement(Nt.Z,{prefixCls:"".concat(Ve,"-overflow"),data:u,renderItem:Je,renderRest:q,suffix:l,itemKey:wn,maxCount:je});return r.createElement("span",{className:"".concat(Ve,"-wrap")},d,!u.length&&!Jt&&r.createElement("span",{className:"".concat(Ve,"-placeholder")},E))},an=Vt,Ht=function(t){var c=t.inputElement,o=t.prefixCls,u=t.id,m=t.inputRef,v=t.disabled,h=t.autoFocus,y=t.autoComplete,E=t.activeDescendantId,x=t.mode,B=t.open,Q=t.values,S=t.placeholder,G=t.tabIndex,V=t.showSearch,K=t.searchValue,oe=t.activeValue,je=t.maxLength,Oe=t.onInputKeyDown,le=t.onInputMouseDown,$e=t.onInputChange,ee=t.onInputPaste,xe=t.onInputCompositionStart,lt=t.onInputCompositionEnd,Ee=t.onInputBlur,Me=t.title,Ke=r.useState(!1),Te=(0,A.Z)(Ke,2),_e=Te[0],Ue=Te[1],Re=x==="combobox",ze=Re||V,nt=Q[0],Xe=K||"";Re&&oe&&!_e&&(Xe=oe),r.useEffect(function(){Re&&Ue(!1)},[Re,oe]);var Pe=x!=="combobox"&&!B&&!V?!1:!!Xe,ve=Me===void 0?zt(nt):Me,ae=r.useMemo(function(){return nt?null:r.createElement("span",{className:"".concat(o,"-selection-placeholder"),style:Pe?{visibility:"hidden"}:void 0},S)},[nt,Pe,S,o]);return r.createElement("span",{className:"".concat(o,"-selection-wrap")},r.createElement("span",{className:"".concat(o,"-selection-search")},r.createElement(g,{ref:m,prefixCls:o,id:u,open:B,inputElement:c,disabled:v,autoFocus:h,autoComplete:y,editable:ze,activeDescendantId:E,value:Xe,onKeyDown:Oe,onMouseDown:le,onChange:function(bt){Ue(!0),$e(bt)},onPaste:ee,onCompositionStart:xe,onCompositionEnd:lt,onBlur:Ee,tabIndex:G,attrs:(0,Ne.Z)(t,!0),maxLength:Re?je:void 0})),!Re&&nt?r.createElement("span",{className:"".concat(o,"-selection-item"),title:ve,style:Pe?{visibility:"hidden"}:void 0},nt.label):null,ae)},$n=Ht,Tn=function(t,c){var o=(0,r.useRef)(null),u=(0,r.useRef)(!1),m=t.prefixCls,v=t.open,h=t.mode,y=t.showSearch,E=t.tokenWithEnter,x=t.disabled,B=t.prefix,Q=t.autoClearSearchValue,S=t.onSearch,G=t.onSearchSubmit,V=t.onToggleOpen,K=t.onInputKeyDown,oe=t.onInputBlur,je=t.domRef;r.useImperativeHandle(c,function(){return{focus:function(ve){o.current.focus(ve)},blur:function(){o.current.blur()}}});var Oe=ge(0),le=(0,A.Z)(Oe,2),$e=le[0],ee=le[1],xe=function(ve){var ae=ve.which,rt=o.current instanceof HTMLTextAreaElement;!rt&&v&&(ae===p.Z.UP||ae===p.Z.DOWN)&&ve.preventDefault(),K&&K(ve),ae===p.Z.ENTER&&h==="tags"&&!u.current&&!v&&(G==null||G(ve.target.value)),!(rt&&!v&&~[p.Z.UP,p.Z.DOWN,p.Z.LEFT,p.Z.RIGHT].indexOf(ae))&&Le(ae)&&V(!0)},lt=function(){ee(!0)},Ee=(0,r.useRef)(null),Me=function(ve){S(ve,!0,u.current)!==!1&&V(!0)},Ke=function(){u.current=!0},Te=function(ve){u.current=!1,h!=="combobox"&&Me(ve.target.value)},_e=function(ve){var ae=ve.target.value;if(E&&Ee.current&&/[\r\n]/.test(Ee.current)){var rt=Ee.current.replace(/[\r\n]+$/,"").replace(/\r\n/g," ").replace(/[\r\n]/g," ");ae=ae.replace(rt,Ee.current)}Ee.current=null,Me(ae)},Ue=function(ve){var ae=ve.clipboardData,rt=ae==null?void 0:ae.getData("text");Ee.current=rt||""},Re=function(ve){var ae=ve.target;if(ae!==o.current){var rt=document.body.style.msTouchAction!==void 0;rt?setTimeout(function(){o.current.focus()}):o.current.focus()}},ze=function(ve){var ae=$e();ve.target!==o.current&&!ae&&!(h==="combobox"&&x)&&ve.preventDefault(),(h!=="combobox"&&(!y||!ae)||!v)&&(v&&Q!==!1&&S("",!0,!1),V())},nt={inputRef:o,onInputKeyDown:xe,onInputMouseDown:lt,onInputChange:_e,onInputPaste:Ue,onInputCompositionStart:Ke,onInputCompositionEnd:Te,onInputBlur:oe},Xe=h==="multiple"||h==="tags"?r.createElement(an,(0,C.Z)({},t,nt)):r.createElement($n,(0,C.Z)({},t,nt));return r.createElement("div",{ref:je,className:"".concat(m,"-selector"),onClick:Re,onMouseDown:ze},B&&r.createElement("div",{className:"".concat(m,"-prefix")},B),Xe)},zn=r.forwardRef(Tn),Vn=zn,a=i(40228),D=["prefixCls","disabled","visible","children","popupElement","animation","transitionName","dropdownStyle","dropdownClassName","direction","placement","builtinPlacements","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","getPopupContainer","empty","getTriggerDOMNode","onPopupVisibleChange","onPopupMouseEnter"],I=function(t){var c=t===!0?0:1;return{bottomLeft:{points:["tl","bl"],offset:[0,4],overflow:{adjustX:c,adjustY:1},htmlRegion:"scroll"},bottomRight:{points:["tr","br"],offset:[0,4],overflow:{adjustX:c,adjustY:1},htmlRegion:"scroll"},topLeft:{points:["bl","tl"],offset:[0,-4],overflow:{adjustX:c,adjustY:1},htmlRegion:"scroll"},topRight:{points:["br","tr"],offset:[0,-4],overflow:{adjustX:c,adjustY:1},htmlRegion:"scroll"}}},O=function(t,c){var o=t.prefixCls,u=t.disabled,m=t.visible,v=t.children,h=t.popupElement,y=t.animation,E=t.transitionName,x=t.dropdownStyle,B=t.dropdownClassName,Q=t.direction,S=Q===void 0?"ltr":Q,G=t.placement,V=t.builtinPlacements,K=t.dropdownMatchSelectWidth,oe=t.dropdownRender,je=t.dropdownAlign,Oe=t.getPopupContainer,le=t.empty,$e=t.getTriggerDOMNode,ee=t.onPopupVisibleChange,xe=t.onPopupMouseEnter,lt=(0,qe.Z)(t,D),Ee="".concat(o,"-dropdown"),Me=h;oe&&(Me=oe(h));var Ke=r.useMemo(function(){return V||I(K)},[V,K]),Te=y?"".concat(Ee,"-").concat(y):E,_e=typeof K=="number",Ue=r.useMemo(function(){return _e?null:K===!1?"minWidth":"width"},[K,_e]),Re=x;_e&&(Re=(0,R.Z)((0,R.Z)({},Re),{},{width:K}));var ze=r.useRef(null);return r.useImperativeHandle(c,function(){return{getPopupElement:function(){var Xe;return(Xe=ze.current)===null||Xe===void 0?void 0:Xe.popupElement}}}),r.createElement(a.Z,(0,C.Z)({},lt,{showAction:ee?["click"]:[],hideAction:ee?["click"]:[],popupPlacement:G||(S==="rtl"?"bottomRight":"bottomLeft"),builtinPlacements:Ke,prefixCls:Ee,popupTransitionName:Te,popup:r.createElement("div",{onMouseEnter:xe},Me),ref:ze,stretch:Ue,popupAlign:je,popupVisible:m,getPopupContainer:Oe,popupClassName:s()(B,(0,P.Z)({},"".concat(Ee,"-empty"),le)),popupStyle:Re,getTriggerDOMNode:$e,onPopupVisibleChange:ee}),v)},z=r.forwardRef(O),F=z,N=i(84506);function Z(n,t){var c=n.key,o;return"value"in n&&(o=n.value),c!=null?c:o!==void 0?o:"rc-index-key-".concat(t)}function ne(n){return typeof n!="undefined"&&!Number.isNaN(n)}function U(n,t){var c=n||{},o=c.label,u=c.value,m=c.options,v=c.groupLabel,h=o||(t?"children":"label");return{label:h,value:u||"value",options:m||"options",groupLabel:v||h}}function te(n){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},c=t.fieldNames,o=t.childrenAsData,u=[],m=U(c,!1),v=m.label,h=m.value,y=m.options,E=m.groupLabel;function x(B,Q){Array.isArray(B)&&B.forEach(function(S){if(Q||!(y in S)){var G=S[h];u.push({key:Z(S,u.length),groupOption:Q,data:S,label:S[v],value:G})}else{var V=S[E];V===void 0&&o&&(V=S.label),u.push({key:Z(S,u.length),group:!0,data:S,label:V}),x(S[y],!0)}})}return x(n,!1),u}function $(n){var t=(0,R.Z)({},n);return"props"in t||Object.defineProperty(t,"props",{get:function(){return(0,Kt.ZP)(!1,"Return type is option instead of Option instance. Please read value directly instead of reading from `props`."),t}}),t}var Y=function(t,c,o){if(!c||!c.length)return null;var u=!1,m=function h(y,E){var x=(0,N.Z)(E),B=x[0],Q=x.slice(1);if(!B)return[y];var S=y.split(B);return u=u||S.length>1,S.reduce(function(G,V){return[].concat((0,ue.Z)(G),(0,ue.Z)(h(V,Q)))},[]).filter(Boolean)},v=m(t,c);return u?typeof o!="undefined"?v.slice(0,o):v:null},L=r.createContext(null),T=L;function M(n){var t=n.visible,c=n.values;if(!t)return null;var o=50;return r.createElement("span",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"".concat(c.slice(0,o).map(function(u){var m=u.label,v=u.value;return["number","string"].includes((0,Et.Z)(m))?m:v}).join(", ")),c.length>o?", ...":null)}var ie=["id","prefixCls","className","showSearch","tagRender","direction","omitDomProps","displayValues","onDisplayValuesChange","emptyOptions","notFoundContent","onClear","mode","disabled","loading","getInputElement","getRawInputElement","open","defaultOpen","onDropdownVisibleChange","activeValue","onActiveValueChange","activeDescendantId","searchValue","autoClearSearchValue","onSearch","onSearchSplit","tokenSeparators","allowClear","prefix","suffixIcon","clearIcon","OptionList","animation","transitionName","dropdownStyle","dropdownClassName","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","placement","builtinPlacements","getPopupContainer","showAction","onFocus","onBlur","onKeyUp","onKeyDown","onMouseDown"],de=["value","onChange","removeIcon","placeholder","autoFocus","maxTagCount","maxTagTextLength","maxTagPlaceholder","choiceTransitionName","onInputKeyDown","onPopupScroll","tabIndex"],j=function(t){return t==="tags"||t==="multiple"},fe=r.forwardRef(function(n,t){var c,o=n.id,u=n.prefixCls,m=n.className,v=n.showSearch,h=n.tagRender,y=n.direction,E=n.omitDomProps,x=n.displayValues,B=n.onDisplayValuesChange,Q=n.emptyOptions,S=n.notFoundContent,G=S===void 0?"Not Found":S,V=n.onClear,K=n.mode,oe=n.disabled,je=n.loading,Oe=n.getInputElement,le=n.getRawInputElement,$e=n.open,ee=n.defaultOpen,xe=n.onDropdownVisibleChange,lt=n.activeValue,Ee=n.onActiveValueChange,Me=n.activeDescendantId,Ke=n.searchValue,Te=n.autoClearSearchValue,_e=n.onSearch,Ue=n.onSearchSplit,Re=n.tokenSeparators,ze=n.allowClear,nt=n.prefix,Xe=n.suffixIcon,Pe=n.clearIcon,ve=n.OptionList,ae=n.animation,rt=n.transitionName,bt=n.dropdownStyle,Ct=n.dropdownClassName,Ve=n.dropdownMatchSelectWidth,Jt=n.dropdownRender,Qe=n.dropdownAlign,cn=n.placement,qt=n.builtinPlacements,Je=n.getPopupContainer,q=n.showAction,l=q===void 0?[]:q,d=n.onFocus,w=n.onBlur,b=n.onKeyUp,H=n.onKeyDown,me=n.onMouseDown,Se=(0,qe.Z)(n,ie),he=j(K),mt=(v!==void 0?v:he)||K==="combobox",Bt=(0,R.Z)({},Se);de.forEach(function(se){delete Bt[se]}),E==null||E.forEach(function(se){delete Bt[se]});var Wt=r.useState(!1),On=(0,A.Z)(Wt,2),ot=On[0],Un=On[1];r.useEffect(function(){Un((0,mn.Z)())},[]);var Hn=r.useRef(null),sn=r.useRef(null),Lt=r.useRef(null),kt=r.useRef(null),gt=r.useRef(null),xn=r.useRef(!1),Wn=J(),Ln=(0,A.Z)(Wn,3),dn=Ln[0],rn=Ln[1],Qn=Ln[2];r.useImperativeHandle(t,function(){var se,re;return{focus:(se=kt.current)===null||se===void 0?void 0:se.focus,blur:(re=kt.current)===null||re===void 0?void 0:re.blur,scrollTo:function(jt){var ft;return(ft=gt.current)===null||ft===void 0?void 0:ft.scrollTo(jt)},nativeElement:Hn.current||sn.current}});var fn=r.useMemo(function(){var se;if(K!=="combobox")return Ke;var re=(se=x[0])===null||se===void 0?void 0:se.value;return typeof re=="string"||typeof re=="number"?String(re):""},[Ke,K,x]),er=K==="combobox"&&typeof Oe=="function"&&Oe()||null,en=typeof le=="function"&&le(),ur=(0,on.x1)(sn,en==null||(c=en.props)===null||c===void 0?void 0:c.ref),tr=r.useState(!1),nr=(0,A.Z)(tr,2),cr=nr[0],rr=nr[1];(0,vn.Z)(function(){rr(!0)},[]);var or=(0,ke.Z)(!1,{defaultValue:ee,value:$e}),Fn=(0,A.Z)(or,2),Jn=Fn[0],qn=Fn[1],dt=cr?Jn:!1,ar=!G&&Q;(oe||ar&&dt&&K==="combobox")&&(dt=!1);var Xn=ar?!1:dt,W=r.useCallback(function(se){var re=se!==void 0?se:!dt;oe||(qn(re),dt!==re&&(xe==null||xe(re)))},[oe,dt,qn,xe]),k=r.useMemo(function(){return(Re||[]).some(function(se){return[`
`,`\r
`].includes(se)})},[Re]),_=r.useContext(T)||{},X=_.maxCount,be=_.rawValues,He=function(re,Ft,jt){if(!(he&&ne(X)&&(be==null?void 0:be.size)>=X)){var ft=!0,Pt=re;Ee==null||Ee(null);var Dn=Y(re,Re,ne(X)?X-be.size:void 0),En=jt?null:Dn;return K!=="combobox"&&En&&(Pt="",Ue==null||Ue(En),W(!1),ft=!1),_e&&fn!==Pt&&_e(Pt,{source:Ft?"typing":"effect"}),ft}},Mn=function(re){!re||!re.trim()||_e(re,{source:"submit"})};r.useEffect(function(){!dt&&!he&&K!=="combobox"&&He("",!1,!1)},[dt]),r.useEffect(function(){Jn&&oe&&qn(!1),oe&&!xn.current&&rn(!1)},[oe]);var yn=ge(),Nn=(0,A.Z)(yn,2),yt=Nn[0],An=Nn[1],jn=r.useRef(!1),sr=function(re){var Ft=yt(),jt=re.key,ft=jt==="Enter";if(ft&&(K!=="combobox"&&re.preventDefault(),dt||W(!0)),An(!!fn),jt==="Backspace"&&!Ft&&he&&!fn&&x.length){for(var Pt=(0,ue.Z)(x),Dn=null,En=Pt.length-1;En>=0;En-=1){var Kn=Pt[En];if(!Kn.disabled){Pt.splice(En,1),Dn=Kn;break}}Dn&&B(Pt,{type:"remove",values:[Dn]})}for(var Yn=arguments.length,_n=new Array(Yn>1?Yn-1:0),lr=1;lr<Yn;lr++)_n[lr-1]=arguments[lr];if(dt&&(!ft||!jn.current)){var ir;ft&&(jn.current=!0),(ir=gt.current)===null||ir===void 0||ir.onKeyDown.apply(ir,[re].concat(_n))}H==null||H.apply(void 0,[re].concat(_n))},vr=function(re){for(var Ft=arguments.length,jt=new Array(Ft>1?Ft-1:0),ft=1;ft<Ft;ft++)jt[ft-1]=arguments[ft];if(dt){var Pt;(Pt=gt.current)===null||Pt===void 0||Pt.onKeyUp.apply(Pt,[re].concat(jt))}re.key==="Enter"&&(jn.current=!1),b==null||b.apply(void 0,[re].concat(jt))},kn=function(re){var Ft=x.filter(function(jt){return jt!==re});B(Ft,{type:"remove",values:[re]})},Pn=function(){jn.current=!1},dr=r.useRef(!1),br=function(){rn(!0),oe||(d&&!dr.current&&d.apply(void 0,arguments),l.includes("focus")&&W(!0)),dr.current=!0},Cr=function(){xn.current=!0,rn(!1,function(){dr.current=!1,xn.current=!1,W(!1)}),!oe&&(fn&&(K==="tags"?_e(fn,{source:"submit"}):K==="multiple"&&_e("",{source:"blur"})),w&&w.apply(void 0,arguments))},Gn=[];r.useEffect(function(){return function(){Gn.forEach(function(se){return clearTimeout(se)}),Gn.splice(0,Gn.length)}},[]);var yr=function(re){var Ft,jt=re.target,ft=(Ft=Lt.current)===null||Ft===void 0?void 0:Ft.getPopupElement();if(ft&&ft.contains(jt)){var Pt=setTimeout(function(){var Yn=Gn.indexOf(Pt);if(Yn!==-1&&Gn.splice(Yn,1),Qn(),!ot&&!ft.contains(document.activeElement)){var _n;(_n=kt.current)===null||_n===void 0||_n.focus()}});Gn.push(Pt)}for(var Dn=arguments.length,En=new Array(Dn>1?Dn-1:0),Kn=1;Kn<Dn;Kn++)En[Kn-1]=arguments[Kn];me==null||me.apply(void 0,[re].concat(En))},Er=r.useState({}),wr=(0,A.Z)(Er,2),Ir=wr[1];function Rr(){Ir({})}var mr;en&&(mr=function(re){W(re)}),Be(function(){var se;return[Hn.current,(se=Lt.current)===null||se===void 0?void 0:se.getPopupElement()]},Xn,W,!!en);var Or=r.useMemo(function(){return(0,R.Z)((0,R.Z)({},n),{},{notFoundContent:G,open:dt,triggerOpen:Xn,id:o,showSearch:mt,multiple:he,toggleOpen:W})},[n,G,Xn,dt,o,mt,he,W]),gr=!!Xe||je,pr;gr&&(pr=r.createElement(_t,{className:s()("".concat(u,"-arrow"),(0,P.Z)({},"".concat(u,"-arrow-loading"),je)),customizeIcon:Xe,customizeIconProps:{loading:je,searchValue:fn,open:dt,focused:dn,showSearch:mt}}));var xr=function(){var re;V==null||V(),(re=kt.current)===null||re===void 0||re.focus(),B([],{type:"clear",values:x}),He("",!1,!1)},hr=pn(u,xr,x,ze,Pe,oe,fn,K),Mr=hr.allowClear,Pr=hr.clearIcon,Dr=r.createElement(ve,{ref:gt}),Zr=s()(u,m,(0,P.Z)((0,P.Z)((0,P.Z)((0,P.Z)((0,P.Z)((0,P.Z)((0,P.Z)((0,P.Z)((0,P.Z)((0,P.Z)({},"".concat(u,"-focused"),dn),"".concat(u,"-multiple"),he),"".concat(u,"-single"),!he),"".concat(u,"-allow-clear"),ze),"".concat(u,"-show-arrow"),gr),"".concat(u,"-disabled"),oe),"".concat(u,"-loading"),je),"".concat(u,"-open"),dt),"".concat(u,"-customize-input"),er),"".concat(u,"-show-search"),mt)),Sr=r.createElement(F,{ref:Lt,disabled:oe,prefixCls:u,visible:Xn,popupElement:Dr,animation:ae,transitionName:rt,dropdownStyle:bt,dropdownClassName:Ct,direction:y,dropdownMatchSelectWidth:Ve,dropdownRender:Jt,dropdownAlign:Qe,placement:cn,builtinPlacements:qt,getPopupContainer:Je,empty:Q,getTriggerDOMNode:function(re){return sn.current||re},onPopupVisibleChange:mr,onPopupMouseEnter:Rr},en?r.cloneElement(en,{ref:ur}):r.createElement(Vn,(0,C.Z)({},n,{domRef:sn,prefixCls:u,inputElement:er,ref:kt,id:o,prefix:nt,showSearch:mt,autoClearSearchValue:Te,mode:K,activeDescendantId:Me,tagRender:h,values:x,open:dt,onToggleOpen:W,activeValue:lt,searchValue:fn,onSearch:He,onSearchSubmit:Mn,onRemove:kn,tokenWithEnter:k,onInputBlur:Pn}))),fr;return en?fr=Sr:fr=r.createElement("div",(0,C.Z)({className:Zr},Bt,{ref:Hn,onMouseDown:yr,onKeyDown:sr,onKeyUp:vr,onFocus:br,onBlur:Cr}),r.createElement(M,{visible:dn&&!dt,values:x}),Sr,pr,Mr&&Pr),r.createElement(Ut.Provider,{value:Or},fr)}),Ce=fe,it=function(){return null};it.isSelectOptGroup=!0;var ut=it,ye=function(){return null};ye.isSelectOption=!0;var ct=ye,wt=i(56982),Zt=i(98423),ht=i(87718);function It(){return/(mac\sos|macintosh)/i.test(navigator.appVersion)}var Rt=["disabled","title","children","style","className"];function tn(n){return typeof n=="string"||typeof n=="number"}var Ge=function(t,c){var o=hn(),u=o.prefixCls,m=o.id,v=o.open,h=o.multiple,y=o.mode,E=o.searchValue,x=o.toggleOpen,B=o.notFoundContent,Q=o.onPopupScroll,S=r.useContext(T),G=S.maxCount,V=S.flattenOptions,K=S.onActiveValue,oe=S.defaultActiveFirstOption,je=S.onSelect,Oe=S.menuItemSelectedIcon,le=S.rawValues,$e=S.fieldNames,ee=S.virtual,xe=S.direction,lt=S.listHeight,Ee=S.listItemHeight,Me=S.optionRender,Ke="".concat(u,"-item"),Te=(0,wt.Z)(function(){return V},[v,V],function(q,l){return l[0]&&q[1]!==l[1]}),_e=r.useRef(null),Ue=r.useMemo(function(){return h&&ne(G)&&(le==null?void 0:le.size)>=G},[h,G,le==null?void 0:le.size]),Re=function(l){l.preventDefault()},ze=function(l){var d;(d=_e.current)===null||d===void 0||d.scrollTo(typeof l=="number"?{index:l}:l)},nt=r.useCallback(function(q){return y==="combobox"?!1:le.has(q)},[y,(0,ue.Z)(le).toString(),le.size]),Xe=function(l){for(var d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,w=Te.length,b=0;b<w;b+=1){var H=(l+b*d+w)%w,me=Te[H]||{},Se=me.group,he=me.data;if(!Se&&!(he!=null&&he.disabled)&&(nt(he.value)||!Ue))return H}return-1},Pe=r.useState(function(){return Xe(0)}),ve=(0,A.Z)(Pe,2),ae=ve[0],rt=ve[1],bt=function(l){var d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;rt(l);var w={source:d?"keyboard":"mouse"},b=Te[l];if(!b){K(null,-1,w);return}K(b.value,l,w)};(0,r.useEffect)(function(){bt(oe!==!1?Xe(0):-1)},[Te.length,E]);var Ct=r.useCallback(function(q){return y==="combobox"?String(q).toLowerCase()===E.toLowerCase():le.has(q)},[y,E,(0,ue.Z)(le).toString(),le.size]);(0,r.useEffect)(function(){var q=setTimeout(function(){if(!h&&v&&le.size===1){var d=Array.from(le)[0],w=Te.findIndex(function(b){var H=b.data;return E?String(H.value).startsWith(E):H.value===d});w!==-1&&(bt(w),ze(w))}});if(v){var l;(l=_e.current)===null||l===void 0||l.scrollTo(void 0)}return function(){return clearTimeout(q)}},[v,E]);var Ve=function(l){l!==void 0&&je(l,{selected:!le.has(l)}),h||x(!1)};if(r.useImperativeHandle(c,function(){return{onKeyDown:function(l){var d=l.which,w=l.ctrlKey;switch(d){case p.Z.N:case p.Z.P:case p.Z.UP:case p.Z.DOWN:{var b=0;if(d===p.Z.UP?b=-1:d===p.Z.DOWN?b=1:It()&&w&&(d===p.Z.N?b=1:d===p.Z.P&&(b=-1)),b!==0){var H=Xe(ae+b,b);ze(H),bt(H,!0)}break}case p.Z.TAB:case p.Z.ENTER:{var me,Se=Te[ae];Se&&!(Se!=null&&(me=Se.data)!==null&&me!==void 0&&me.disabled)&&!Ue?Ve(Se.value):Ve(void 0),v&&l.preventDefault();break}case p.Z.ESC:x(!1),v&&l.stopPropagation()}},onKeyUp:function(){},scrollTo:function(l){ze(l)}}}),Te.length===0)return r.createElement("div",{role:"listbox",id:"".concat(m,"_list"),className:"".concat(Ke,"-empty"),onMouseDown:Re},B);var Jt=Object.keys($e).map(function(q){return $e[q]}),Qe=function(l){return l.label};function cn(q,l){var d=q.group;return{role:d?"presentation":"option",id:"".concat(m,"_list_").concat(l)}}var qt=function(l){var d=Te[l];if(!d)return null;var w=d.data||{},b=w.value,H=d.group,me=(0,Ne.Z)(w,!0),Se=Qe(d);return d?r.createElement("div",(0,C.Z)({"aria-label":typeof Se=="string"&&!H?Se:null},me,{key:l},cn(d,l),{"aria-selected":Ct(b)}),b):null},Je={role:"listbox",id:"".concat(m,"_list")};return r.createElement(r.Fragment,null,ee&&r.createElement("div",(0,C.Z)({},Je,{style:{height:0,width:0,overflow:"hidden"}}),qt(ae-1),qt(ae),qt(ae+1)),r.createElement(ht.Z,{itemKey:"key",ref:_e,data:Te,height:lt,itemHeight:Ee,fullHeight:!1,onMouseDown:Re,onScroll:Q,virtual:ee,direction:xe,innerProps:ee?null:Je},function(q,l){var d=q.group,w=q.groupOption,b=q.data,H=q.label,me=q.value,Se=b.key;if(d){var he,mt=(he=b.title)!==null&&he!==void 0?he:tn(H)?H.toString():void 0;return r.createElement("div",{className:s()(Ke,"".concat(Ke,"-group"),b.className),title:mt},H!==void 0?H:Se)}var Bt=b.disabled,Wt=b.title,On=b.children,ot=b.style,Un=b.className,Hn=(0,qe.Z)(b,Rt),sn=(0,Zt.Z)(Hn,Jt),Lt=nt(me),kt=Bt||!Lt&&Ue,gt="".concat(Ke,"-option"),xn=s()(Ke,gt,Un,(0,P.Z)((0,P.Z)((0,P.Z)((0,P.Z)({},"".concat(gt,"-grouped"),w),"".concat(gt,"-active"),ae===l&&!kt),"".concat(gt,"-disabled"),kt),"".concat(gt,"-selected"),Lt)),Wn=Qe(q),Ln=!Oe||typeof Oe=="function"||Lt,dn=typeof Wn=="number"?Wn:Wn||me,rn=tn(dn)?dn.toString():void 0;return Wt!==void 0&&(rn=Wt),r.createElement("div",(0,C.Z)({},(0,Ne.Z)(sn),ee?{}:cn(q,l),{"aria-selected":Ct(me),className:xn,title:rn,onMouseMove:function(){ae===l||kt||bt(l)},onClick:function(){kt||Ve(me)},style:ot}),r.createElement("div",{className:"".concat(gt,"-content")},typeof Me=="function"?Me(q,{index:l}):dn),r.isValidElement(Oe)||Lt,Ln&&r.createElement(_t,{className:"".concat(Ke,"-option-state"),customizeIcon:Oe,customizeIconProps:{value:me,disabled:kt,isSelected:Lt}},Lt?"\u2713":null))}))},st=r.forwardRef(Ge),St=st,vt=function(n,t){var c=r.useRef({values:new Map,options:new Map}),o=r.useMemo(function(){var m=c.current,v=m.values,h=m.options,y=n.map(function(B){if(B.label===void 0){var Q;return(0,R.Z)((0,R.Z)({},B),{},{label:(Q=v.get(B.value))===null||Q===void 0?void 0:Q.label})}return B}),E=new Map,x=new Map;return y.forEach(function(B){E.set(B.value,B),x.set(B.value,t.get(B.value)||h.get(B.value))}),c.current.values=E,c.current.options=x,y},[n,t]),u=r.useCallback(function(m){return t.get(m)||c.current.options.get(m)},[t]);return[o,u]};function Xt(n,t){return f(n).join("").toUpperCase().includes(t)}var $t=function(n,t,c,o,u){return r.useMemo(function(){if(!c||o===!1)return n;var m=t.options,v=t.label,h=t.value,y=[],E=typeof o=="function",x=c.toUpperCase(),B=E?o:function(S,G){return u?Xt(G[u],x):G[m]?Xt(G[v!=="children"?v:"label"],x):Xt(G[h],x)},Q=E?function(S){return $(S)}:function(S){return S};return n.forEach(function(S){if(S[m]){var G=B(c,Q(S));if(G)y.push(S);else{var V=S[m].filter(function(K){return B(c,Q(K))});V.length&&y.push((0,R.Z)((0,R.Z)({},S),{},(0,P.Z)({},m,V)))}return}B(c,Q(S))&&y.push(S)}),y},[n,o,u,c,t])},Ot=i(88708),Gt=i(50344),Ye=["children","value"],Tt=["children"];function ln(n){var t=n,c=t.key,o=t.props,u=o.children,m=o.value,v=(0,qe.Z)(o,Ye);return(0,R.Z)({key:c,value:m!==void 0?m:c,children:u},v)}function Yt(n){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return(0,Gt.Z)(n).map(function(c,o){if(!r.isValidElement(c)||!c.type)return null;var u=c,m=u.type.isSelectOptGroup,v=u.key,h=u.props,y=h.children,E=(0,qe.Z)(h,Tt);return t||!m?ln(c):(0,R.Z)((0,R.Z)({key:"__RC_SELECT_GRP__".concat(v===null?o:v,"__"),label:v},E),{},{options:Yt(y)})}).filter(function(c){return c})}var bn=function(t,c,o,u,m){return r.useMemo(function(){var v=t,h=!t;h&&(v=Yt(c));var y=new Map,E=new Map,x=function(S,G,V){V&&typeof V=="string"&&S.set(G[V],G)},B=function Q(S){for(var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,V=0;V<S.length;V+=1){var K=S[V];!K[o.options]||G?(y.set(K[o.value],K),x(E,K,o.label),x(E,K,u),x(E,K,m)):Q(K[o.options],!0)}};return B(v),{options:v,valueOptions:y,labelOptions:E}},[t,c,o,u,m])},Qt=bn;function Fe(n){var t=r.useRef();t.current=n;var c=r.useCallback(function(){return t.current.apply(t,arguments)},[]);return c}function xt(n){var t=n.mode,c=n.options,o=n.children,u=n.backfill,m=n.allowClear,v=n.placeholder,h=n.getInputElement,y=n.showSearch,E=n.onSearch,x=n.defaultOpen,B=n.autoFocus,Q=n.labelInValue,S=n.value,G=n.inputValue,V=n.optionLabelProp,K=isMultiple(t),oe=y!==void 0?y:K||t==="combobox",je=c||convertChildrenToData(o);if(warning(t!=="tags"||je.every(function(ee){return!ee.disabled}),"Please avoid setting option to disabled in tags mode since user can always type text as tag."),t==="tags"||t==="combobox"){var Oe=je.some(function(ee){return ee.options?ee.options.some(function(xe){return typeof("value"in xe?xe.value:xe.key)=="number"}):typeof("value"in ee?ee.value:ee.key)=="number"});warning(!Oe,"`value` of Option should not use number type when `mode` is `tags` or `combobox`.")}if(warning(t!=="combobox"||!V,"`combobox` mode not support `optionLabelProp`. Please set `value` on Option directly."),warning(t==="combobox"||!u,"`backfill` only works with `combobox` mode."),warning(t==="combobox"||!h,"`getInputElement` only work with `combobox` mode."),noteOnce(t!=="combobox"||!h||!m||!v,"Customize `getInputElement` should customize clear and placeholder logic instead of configuring `allowClear` and `placeholder`."),E&&!oe&&t!=="combobox"&&t!=="tags"&&warning(!1,"`onSearch` should work with `showSearch` instead of use alone."),noteOnce(!x||B,"`defaultOpen` makes Select open without focus which means it will not close by click outside. You can set `autoFocus` if needed."),S!=null){var le=toArray(S);warning(!Q||le.every(function(ee){return _typeof(ee)==="object"&&("key"in ee||"value"in ee)}),"`value` should in shape of `{ value: string | number, label?: ReactNode }` when you set `labelInValue` to `true`"),warning(!K||Array.isArray(S),"`value` should be array when `mode` is `multiple` or `tags`")}if(o){var $e=null;toNodeArray(o).some(function(ee){if(!React.isValidElement(ee)||!ee.type)return!1;var xe=ee,lt=xe.type;if(lt.isSelectOption)return!1;if(lt.isSelectOptGroup){var Ee=toNodeArray(ee.props.children).every(function(Me){return!React.isValidElement(Me)||!ee.type||Me.type.isSelectOption?!0:($e=Me.type,!1)});return!Ee}return $e=lt,!0}),$e&&warning(!1,"`children` should be `Select.Option` or `Select.OptGroup` instead of `".concat($e.displayName||$e.name||$e,"`.")),warning(G===void 0,"`inputValue` is deprecated, please use `searchValue` instead.")}}function Mt(n,t){if(n){var c=function o(u){for(var m=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,v=0;v<u.length;v++){var h=u[v];if(h[t==null?void 0:t.value]===null)return warning(!1,"`value` in Select options should not be `null`."),!0;if(!m&&Array.isArray(h[t==null?void 0:t.options])&&o(h[t==null?void 0:t.options],!0))break}};c(n)}}var at=null,tt=["id","mode","prefixCls","backfill","fieldNames","inputValue","searchValue","onSearch","autoClearSearchValue","onSelect","onDeselect","dropdownMatchSelectWidth","filterOption","filterSort","optionFilterProp","optionLabelProp","options","optionRender","children","defaultActiveFirstOption","menuItemSelectedIcon","virtual","direction","listHeight","listItemHeight","labelRender","value","defaultValue","labelInValue","onChange","maxCount"],nn=["inputValue"];function In(n){return!n||(0,Et.Z)(n)!=="object"}var Cn=r.forwardRef(function(n,t){var c=n.id,o=n.mode,u=n.prefixCls,m=u===void 0?"rc-select":u,v=n.backfill,h=n.fieldNames,y=n.inputValue,E=n.searchValue,x=n.onSearch,B=n.autoClearSearchValue,Q=B===void 0?!0:B,S=n.onSelect,G=n.onDeselect,V=n.dropdownMatchSelectWidth,K=V===void 0?!0:V,oe=n.filterOption,je=n.filterSort,Oe=n.optionFilterProp,le=n.optionLabelProp,$e=n.options,ee=n.optionRender,xe=n.children,lt=n.defaultActiveFirstOption,Ee=n.menuItemSelectedIcon,Me=n.virtual,Ke=n.direction,Te=n.listHeight,_e=Te===void 0?200:Te,Ue=n.listItemHeight,Re=Ue===void 0?20:Ue,ze=n.labelRender,nt=n.value,Xe=n.defaultValue,Pe=n.labelInValue,ve=n.onChange,ae=n.maxCount,rt=(0,qe.Z)(n,tt),bt=(0,Ot.ZP)(c),Ct=j(o),Ve=!!(!$e&&xe),Jt=r.useMemo(function(){return oe===void 0&&o==="combobox"?!1:oe},[oe,o]),Qe=r.useMemo(function(){return U(h,Ve)},[JSON.stringify(h),Ve]),cn=(0,ke.Z)("",{value:E!==void 0?E:y,postState:function(k){return k||""}}),qt=(0,A.Z)(cn,2),Je=qt[0],q=qt[1],l=Qt($e,xe,Qe,Oe,le),d=l.valueOptions,w=l.labelOptions,b=l.options,H=r.useCallback(function(W){var k=f(W);return k.map(function(_){var X,be,He,Mn,yn;if(In(_))X=_;else{var Nn;He=_.key,be=_.label,X=(Nn=_.value)!==null&&Nn!==void 0?Nn:He}var yt=d.get(X);if(yt){var An;if(be===void 0&&(be=yt==null?void 0:yt[le||Qe.label]),He===void 0&&(He=(An=yt==null?void 0:yt.key)!==null&&An!==void 0?An:X),Mn=yt==null?void 0:yt.disabled,yn=yt==null?void 0:yt.title,0)var jn}return{label:be,value:X,key:He,disabled:Mn,title:yn}})},[Qe,le,d]),me=(0,ke.Z)(Xe,{value:nt}),Se=(0,A.Z)(me,2),he=Se[0],mt=Se[1],Bt=r.useMemo(function(){var W,k=Ct&&he===null?[]:he,_=H(k);return o==="combobox"&&et((W=_[0])===null||W===void 0?void 0:W.value)?[]:_},[he,H,o,Ct]),Wt=vt(Bt,d),On=(0,A.Z)(Wt,2),ot=On[0],Un=On[1],Hn=r.useMemo(function(){if(!o&&ot.length===1){var W=ot[0];if(W.value===null&&(W.label===null||W.label===void 0))return[]}return ot.map(function(k){var _;return(0,R.Z)((0,R.Z)({},k),{},{label:(_=typeof ze=="function"?ze(k):k.label)!==null&&_!==void 0?_:k.value})})},[o,ot,ze]),sn=r.useMemo(function(){return new Set(ot.map(function(W){return W.value}))},[ot]);r.useEffect(function(){if(o==="combobox"){var W,k=(W=ot[0])===null||W===void 0?void 0:W.value;q(Ae(k)?String(k):"")}},[ot]);var Lt=Fe(function(W,k){var _=k!=null?k:W;return(0,P.Z)((0,P.Z)({},Qe.value,W),Qe.label,_)}),kt=r.useMemo(function(){if(o!=="tags")return b;var W=(0,ue.Z)(b),k=function(X){return d.has(X)};return(0,ue.Z)(ot).sort(function(_,X){return _.value<X.value?-1:1}).forEach(function(_){var X=_.value;k(X)||W.push(Lt(X,_.label))}),W},[Lt,b,d,ot,o]),gt=$t(kt,Qe,Je,Jt,Oe),xn=r.useMemo(function(){return o!=="tags"||!Je||gt.some(function(W){return W[Oe||"value"]===Je})||gt.some(function(W){return W[Qe.value]===Je})?gt:[Lt(Je)].concat((0,ue.Z)(gt))},[Lt,Oe,o,gt,Je,Qe]),Wn=function W(k){var _=(0,ue.Z)(k).sort(function(X,be){return je(X,be,{searchValue:Je})});return _.map(function(X){return Array.isArray(X.options)?(0,R.Z)((0,R.Z)({},X),{},{options:X.options.length>0?W(X.options):X.options}):X})},Ln=r.useMemo(function(){return je?Wn(xn):xn},[xn,je,Je]),dn=r.useMemo(function(){return te(Ln,{fieldNames:Qe,childrenAsData:Ve})},[Ln,Qe,Ve]),rn=function(k){var _=H(k);if(mt(_),ve&&(_.length!==ot.length||_.some(function(He,Mn){var yn;return((yn=ot[Mn])===null||yn===void 0?void 0:yn.value)!==(He==null?void 0:He.value)}))){var X=Pe?_:_.map(function(He){return He.value}),be=_.map(function(He){return $(Un(He.value))});ve(Ct?X:X[0],Ct?be:be[0])}},Qn=r.useState(null),fn=(0,A.Z)(Qn,2),er=fn[0],en=fn[1],ur=r.useState(0),tr=(0,A.Z)(ur,2),nr=tr[0],cr=tr[1],rr=lt!==void 0?lt:o!=="combobox",or=r.useCallback(function(W,k){var _=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},X=_.source,be=X===void 0?"keyboard":X;cr(k),v&&o==="combobox"&&W!==null&&be==="keyboard"&&en(String(W))},[v,o]),Fn=function(k,_,X){var be=function(){var kn,Pn=Un(k);return[Pe?{label:Pn==null?void 0:Pn[Qe.label],value:k,key:(kn=Pn==null?void 0:Pn.key)!==null&&kn!==void 0?kn:k}:k,$(Pn)]};if(_&&S){var He=be(),Mn=(0,A.Z)(He,2),yn=Mn[0],Nn=Mn[1];S(yn,Nn)}else if(!_&&G&&X!=="clear"){var yt=be(),An=(0,A.Z)(yt,2),jn=An[0],sr=An[1];G(jn,sr)}},Jn=Fe(function(W,k){var _,X=Ct?k.selected:!0;X?_=Ct?[].concat((0,ue.Z)(ot),[W]):[W]:_=ot.filter(function(be){return be.value!==W}),rn(_),Fn(W,X),o==="combobox"?en(""):(!j||Q)&&(q(""),en(""))}),qn=function(k,_){rn(k);var X=_.type,be=_.values;(X==="remove"||X==="clear")&&be.forEach(function(He){Fn(He.value,!1,X)})},dt=function(k,_){if(q(k),en(null),_.source==="submit"){var X=(k||"").trim();if(X){var be=Array.from(new Set([].concat((0,ue.Z)(sn),[X])));rn(be),Fn(X,!0),q("")}return}_.source!=="blur"&&(o==="combobox"&&rn(k),x==null||x(k))},ar=function(k){var _=k;o!=="tags"&&(_=k.map(function(be){var He=w.get(be);return He==null?void 0:He.value}).filter(function(be){return be!==void 0}));var X=Array.from(new Set([].concat((0,ue.Z)(sn),(0,ue.Z)(_))));rn(X),X.forEach(function(be){Fn(be,!0)})},Xn=r.useMemo(function(){var W=Me!==!1&&K!==!1;return(0,R.Z)((0,R.Z)({},l),{},{flattenOptions:dn,onActiveValue:or,defaultActiveFirstOption:rr,onSelect:Jn,menuItemSelectedIcon:Ee,rawValues:sn,fieldNames:Qe,virtual:W,direction:Ke,listHeight:_e,listItemHeight:Re,childrenAsData:Ve,maxCount:ae,optionRender:ee})},[ae,l,dn,or,rr,Jn,Ee,sn,Qe,Me,K,Ke,_e,Re,Ve,ee]);return r.createElement(T.Provider,{value:Xn},r.createElement(Ce,(0,C.Z)({},rt,{id:bt,prefixCls:m,ref:t,omitDomProps:nn,mode:o,displayValues:Hn,onDisplayValuesChange:qn,direction:Ke,searchValue:Je,onSearch:dt,autoClearSearchValue:Q,onSearchSplit:ar,dropdownMatchSelectWidth:K,OptionList:St,emptyOptions:!dn.length,activeValue:er,activeDescendantId:"".concat(bt,"_list_").concat(nr)})))}),Rn=Cn;Rn.Option=ct,Rn.OptGroup=ut;var un=Rn,Bn=un},87718:function(Zn,pt,i){i.d(pt,{Z:function(){return Vn}});var C=i(87462),ue=i(71002),P=i(1413),R=i(4942),A=i(97685),qe=i(91),Et=i(93967),ke=i.n(Et),Kt=i(9220),r=i(56790),we=i(8410),s=i(67294),vn=i(73935),mn=s.forwardRef(function(a,D){var I=a.height,O=a.offsetY,z=a.offsetX,F=a.children,N=a.prefixCls,Z=a.onInnerResize,ne=a.innerProps,U=a.rtl,te=a.extra,$={},Y={display:"flex",flexDirection:"column"};return O!==void 0&&($={height:I,position:"relative",overflow:"hidden"},Y=(0,P.Z)((0,P.Z)({},Y),{},(0,R.Z)((0,R.Z)((0,R.Z)((0,R.Z)((0,R.Z)({transform:"translateY(".concat(O,"px)")},U?"marginRight":"marginLeft",-z),"position","absolute"),"left",0),"right",0),"top",0))),s.createElement("div",{style:$},s.createElement(Kt.Z,{onResize:function(T){var M=T.offsetHeight;M&&Z&&Z()}},s.createElement("div",(0,C.Z)({style:Y,className:ke()((0,R.Z)({},"".concat(N,"-holder-inner"),N)),ref:D},ne),F,te)))});mn.displayName="Filler";var on=mn;function gn(a){var D=a.children,I=a.setRef,O=s.useCallback(function(z){I(z)},[]);return s.cloneElement(D,{ref:O})}function _t(a,D,I,O,z,F,N,Z){var ne=Z.getKey;return a.slice(D,I+1).map(function(U,te){var $=D+te,Y=N(U,$,{style:{width:O},offsetX:z}),L=ne(U);return s.createElement(gn,{key:L,setRef:function(M){return F(U,M)}},Y)})}function pn(a,D,I,O){var z=I-a,F=D-I,N=Math.min(z,F)*2;if(O<=N){var Z=Math.floor(O/2);return O%2?I+Z+1:I-Z}return z>F?I-(O-F):I+(O-z)}function Ut(a,D,I){var O=a.length,z=D.length,F,N;if(O===0&&z===0)return null;O<z?(F=a,N=D):(F=D,N=a);var Z={__EMPTY_ITEM__:!0};function ne(T){return T!==void 0?I(T):Z}for(var U=null,te=Math.abs(O-z)!==1,$=0;$<N.length;$+=1){var Y=ne(F[$]),L=ne(N[$]);if(Y!==L){U=$,te=te||Y!==ne(N[$+1]);break}}return U===null?null:{index:U,multiple:te}}function hn(a,D,I){var O=s.useState(a),z=(0,A.Z)(O,2),F=z[0],N=z[1],Z=s.useState(null),ne=(0,A.Z)(Z,2),U=ne[0],te=ne[1];return s.useEffect(function(){var $=Ut(F||[],a||[],D);($==null?void 0:$.index)!==void 0&&(I==null||I($.index),te(a[$.index])),N(a)},[a]),[U]}var J=i(75164),ge=(typeof navigator=="undefined"?"undefined":(0,ue.Z)(navigator))==="object"&&/Firefox/i.test(navigator.userAgent),Be=ge,p=function(a,D,I,O){var z=(0,s.useRef)(!1),F=(0,s.useRef)(null);function N(){clearTimeout(F.current),z.current=!0,F.current=setTimeout(function(){z.current=!1},50)}var Z=(0,s.useRef)({top:a,bottom:D,left:I,right:O});return Z.current.top=a,Z.current.bottom=D,Z.current.left=I,Z.current.right=O,function(ne,U){var te=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,$=ne?U<0&&Z.current.left||U>0&&Z.current.right:U<0&&Z.current.top||U>0&&Z.current.bottom;return te&&$?(clearTimeout(F.current),z.current=!1):(!$||z.current)&&N(),!z.current&&$}};function Le(a,D,I,O,z,F,N){var Z=(0,s.useRef)(0),ne=(0,s.useRef)(null),U=(0,s.useRef)(null),te=(0,s.useRef)(!1),$=p(D,I,O,z);function Y(j,fe){if(J.Z.cancel(ne.current),!$(!1,fe)){var Ce=j;if(!Ce._virtualHandled)Ce._virtualHandled=!0;else return;Z.current+=fe,U.current=fe,Be||Ce.preventDefault(),ne.current=(0,J.Z)(function(){var it=te.current?10:1;N(Z.current*it,!1),Z.current=0})}}function L(j,fe){N(fe,!0),Be||j.preventDefault()}var T=(0,s.useRef)(null),M=(0,s.useRef)(null);function ie(j){if(a){J.Z.cancel(M.current),M.current=(0,J.Z)(function(){T.current=null},2);var fe=j.deltaX,Ce=j.deltaY,it=j.shiftKey,ut=fe,ye=Ce;(T.current==="sx"||!T.current&&it&&Ce&&!fe)&&(ut=Ce,ye=0,T.current="sx");var ct=Math.abs(ut),wt=Math.abs(ye);T.current===null&&(T.current=F&&ct>wt?"x":"y"),T.current==="y"?Y(j,ye):L(j,ut)}}function de(j){a&&(te.current=j.detail===U.current)}return[ie,de]}function Ne(a,D,I,O){var z=s.useMemo(function(){return[new Map,[]]},[a,I.id,O]),F=(0,A.Z)(z,2),N=F[0],Z=F[1],ne=function(te){var $=arguments.length>1&&arguments[1]!==void 0?arguments[1]:te,Y=N.get(te),L=N.get($);if(Y===void 0||L===void 0)for(var T=a.length,M=Z.length;M<T;M+=1){var ie,de=a[M],j=D(de);N.set(j,M);var fe=(ie=I.get(j))!==null&&ie!==void 0?ie:O;if(Z[M]=(Z[M-1]||0)+fe,j===te&&(Y=M),j===$&&(L=M),Y!==void 0&&L!==void 0)break}return{top:Z[Y-1]||0,bottom:Z[L]}};return ne}var Nt=i(15671),We=i(43144),At=function(){function a(){(0,Nt.Z)(this,a),(0,R.Z)(this,"maps",void 0),(0,R.Z)(this,"id",0),(0,R.Z)(this,"diffRecords",new Map),this.maps=Object.create(null)}return(0,We.Z)(a,[{key:"set",value:function(I,O){this.diffRecords.set(I,this.maps[I]),this.maps[I]=O,this.id+=1}},{key:"get",value:function(I){return this.maps[I]}},{key:"resetRecord",value:function(){this.diffRecords.clear()}},{key:"getRecord",value:function(){return this.diffRecords}}]),a}(),De=At;function ce(a){var D=parseFloat(a);return isNaN(D)?0:D}function e(a,D,I){var O=s.useState(0),z=(0,A.Z)(O,2),F=z[0],N=z[1],Z=(0,s.useRef)(new Map),ne=(0,s.useRef)(new De),U=(0,s.useRef)(0);function te(){U.current+=1}function $(){var L=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;te();var T=function(){var de=!1;Z.current.forEach(function(j,fe){if(j&&j.offsetParent){var Ce=j.offsetHeight,it=getComputedStyle(j),ut=it.marginTop,ye=it.marginBottom,ct=ce(ut),wt=ce(ye),Zt=Ce+ct+wt;ne.current.get(fe)!==Zt&&(ne.current.set(fe,Zt),de=!0)}}),de&&N(function(j){return j+1})};if(L)T();else{U.current+=1;var M=U.current;Promise.resolve().then(function(){M===U.current&&T()})}}function Y(L,T){var M=a(L),ie=Z.current.get(M);T?(Z.current.set(M,T),$()):Z.current.delete(M),!ie!=!T&&(T?D==null||D(L):I==null||I(L))}return(0,s.useEffect)(function(){return te},[]),[Y,$,ne.current,F]}var g=14/15;function f(a,D,I){var O=(0,s.useRef)(!1),z=(0,s.useRef)(0),F=(0,s.useRef)(0),N=(0,s.useRef)(null),Z=(0,s.useRef)(null),ne,U=function(L){if(O.current){var T=Math.ceil(L.touches[0].pageX),M=Math.ceil(L.touches[0].pageY),ie=z.current-T,de=F.current-M,j=Math.abs(ie)>Math.abs(de);j?z.current=T:F.current=M;var fe=I(j,j?ie:de,!1,L);fe&&L.preventDefault(),clearInterval(Z.current),fe&&(Z.current=setInterval(function(){j?ie*=g:de*=g;var Ce=Math.floor(j?ie:de);(!I(j,Ce,!0)||Math.abs(Ce)<=.1)&&clearInterval(Z.current)},16))}},te=function(){O.current=!1,ne()},$=function(L){ne(),L.touches.length===1&&!O.current&&(O.current=!0,z.current=Math.ceil(L.touches[0].pageX),F.current=Math.ceil(L.touches[0].pageY),N.current=L.target,N.current.addEventListener("touchmove",U,{passive:!1}),N.current.addEventListener("touchend",te,{passive:!0}))};ne=function(){N.current&&(N.current.removeEventListener("touchmove",U),N.current.removeEventListener("touchend",te))},(0,we.Z)(function(){return a&&D.current.addEventListener("touchstart",$,{passive:!0}),function(){var Y;(Y=D.current)===null||Y===void 0||Y.removeEventListener("touchstart",$),ne(),clearInterval(Z.current)}},[a])}function pe(a){return Math.floor(Math.pow(a,.5))}function Ze(a,D){var I="touches"in a?a.touches[0]:a;return I[D?"pageX":"pageY"]-window[D?"scrollX":"scrollY"]}function Ae(a,D,I){s.useEffect(function(){var O=D.current;if(a&&O){var z=!1,F,N,Z=function(){J.Z.cancel(F)},ne=function Y(){Z(),F=(0,J.Z)(function(){I(N),Y()})},U=function(L){if(!(L.target.draggable||L.button!==0)){var T=L;T._virtualHandled||(T._virtualHandled=!0,z=!0)}},te=function(){z=!1,Z()},$=function(L){if(z){var T=Ze(L,!1),M=O.getBoundingClientRect(),ie=M.top,de=M.bottom;if(T<=ie){var j=ie-T;N=-pe(j),ne()}else if(T>=de){var fe=T-de;N=pe(fe),ne()}else Z()}};return O.addEventListener("mousedown",U),O.ownerDocument.addEventListener("mouseup",te),O.ownerDocument.addEventListener("mousemove",$),function(){O.removeEventListener("mousedown",U),O.ownerDocument.removeEventListener("mouseup",te),O.ownerDocument.removeEventListener("mousemove",$),Z()}}},[a])}var et=10;function Ie(a,D,I,O,z,F,N,Z){var ne=s.useRef(),U=s.useState(null),te=(0,A.Z)(U,2),$=te[0],Y=te[1];return(0,we.Z)(function(){if($&&$.times<et){if(!a.current){Y(function(Xt){return(0,P.Z)({},Xt)});return}F();var L=$.targetAlign,T=$.originAlign,M=$.index,ie=$.offset,de=a.current.clientHeight,j=!1,fe=L,Ce=null;if(de){for(var it=L||T,ut=0,ye=0,ct=0,wt=Math.min(D.length-1,M),Zt=0;Zt<=wt;Zt+=1){var ht=z(D[Zt]);ye=ut;var It=I.get(ht);ct=ye+(It===void 0?O:It),ut=ct}for(var Rt=it==="top"?ie:de-ie,tn=wt;tn>=0;tn-=1){var Ge=z(D[tn]),st=I.get(Ge);if(st===void 0){j=!0;break}if(Rt-=st,Rt<=0)break}switch(it){case"top":Ce=ye-ie;break;case"bottom":Ce=ct-de+ie;break;default:{var St=a.current.scrollTop,vt=St+de;ye<St?fe="top":ct>vt&&(fe="bottom")}}Ce!==null&&N(Ce),Ce!==$.lastTop&&(j=!0)}j&&Y((0,P.Z)((0,P.Z)({},$),{},{times:$.times+1,targetAlign:fe,lastTop:Ce}))}},[$,a.current]),function(L){if(L==null){Z();return}if(J.Z.cancel(ne.current),typeof L=="number")N(L);else if(L&&(0,ue.Z)(L)==="object"){var T,M=L.align;"index"in L?T=L.index:T=D.findIndex(function(j){return z(j)===L.key});var ie=L.offset,de=ie===void 0?0:ie;Y({times:0,index:T,offset:de,originAlign:M})}}}var zt=s.forwardRef(function(a,D){var I=a.prefixCls,O=a.rtl,z=a.scrollOffset,F=a.scrollRange,N=a.onStartMove,Z=a.onStopMove,ne=a.onScroll,U=a.horizontal,te=a.spinSize,$=a.containerSize,Y=a.style,L=a.thumbStyle,T=a.showScrollBar,M=s.useState(!1),ie=(0,A.Z)(M,2),de=ie[0],j=ie[1],fe=s.useState(null),Ce=(0,A.Z)(fe,2),it=Ce[0],ut=Ce[1],ye=s.useState(null),ct=(0,A.Z)(ye,2),wt=ct[0],Zt=ct[1],ht=!O,It=s.useRef(),Rt=s.useRef(),tn=s.useState(T),Ge=(0,A.Z)(tn,2),st=Ge[0],St=Ge[1],vt=s.useRef(),Xt=function(){T===!0||T===!1||(clearTimeout(vt.current),St(!0),vt.current=setTimeout(function(){St(!1)},3e3))},$t=F-$||0,Ot=$-te||0,Gt=s.useMemo(function(){if(z===0||$t===0)return 0;var Mt=z/$t;return Mt*Ot},[z,$t,Ot]),Ye=function(at){at.stopPropagation(),at.preventDefault()},Tt=s.useRef({top:Gt,dragging:de,pageY:it,startTop:wt});Tt.current={top:Gt,dragging:de,pageY:it,startTop:wt};var ln=function(at){j(!0),ut(Ze(at,U)),Zt(Tt.current.top),N(),at.stopPropagation(),at.preventDefault()};s.useEffect(function(){var Mt=function(In){In.preventDefault()},at=It.current,tt=Rt.current;return at.addEventListener("touchstart",Mt,{passive:!1}),tt.addEventListener("touchstart",ln,{passive:!1}),function(){at.removeEventListener("touchstart",Mt),tt.removeEventListener("touchstart",ln)}},[]);var Yt=s.useRef();Yt.current=$t;var bn=s.useRef();bn.current=Ot,s.useEffect(function(){if(de){var Mt,at=function(In){var Cn=Tt.current,Rn=Cn.dragging,un=Cn.pageY,Bn=Cn.startTop;J.Z.cancel(Mt);var n=It.current.getBoundingClientRect(),t=$/(U?n.width:n.height);if(Rn){var c=(Ze(In,U)-un)*t,o=Bn;!ht&&U?o-=c:o+=c;var u=Yt.current,m=bn.current,v=m?o/m:0,h=Math.ceil(v*u);h=Math.max(h,0),h=Math.min(h,u),Mt=(0,J.Z)(function(){ne(h,U)})}},tt=function(){j(!1),Z()};return window.addEventListener("mousemove",at,{passive:!0}),window.addEventListener("touchmove",at,{passive:!0}),window.addEventListener("mouseup",tt,{passive:!0}),window.addEventListener("touchend",tt,{passive:!0}),function(){window.removeEventListener("mousemove",at),window.removeEventListener("touchmove",at),window.removeEventListener("mouseup",tt),window.removeEventListener("touchend",tt),J.Z.cancel(Mt)}}},[de]),s.useEffect(function(){return Xt(),function(){clearTimeout(vt.current)}},[z]),s.useImperativeHandle(D,function(){return{delayHidden:Xt}});var Qt="".concat(I,"-scrollbar"),Fe={position:"absolute",visibility:st?null:"hidden"},xt={position:"absolute",background:"rgba(0, 0, 0, 0.5)",borderRadius:99,cursor:"pointer",userSelect:"none"};return U?(Fe.height=8,Fe.left=0,Fe.right=0,Fe.bottom=0,xt.height="100%",xt.width=te,ht?xt.left=Gt:xt.right=Gt):(Fe.width=8,Fe.top=0,Fe.bottom=0,ht?Fe.right=0:Fe.left=0,xt.width="100%",xt.height=te,xt.top=Gt),s.createElement("div",{ref:It,className:ke()(Qt,(0,R.Z)((0,R.Z)((0,R.Z)({},"".concat(Qt,"-horizontal"),U),"".concat(Qt,"-vertical"),!U),"".concat(Qt,"-visible"),st)),style:(0,P.Z)((0,P.Z)({},Fe),Y),onMouseDown:Ye,onMouseMove:Xt},s.createElement("div",{ref:Rt,className:ke()("".concat(Qt,"-thumb"),(0,R.Z)({},"".concat(Qt,"-thumb-moving"),de)),style:(0,P.Z)((0,P.Z)({},xt),L),onMouseDown:ln}))}),Sn=zt,wn=20;function Dt(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,D=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,I=a/D*a;return isNaN(I)&&(I=0),I=Math.max(I,wn),Math.floor(I)}var Vt=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles","showScrollBar"],an=[],Ht={overflowY:"auto",overflowAnchor:"none"};function $n(a,D){var I=a.prefixCls,O=I===void 0?"rc-virtual-list":I,z=a.className,F=a.height,N=a.itemHeight,Z=a.fullHeight,ne=Z===void 0?!0:Z,U=a.style,te=a.data,$=a.children,Y=a.itemKey,L=a.virtual,T=a.direction,M=a.scrollWidth,ie=a.component,de=ie===void 0?"div":ie,j=a.onScroll,fe=a.onVirtualScroll,Ce=a.onVisibleChange,it=a.innerProps,ut=a.extraRender,ye=a.styles,ct=a.showScrollBar,wt=ct===void 0?"optional":ct,Zt=(0,qe.Z)(a,Vt),ht=s.useCallback(function(l){return typeof Y=="function"?Y(l):l==null?void 0:l[Y]},[Y]),It=e(ht,null,null),Rt=(0,A.Z)(It,4),tn=Rt[0],Ge=Rt[1],st=Rt[2],St=Rt[3],vt=!!(L!==!1&&F&&N),Xt=s.useMemo(function(){return Object.values(st.maps).reduce(function(l,d){return l+d},0)},[st.id,st.maps]),$t=vt&&te&&(Math.max(N*te.length,Xt)>F||!!M),Ot=T==="rtl",Gt=ke()(O,(0,R.Z)({},"".concat(O,"-rtl"),Ot),z),Ye=te||an,Tt=(0,s.useRef)(),ln=(0,s.useRef)(),Yt=(0,s.useRef)(),bn=(0,s.useState)(0),Qt=(0,A.Z)(bn,2),Fe=Qt[0],xt=Qt[1],Mt=(0,s.useState)(0),at=(0,A.Z)(Mt,2),tt=at[0],nn=at[1],In=(0,s.useState)(!1),Cn=(0,A.Z)(In,2),Rn=Cn[0],un=Cn[1],Bn=function(){un(!0)},n=function(){un(!1)},t={getKey:ht};function c(l){xt(function(d){var w;typeof l=="function"?w=l(d):w=l;var b=lt(w);return Tt.current.scrollTop=b,b})}var o=(0,s.useRef)({start:0,end:Ye.length}),u=(0,s.useRef)(),m=hn(Ye,ht),v=(0,A.Z)(m,1),h=v[0];u.current=h;var y=s.useMemo(function(){if(!vt)return{scrollHeight:void 0,start:0,end:Ye.length-1,offset:void 0};if(!$t){var l;return{scrollHeight:((l=ln.current)===null||l===void 0?void 0:l.offsetHeight)||0,start:0,end:Ye.length-1,offset:void 0}}for(var d=0,w,b,H,me=Ye.length,Se=0;Se<me;Se+=1){var he=Ye[Se],mt=ht(he),Bt=st.get(mt),Wt=d+(Bt===void 0?N:Bt);Wt>=Fe&&w===void 0&&(w=Se,b=d),Wt>Fe+F&&H===void 0&&(H=Se),d=Wt}return w===void 0&&(w=0,b=0,H=Math.ceil(F/N)),H===void 0&&(H=Ye.length-1),H=Math.min(H+1,Ye.length-1),{scrollHeight:d,start:w,end:H,offset:b}},[$t,vt,Fe,Ye,St,F]),E=y.scrollHeight,x=y.start,B=y.end,Q=y.offset;o.current.start=x,o.current.end=B,s.useLayoutEffect(function(){var l=st.getRecord();if(l.size===1){var d=Array.from(l.keys())[0],w=l.get(d),b=Ye[x];if(b&&w===void 0){var H=ht(b);if(H===d){var me=st.get(d),Se=me-N;c(function(he){return he+Se})}}}st.resetRecord()},[E]);var S=s.useState({width:0,height:F}),G=(0,A.Z)(S,2),V=G[0],K=G[1],oe=function(d){K({width:d.offsetWidth,height:d.offsetHeight})},je=(0,s.useRef)(),Oe=(0,s.useRef)(),le=s.useMemo(function(){return Dt(V.width,M)},[V.width,M]),$e=s.useMemo(function(){return Dt(V.height,E)},[V.height,E]),ee=E-F,xe=(0,s.useRef)(ee);xe.current=ee;function lt(l){var d=l;return Number.isNaN(xe.current)||(d=Math.min(d,xe.current)),d=Math.max(d,0),d}var Ee=Fe<=0,Me=Fe>=ee,Ke=tt<=0,Te=tt>=M,_e=p(Ee,Me,Ke,Te),Ue=function(){return{x:Ot?-tt:tt,y:Fe}},Re=(0,s.useRef)(Ue()),ze=(0,r.zX)(function(l){if(fe){var d=(0,P.Z)((0,P.Z)({},Ue()),l);(Re.current.x!==d.x||Re.current.y!==d.y)&&(fe(d),Re.current=d)}});function nt(l,d){var w=l;d?((0,vn.flushSync)(function(){nn(w)}),ze()):c(w)}function Xe(l){var d=l.currentTarget.scrollTop;d!==Fe&&c(d),j==null||j(l),ze()}var Pe=function(d){var w=d,b=M?M-V.width:0;return w=Math.max(w,0),w=Math.min(w,b),w},ve=(0,r.zX)(function(l,d){d?((0,vn.flushSync)(function(){nn(function(w){var b=w+(Ot?-l:l);return Pe(b)})}),ze()):c(function(w){var b=w+l;return b})}),ae=Le(vt,Ee,Me,Ke,Te,!!M,ve),rt=(0,A.Z)(ae,2),bt=rt[0],Ct=rt[1];f(vt,Tt,function(l,d,w,b){var H=b;return _e(l,d,w)?!1:!H||!H._virtualHandled?(H&&(H._virtualHandled=!0),bt({preventDefault:function(){},deltaX:l?d:0,deltaY:l?0:d}),!0):!1}),Ae($t,Tt,function(l){c(function(d){return d+l})}),(0,we.Z)(function(){function l(w){var b=Ee&&w.detail<0,H=Me&&w.detail>0;vt&&!b&&!H&&w.preventDefault()}var d=Tt.current;return d.addEventListener("wheel",bt,{passive:!1}),d.addEventListener("DOMMouseScroll",Ct,{passive:!0}),d.addEventListener("MozMousePixelScroll",l,{passive:!1}),function(){d.removeEventListener("wheel",bt),d.removeEventListener("DOMMouseScroll",Ct),d.removeEventListener("MozMousePixelScroll",l)}},[vt,Ee,Me]),(0,we.Z)(function(){if(M){var l=Pe(tt);nn(l),ze({x:l})}},[V.width,M]);var Ve=function(){var d,w;(d=je.current)===null||d===void 0||d.delayHidden(),(w=Oe.current)===null||w===void 0||w.delayHidden()},Jt=Ie(Tt,Ye,st,N,ht,function(){return Ge(!0)},c,Ve);s.useImperativeHandle(D,function(){return{nativeElement:Yt.current,getScrollInfo:Ue,scrollTo:function(d){function w(b){return b&&(0,ue.Z)(b)==="object"&&("left"in b||"top"in b)}w(d)?(d.left!==void 0&&nn(Pe(d.left)),Jt(d.top)):Jt(d)}}}),(0,we.Z)(function(){if(Ce){var l=Ye.slice(x,B+1);Ce(l,Ye)}},[x,B,Ye]);var Qe=Ne(Ye,ht,st,N),cn=ut==null?void 0:ut({start:x,end:B,virtual:$t,offsetX:tt,offsetY:Q,rtl:Ot,getSize:Qe}),qt=_t(Ye,x,B,M,tt,tn,$,t),Je=null;F&&(Je=(0,P.Z)((0,R.Z)({},ne?"height":"maxHeight",F),Ht),vt&&(Je.overflowY="hidden",M&&(Je.overflowX="hidden"),Rn&&(Je.pointerEvents="none")));var q={};return Ot&&(q.dir="rtl"),s.createElement("div",(0,C.Z)({ref:Yt,style:(0,P.Z)((0,P.Z)({},U),{},{position:"relative"}),className:Gt},q,Zt),s.createElement(Kt.Z,{onResize:oe},s.createElement(de,{className:"".concat(O,"-holder"),style:Je,ref:Tt,onScroll:Xe,onMouseEnter:Ve},s.createElement(on,{prefixCls:O,height:E,offsetX:tt,offsetY:Q,scrollWidth:M,onInnerResize:Ge,ref:ln,innerProps:it,rtl:Ot,extra:cn},qt))),$t&&E>F&&s.createElement(Sn,{ref:je,prefixCls:O,scrollOffset:Fe,scrollRange:E,rtl:Ot,onScroll:nt,onStartMove:Bn,onStopMove:n,spinSize:$e,containerSize:V.height,style:ye==null?void 0:ye.verticalScrollBar,thumbStyle:ye==null?void 0:ye.verticalScrollBarThumb,showScrollBar:wt}),$t&&M>V.width&&s.createElement(Sn,{ref:Oe,prefixCls:O,scrollOffset:tt,scrollRange:M,rtl:Ot,onScroll:nt,onStartMove:Bn,onStopMove:n,spinSize:le,containerSize:V.width,horizontal:!0,style:ye==null?void 0:ye.horizontalScrollBar,thumbStyle:ye==null?void 0:ye.horizontalScrollBarThumb,showScrollBar:wt}))}var Tn=s.forwardRef($n);Tn.displayName="List";var zn=Tn,Vn=zn}}]);
