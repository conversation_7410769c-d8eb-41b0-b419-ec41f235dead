// Import crypto polyfill first
import '@/utils/crypto-polyfill';

import { Footer, Theme, SelectLang, AvatarDropdown, AvatarName } from '@/components';
import { LinkOutlined } from '@ant-design/icons';
import type { Settings as LayoutSettings, MenuDataItem } from '@ant-design/pro-components';
import { SettingDrawer } from '@ant-design/pro-components';
import type { RuntimeConfig, RunTimeLayoutConfig } from '@umijs/max';
import { history, Link } from '@umijs/max';
import defaultSettings from '../config/defaultSettings';
import { errorConfig } from './requestErrorConfig';
import { currentUser as queryCurrentUser } from '@/services/ant-design-pro/api';
import React from 'react';
import { getTheme, updateDefaultSettings } from '@/components/RightContent/themeSwitcher';
import { keycloakService } from '@/services/keycloak';
const isDev = process.env.NODE_ENV === 'development';
const loginPath = '/user/login';

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: API.CurrentUser;
  loading?: boolean;
  fetchUserInfo?: () => Promise<API.CurrentUser | undefined>;
}> {
  const fetchUserInfo = async () => {
    try {
      // 使用 Keycloak 认证
      const keycloakAuthenticated = await keycloakService.init();
      if (keycloakAuthenticated) {
        const userInfo = keycloakService.getUserInfo();
        return {
          userid: userInfo.id,
          name: userInfo.name,
          username: userInfo.username,
          email: userInfo.email,
          avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(userInfo.name || userInfo.username)}&background=1890ff&color=fff`,
        };
      }
      return undefined;
    } catch (error) {
      console.log('认证失败:', error);
      return undefined;
    }
  };

  // 如果不是登录页面和认证回调页面，执行
  const { location } = history;
  if (location.pathname !== loginPath && location.pathname !== '/auth/callback') {
    const currentUser = await fetchUserInfo();

    // 初始化主题配置
    const theme = getTheme();
    if (theme) {
      updateDefaultSettings(theme);
    }

    return {
      fetchUserInfo,
      currentUser,
      settings: { ...defaultSettings } as Partial<LayoutSettings>,
    };
  }
  return {
    fetchUserInfo,
    settings: { ...defaultSettings } as Partial<LayoutSettings>,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  return {
    actionsRender: () => [<Theme key="doc" />, <SelectLang key="SelectLang" />],
    avatarProps: {
      src: initialState?.currentUser?.avatar,
      title: <AvatarName />,
      render: (_, avatarChildren) => {
        return <AvatarDropdown>{avatarChildren}</AvatarDropdown>;
      },
    },
    // siderWidth:300,
    logo:require('../src/assets/img/logo/CMCC/xhlogo.png'),
    waterMarkProps: {
      // content: initialState?.currentUser?.name,
    },
    // footerRender: () => <Footer />,
    onPageChange: () => {
      const { location } = history;
      // 如果没有登录，重定向到 login
      if (!initialState?.currentUser && location.pathname !== loginPath) {
        history.push(loginPath);
      }
    },
    // bgLayoutImgList: [
    //   {
    //     src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr',
    //     left: 85,
    //     bottom: 100,
    //     height: '303px',
    //   },
    //   {
    //     src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr',
    //     bottom: -68,
    //     right: -45,
    //     height: '303px',
    //   },
    //   {
    //     src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr',
    //     bottom: 0,
    //     left: 0,
    //     width: '331px',
    //   },
    // ],
    links: isDev
      ? [
        // <Link key="openapi" to="/umi/plugin/openapi" target="_blank">
          //   <LinkOutlined />
          //   <span>OpenAPI 文档</span>
          // </Link>,
        ]
      : [],
    menuHeaderRender: undefined,
    // menuRender:(props: { menuData: MenuDataItem[] })=>{
    //   return props.menuData.map((item, index) => {
    //     const isLastItem = index === props.menuData.length - 1;
    //     return (
    //       <React.Fragment key={item.path}>
    //         <Menu.Item key={item.path}>
    //           <Link to={item.path}>{item.name}</Link>
    //         </Menu.Item>
    //         {!isLastItem && (
    //           <div
    //             style={{
    //               height: '100%',
    //               borderRight: '1px solid #e8e8e8', // 分割线样式
    //               margin: '0 8px', // 调整分割线与菜单项的间距
    //             }}
    //           ></div>
    //         )}
    //       </React.Fragment>
    //     );
    //   });
    // },
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    // 增加一个 loading 的状态
    childrenRender: (children) => {
      // if (initialState?.loading) return <PageLoading />;
      return (
        <>
          {children}
          {/* {props?.location.pathname === '/demo/demo2' ? <SiderDemo {...props}/> : children} */}
          {isDev
          // &&( <SettingDrawer
          //     disableUrlParams
          //     enableDarkTheme
          //     settings={initialState?.settings}
          //     onSettingChange={(settings) => {
          //       setInitialState((preInitialState) => ({
          //         ...preInitialState,
          //         settings,
          //       }));
          //     }}
          //   />
          // )
          }
        </>
      );
    },
    ...initialState?.settings,
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request = {
  ...errorConfig,
};

