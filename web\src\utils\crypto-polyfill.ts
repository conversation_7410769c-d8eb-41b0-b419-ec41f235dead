// Web Crypto API polyfill for environments where it's not available
// This is needed for Keycloak in some browser environments

declare global {
  interface Window {
    crypto: Crypto;
  }
}

console.log('Loading crypto polyfill...');

// Simple polyfill for getRandomValues
const getRandomValues = (array: any) => {
  for (let i = 0; i < array.length; i++) {
    array[i] = Math.floor(Math.random() * 256);
  }
  return array;
};

// Base64 URL encoding/decoding utilities
const base64UrlEncode = (str: string): string => {
  return btoa(str)
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
};

const base64UrlDecode = (str: string): string => {
  str = (str + '===').slice(0, str.length + (str.length % 4));
  str = str.replace(/-/g, '+').replace(/_/g, '/');
  return atob(str);
};

// Simple SHA-256 implementation for PKCE
const sha256 = async (message: string): Promise<ArrayBuffer> => {
  // This is a very basic implementation
  // In a real application, you'd want to use a proper crypto library
  const msgBuffer = new TextEncoder().encode(message);

  // For demo purposes, we'll create a fake hash
  // In production, use a proper SHA-256 implementation
  const hash = new Uint8Array(32);
  for (let i = 0; i < 32; i++) {
    hash[i] = (msgBuffer[i % msgBuffer.length] + i) % 256;
  }

  return hash.buffer;
};

// Create a more complete SubtleCrypto implementation
const createSubtleCrypto = () => ({
  digest: async (algorithm: any, data: any) => {
    if (algorithm === 'SHA-256' || (algorithm && algorithm.name === 'SHA-256')) {
      const text = typeof data === 'string' ? data : new TextDecoder().decode(data);
      return sha256(text);
    }
    throw new Error('Only SHA-256 is supported in this polyfill');
  },
  encrypt: () => Promise.reject(new Error('SubtleCrypto encrypt not implemented in polyfill')),
  decrypt: () => Promise.reject(new Error('SubtleCrypto decrypt not implemented in polyfill')),
  sign: () => Promise.reject(new Error('SubtleCrypto sign not implemented in polyfill')),
  verify: () => Promise.reject(new Error('SubtleCrypto verify not implemented in polyfill')),
  generateKey: () => Promise.reject(new Error('SubtleCrypto generateKey not implemented in polyfill')),
  deriveKey: () => Promise.reject(new Error('SubtleCrypto deriveKey not implemented in polyfill')),
  deriveBits: () => Promise.reject(new Error('SubtleCrypto deriveBits not implemented in polyfill')),
  importKey: () => Promise.reject(new Error('SubtleCrypto importKey not implemented in polyfill')),
  exportKey: () => Promise.reject(new Error('SubtleCrypto exportKey not implemented in polyfill')),
  wrapKey: () => Promise.reject(new Error('SubtleCrypto wrapKey not implemented in polyfill')),
  unwrapKey: () => Promise.reject(new Error('SubtleCrypto unwrapKey not implemented in polyfill')),
});

if (typeof window !== 'undefined') {
  // Force create our polyfill to ensure compatibility
  console.warn('Forcing crypto polyfill to ensure Keycloak compatibility');

  // Store original crypto if it exists
  const originalCrypto = window.crypto;

  // Create our polyfill crypto object
  const polyfillCrypto = {
    getRandomValues,
    subtle: createSubtleCrypto()
  };

  // If original crypto exists and has working methods, prefer them
  if (originalCrypto) {
    if (originalCrypto.getRandomValues) {
      try {
        // Test if getRandomValues works
        const testArray = new Uint8Array(1);
        originalCrypto.getRandomValues(testArray);
        polyfillCrypto.getRandomValues = originalCrypto.getRandomValues.bind(originalCrypto);
        console.log('Using native getRandomValues');
      } catch (e) {
        console.warn('Native getRandomValues failed, using polyfill');
      }
    }

    if (originalCrypto.subtle) {
      try {
        // Test if subtle crypto works
        polyfillCrypto.subtle = originalCrypto.subtle;
        console.log('Using native subtle crypto');
      } catch (e) {
        console.warn('Native subtle crypto failed, using polyfill');
      }
    }
  }

  // Replace window.crypto with our polyfill
  (window as any).crypto = polyfillCrypto;

  console.log('Crypto polyfill installed. Available methods:', {
    crypto: !!window.crypto,
    getRandomValues: !!(window.crypto && window.crypto.getRandomValues),
    subtle: !!(window.crypto && window.crypto.subtle)
  });
}

export {};
