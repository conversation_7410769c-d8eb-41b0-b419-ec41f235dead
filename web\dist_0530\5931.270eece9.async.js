"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5931],{8751:function(T,u,t){t.d(u,{Z:function(){return m}});var n=t(1413),o=t(67294),f={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"},i=f,g=t(91146),v=function(h,r){return o.createElement(g.Z,(0,n.Z)((0,n.Z)({},h),{},{ref:r,icon:i}))},C=o.forwardRef(v),m=C},18429:function(T,u,t){t.d(u,{Z:function(){return m}});var n=t(1413),o=t(67294),f={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"}}]},name:"close-circle",theme:"outlined"},i=f,g=t(91146),v=function(h,r){return o.createElement(g.Z,(0,n.Z)((0,n.Z)({},h),{},{ref:r,icon:i}))},C=o.forwardRef(v),m=C},71255:function(T,u,t){t.d(u,{Z:function(){return m}});var n=t(1413),o=t(67294),f={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zm-280 0c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z"}},{tag:"path",attrs:{d:"M894 345a343.92 343.92 0 00-189-130v.1c-17.1-19-36.4-36.5-58-52.1-163.7-119-393.5-82.7-513 81-96.3 133-92.2 311.9 6 439l.8 132.6c0 3.2.5 6.4 1.5 9.4a31.95 31.95 0 0040.1 20.9L309 806c33.5 11.9 68.1 18.7 102.5 20.6l-.5.4c89.1 64.9 205.9 84.4 313 49l127.1 41.4c3.2 1 6.5 1.6 9.9 1.6 17.7 0 32-14.3 32-32V753c88.1-119.6 90.4-284.9 1-408zM323 735l-12-5-99 31-1-104-8-9c-84.6-103.2-90.2-251.9-11-361 96.4-132.2 281.2-161.4 413-66 132.2 96.1 161.5 280.6 66 412-80.1 109.9-223.5 150.5-348 102zm505-17l-8 10 1 104-98-33-12 5c-56 20.8-115.7 22.5-171 7l-.2-.1A367.31 367.31 0 00729 676c76.4-105.3 88.8-237.6 44.4-350.4l.6.4c23 16.5 44.1 37.1 62 62 72.6 99.6 68.5 235.2-8 330z"}},{tag:"path",attrs:{d:"M433 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z"}}]},name:"comment",theme:"outlined"},i=f,g=t(91146),v=function(h,r){return o.createElement(g.Z,(0,n.Z)((0,n.Z)({},h),{},{ref:r,icon:i}))},C=o.forwardRef(v),m=C},15360:function(T,u,t){t.d(u,{Z:function(){return m}});var n=t(1413),o=t(67294),f={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM504 618H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM312 490v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8z"}}]},name:"file-text",theme:"outlined"},i=f,g=t(91146),v=function(h,r){return o.createElement(g.Z,(0,n.Z)((0,n.Z)({},h),{},{ref:r,icon:i}))},C=o.forwardRef(v),m=C},48118:function(T,u,t){t.d(u,{Z:function(){return m}});var n=t(1413),o=t(67294),f={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm72-112c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48zm400-188h-59.3c-2.6 0-5 1.2-6.5 3.3L763.7 538.1l-49.9-68.8a7.92 7.92 0 00-6.5-3.3H648c-6.5 0-10.3 7.4-6.5 12.7l109.2 150.7a16.1 16.1 0 0026 0l165.8-228.7c3.8-5.3 0-12.7-6.5-12.7zm-44 306h-64.2c-5.5 0-10.6 2.9-13.6 7.5a352.2 352.2 0 01-49.8 62.2A355.92 355.92 0 01651.1 840a355 355 0 01-138.7 27.9c-48.1 0-94.8-9.4-138.7-27.9a355.92 355.92 0 01-113.3-76.3A353.06 353.06 0 01184 650.5c-18.6-43.8-28-90.5-28-138.5s9.4-94.7 28-138.5c17.9-42.4 43.6-80.5 76.4-113.2 32.8-32.7 70.9-58.4 113.3-76.3a355 355 0 01138.7-27.9c48.1 0 94.8 9.4 138.7 27.9 42.4 17.9 80.5 43.6 113.3 76.3 19 19 35.6 39.8 49.8 62.2 2.9 4.7 8.1 7.5 13.6 7.5H892c6 0 9.8-6.3 7.2-11.6C828.8 178.5 684.7 82 517.7 80 278.9 77.2 80.5 272.5 80 511.2 79.5 750.1 273.3 944 512.4 944c169.2 0 315.6-97 386.7-238.4A8 8 0 00892 694z"}}]},name:"issues-close",theme:"outlined"},i=f,g=t(91146),v=function(h,r){return o.createElement(g.Z,(0,n.Z)((0,n.Z)({},h),{},{ref:r,icon:i}))},C=o.forwardRef(v),m=C},48474:function(T,u,t){t.d(u,{Z:function(){return m}});var n=t(1413),o=t(67294),f={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-696 72h136v656H184V184zm656 656H384V384h456v456zM384 320V184h456v136H384z"}}]},name:"layout",theme:"outlined"},i=f,g=t(91146),v=function(h,r){return o.createElement(g.Z,(0,n.Z)((0,n.Z)({},h),{},{ref:r,icon:i}))},C=o.forwardRef(v),m=C},82099:function(T,u,t){t.d(u,{Z:function(){return m}});var n=t(1413),o=t(67294),f={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M482.2 508.4L331.3 389c-3-2.4-7.3-.2-7.3 3.6V478H184V184h204v128c0 2.2 1.8 4 4 4h60c2.2 0 4-1.8 4-4V144c0-15.5-12.5-28-28-28H144c-15.5 0-28 12.5-28 28v736c0 15.5 12.5 28 28 28h284c15.5 0 28-12.5 28-28V712c0-2.2-1.8-4-4-4h-60c-2.2 0-4 1.8-4 4v128H184V546h140v85.4c0 3.8 4.4 6 7.3 3.6l150.9-119.4a4.5 4.5 0 000-7.2zM880 116H596c-15.5 0-28 12.5-28 28v168c0 2.2 1.8 4 4 4h60c2.2 0 4-1.8 4-4V184h204v294H700v-85.4c0-3.8-4.3-6-7.3-3.6l-151 119.4a4.52 4.52 0 000 7.1l151 119.5c2.9 2.3 7.3.2 7.3-3.6V546h140v294H636V712c0-2.2-1.8-4-4-4h-60c-2.2 0-4 1.8-4 4v168c0 15.5 12.5 28 28 28h284c15.5 0 28-12.5 28-28V144c0-15.5-12.5-28-28-28z"}}]},name:"merge-cells",theme:"outlined"},i=f,g=t(91146),v=function(h,r){return o.createElement(g.Z,(0,n.Z)((0,n.Z)({},h),{},{ref:r,icon:i}))},C=o.forwardRef(v),m=C},57184:function(T,u,t){t.d(u,{Z:function(){return m}});var n=t(1413),o=t(67294),f={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M878.3 392.1L631.9 145.7c-6.5-6.5-15-9.7-23.5-9.7s-17 3.2-23.5 9.7L423.8 306.9c-12.2-1.4-24.5-2-36.8-2-73.2 0-146.4 24.1-206.5 72.3a33.23 33.23 0 00-2.7 49.4l181.7 181.7-215.4 215.2a15.8 15.8 0 00-4.6 9.8l-3.4 37.2c-.9 9.4 6.6 17.4 15.9 17.4.5 0 1 0 1.5-.1l37.2-3.4c3.7-.3 7.2-2 9.8-4.6l215.4-215.4 181.7 181.7c6.5 6.5 15 9.7 23.5 9.7 9.7 0 19.3-4.2 25.9-12.4 56.3-70.3 79.7-158.3 70.2-243.4l161.1-161.1c12.9-12.8 12.9-33.8 0-46.8zM666.2 549.3l-24.5 24.5 3.8 34.4a259.92 259.92 0 01-30.4 153.9L262 408.8c12.9-7.1 26.3-13.1 40.3-17.9 27.2-9.4 55.7-14.1 84.7-14.1 9.6 0 19.3.5 28.9 1.6l34.4 3.8 24.5-24.5L608.5 224 800 415.5 666.2 549.3z"}}]},name:"pushpin",theme:"outlined"},i=f,g=t(91146),v=function(h,r){return o.createElement(g.Z,(0,n.Z)((0,n.Z)({},h),{},{ref:r,icon:i}))},C=o.forwardRef(v),m=C},40666:function(T,u,t){t.d(u,{Z:function(){return m}});var n=t(1413),o=t(67294),f={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M938 458.8l-29.6-312.6c-1.5-16.2-14.4-29-30.6-30.6L565.2 86h-.4c-3.2 0-5.7 1-7.6 2.9L88.9 557.2a9.96 9.96 0 000 14.1l363.8 363.8c1.9 1.9 4.4 2.9 7.1 2.9s5.2-1 7.1-2.9l468.3-468.3c2-2.1 3-5 2.8-8zM459.7 834.7L189.3 564.3 589 164.6 836 188l23.4 247-399.7 399.7zM680 256c-48.5 0-88 39.5-88 88s39.5 88 88 88 88-39.5 88-88-39.5-88-88-88zm0 120c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32z"}}]},name:"tag",theme:"outlined"},i=f,g=t(91146),v=function(h,r){return o.createElement(g.Z,(0,n.Z)((0,n.Z)({},h),{},{ref:r,icon:i}))},C=o.forwardRef(v),m=C},55355:function(T,u,t){t.d(u,{Z:function(){return m}});var n=t(1413),o=t(67294),f={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M824.2 699.9a301.55 301.55 0 00-86.4-60.4C783.1 602.8 812 546.8 812 484c0-110.8-92.4-201.7-203.2-200-109.1 1.7-197 90.6-197 200 0 62.8 29 118.8 74.2 155.5a300.95 300.95 0 00-86.4 60.4C345 754.6 314 826.8 312 903.8a8 8 0 008 8.2h56c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5A226.62 226.62 0 01612 684c60.9 0 118.2 23.7 161.3 66.8C814.5 792 838 846.3 840 904.3c.1 4.3 3.7 7.7 8 7.7h56a8 8 0 008-8.2c-2-77-33-149.2-87.8-203.9zM612 612c-34.2 0-66.4-13.3-90.5-37.5a126.86 126.86 0 01-37.5-91.8c.3-32.8 13.4-64.5 36.3-88 24-24.6 56.1-38.3 90.4-38.7 33.9-.3 66.8 12.9 91 36.6 24.8 24.3 38.4 56.8 38.4 91.4 0 34.2-13.3 66.3-37.5 90.5A127.3 127.3 0 01612 612zM361.5 510.4c-.9-8.7-1.4-17.5-1.4-26.4 0-15.9 1.5-31.4 4.3-46.5.7-3.6-1.2-7.3-4.5-8.8-13.6-6.1-26.1-14.5-36.9-25.1a127.54 127.54 0 01-38.7-95.4c.9-32.1 13.8-62.6 36.3-85.6 24.7-25.3 57.9-39.1 93.2-38.7 31.9.3 62.7 12.6 86 34.4 7.9 7.4 14.7 15.6 20.4 24.4 2 3.1 5.9 4.4 9.3 3.2 17.6-6.1 36.2-10.4 55.3-12.4 5.6-.6 8.8-6.6 6.3-11.6-32.5-64.3-98.9-108.7-175.7-109.9-110.9-1.7-203.3 89.2-203.3 199.9 0 62.8 28.9 118.8 74.2 155.5-31.8 14.7-61.1 35-86.5 60.4-54.8 54.7-85.8 126.9-87.8 204a8 8 0 008 8.2h56.1c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5 29.4-29.4 65.4-49.8 104.7-59.7 3.9-1 6.5-4.7 6-8.7z"}}]},name:"team",theme:"outlined"},i=f,g=t(91146),v=function(h,r){return o.createElement(g.Z,(0,n.Z)((0,n.Z)({},h),{},{ref:r,icon:i}))},C=o.forwardRef(v),m=C},99134:function(T,u,t){var n=t(67294);const o=(0,n.createContext)({});u.Z=o},21584:function(T,u,t){var n=t(67294),o=t(93967),f=t.n(o),i=t(53124),g=t(99134),v=t(6999),C=function(r,W){var B={};for(var x in r)Object.prototype.hasOwnProperty.call(r,x)&&W.indexOf(x)<0&&(B[x]=r[x]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var z=0,x=Object.getOwnPropertySymbols(r);z<x.length;z++)W.indexOf(x[z])<0&&Object.prototype.propertyIsEnumerable.call(r,x[z])&&(B[x[z]]=r[x[z]]);return B};function m(r){return typeof r=="number"?`${r} ${r} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(r)?`0 0 ${r}`:r}const S=["xs","sm","md","lg","xl","xxl"],h=n.forwardRef((r,W)=>{const{getPrefixCls:B,direction:x}=n.useContext(i.E_),{gutter:z,wrap:d}=n.useContext(g.Z),{prefixCls:I,span:$,order:p,offset:E,push:M,pull:Z,className:j,children:V,flex:w,style:D}=r,G=C(r,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),b=B("col",I),[J,F,X]=(0,v.cG)(b),e={};let c={};S.forEach(a=>{let s={};const y=r[a];typeof y=="number"?s.span=y:typeof y=="object"&&(s=y||{}),delete G[a],c=Object.assign(Object.assign({},c),{[`${b}-${a}-${s.span}`]:s.span!==void 0,[`${b}-${a}-order-${s.order}`]:s.order||s.order===0,[`${b}-${a}-offset-${s.offset}`]:s.offset||s.offset===0,[`${b}-${a}-push-${s.push}`]:s.push||s.push===0,[`${b}-${a}-pull-${s.pull}`]:s.pull||s.pull===0,[`${b}-rtl`]:x==="rtl"}),s.flex&&(c[`${b}-${a}-flex`]=!0,e[`--${b}-${a}-flex`]=m(s.flex))});const O=f()(b,{[`${b}-${$}`]:$!==void 0,[`${b}-order-${p}`]:p,[`${b}-offset-${E}`]:E,[`${b}-push-${M}`]:M,[`${b}-pull-${Z}`]:Z},j,c,F,X),l={};if(z&&z[0]>0){const a=z[0]/2;l.paddingLeft=a,l.paddingRight=a}return w&&(l.flex=m(w),d===!1&&!l.minWidth&&(l.minWidth=0)),J(n.createElement("div",Object.assign({},G,{style:Object.assign(Object.assign(Object.assign({},l),D),e),className:O,ref:W}),V))});u.Z=h},17621:function(T,u,t){t.d(u,{Z:function(){return z}});var n=t(67294),o=t(93967),f=t.n(o),i=t(74443),g=t(53124),v=t(25378);function C(d,I){const $=[void 0,void 0],p=Array.isArray(d)?d:[d,void 0],E=I||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return p.forEach((M,Z)=>{if(typeof M=="object"&&M!==null)for(let j=0;j<i.c4.length;j++){const V=i.c4[j];if(E[V]&&M[V]!==void 0){$[Z]=M[V];break}}else $[Z]=M}),$}var m=t(99134),S=t(6999),h=function(d,I){var $={};for(var p in d)Object.prototype.hasOwnProperty.call(d,p)&&I.indexOf(p)<0&&($[p]=d[p]);if(d!=null&&typeof Object.getOwnPropertySymbols=="function")for(var E=0,p=Object.getOwnPropertySymbols(d);E<p.length;E++)I.indexOf(p[E])<0&&Object.prototype.propertyIsEnumerable.call(d,p[E])&&($[p[E]]=d[p[E]]);return $};const r=null,W=null;function B(d,I){const[$,p]=n.useState(typeof d=="string"?d:""),E=()=>{if(typeof d=="string"&&p(d),typeof d=="object")for(let M=0;M<i.c4.length;M++){const Z=i.c4[M];if(!I||!I[Z])continue;const j=d[Z];if(j!==void 0){p(j);return}}};return n.useEffect(()=>{E()},[JSON.stringify(d),I]),$}var z=n.forwardRef((d,I)=>{const{prefixCls:$,justify:p,align:E,className:M,style:Z,children:j,gutter:V=0,wrap:w}=d,D=h(d,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:G,direction:b}=n.useContext(g.E_),J=(0,v.Z)(!0,null),F=B(E,J),X=B(p,J),e=G("row",$),[c,O,l]=(0,S.VM)(e),a=C(V,J),s=f()(e,{[`${e}-no-wrap`]:w===!1,[`${e}-${X}`]:X,[`${e}-${F}`]:F,[`${e}-rtl`]:b==="rtl"},M,O,l),y={},P=a[0]!=null&&a[0]>0?a[0]/-2:void 0;P&&(y.marginLeft=P,y.marginRight=P);const[R,H]=a;y.rowGap=H;const A=n.useMemo(()=>({gutter:[R,H],wrap:w}),[R,H,w]);return c(n.createElement(m.Z.Provider,{value:A},n.createElement("div",Object.assign({},D,{className:s,style:Object.assign(Object.assign({},y),Z),ref:I}),j)))})},66309:function(T,u,t){t.d(u,{Z:function(){return X}});var n=t(67294),o=t(93967),f=t.n(o),i=t(98423),g=t(98787),v=t(69760),C=t(96159),m=t(45353),S=t(53124),h=t(11568),r=t(15063),W=t(14747),B=t(83262),x=t(83559);const z=e=>{const{paddingXXS:c,lineWidth:O,tagPaddingHorizontal:l,componentCls:a,calc:s}=e,y=s(l).sub(O).equal(),P=s(c).sub(O).equal();return{[a]:Object.assign(Object.assign({},(0,W.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:y,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,h.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${a}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${a}-close-icon`]:{marginInlineStart:P,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${a}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${a}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:y}}),[`${a}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},d=e=>{const{lineWidth:c,fontSizeIcon:O,calc:l}=e,a=e.fontSizeSM;return(0,B.IX)(e,{tagFontSize:a,tagLineHeight:(0,h.bf)(l(e.lineHeightSM).mul(a).equal()),tagIconSize:l(O).sub(l(c).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},I=e=>({defaultBg:new r.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var $=(0,x.I$)("Tag",e=>{const c=d(e);return z(c)},I),p=function(e,c){var O={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&c.indexOf(l)<0&&(O[l]=e[l]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,l=Object.getOwnPropertySymbols(e);a<l.length;a++)c.indexOf(l[a])<0&&Object.prototype.propertyIsEnumerable.call(e,l[a])&&(O[l[a]]=e[l[a]]);return O},M=n.forwardRef((e,c)=>{const{prefixCls:O,style:l,className:a,checked:s,onChange:y,onClick:P}=e,R=p(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:H,tag:A}=n.useContext(S.E_),Y=k=>{y==null||y(!s),P==null||P(k)},U=H("tag",O),[q,_,N]=$(U),ee=f()(U,`${U}-checkable`,{[`${U}-checkable-checked`]:s},A==null?void 0:A.className,a,_,N);return q(n.createElement("span",Object.assign({},R,{ref:c,style:Object.assign(Object.assign({},l),A==null?void 0:A.style),className:ee,onClick:Y})))}),Z=t(98719);const j=e=>(0,Z.Z)(e,(c,{textColor:O,lightBorderColor:l,lightColor:a,darkColor:s})=>({[`${e.componentCls}${e.componentCls}-${c}`]:{color:O,background:a,borderColor:l,"&-inverse":{color:e.colorTextLightSolid,background:s,borderColor:s},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}));var V=(0,x.bk)(["Tag","preset"],e=>{const c=d(e);return j(c)},I);function w(e){return typeof e!="string"?e:e.charAt(0).toUpperCase()+e.slice(1)}const D=(e,c,O)=>{const l=w(O);return{[`${e.componentCls}${e.componentCls}-${c}`]:{color:e[`color${O}`],background:e[`color${l}Bg`],borderColor:e[`color${l}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var G=(0,x.bk)(["Tag","status"],e=>{const c=d(e);return[D(c,"success","Success"),D(c,"processing","Info"),D(c,"error","Error"),D(c,"warning","Warning")]},I),b=function(e,c){var O={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&c.indexOf(l)<0&&(O[l]=e[l]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,l=Object.getOwnPropertySymbols(e);a<l.length;a++)c.indexOf(l[a])<0&&Object.prototype.propertyIsEnumerable.call(e,l[a])&&(O[l[a]]=e[l[a]]);return O};const F=n.forwardRef((e,c)=>{const{prefixCls:O,className:l,rootClassName:a,style:s,children:y,icon:P,color:R,onClose:H,bordered:A=!0,visible:Y}=e,U=b(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:q,direction:_,tag:N}=n.useContext(S.E_),[ee,k]=n.useState(!0),ie=(0,i.Z)(U,["closeIcon","closable"]);n.useEffect(()=>{Y!==void 0&&k(Y)},[Y]);const ae=(0,g.o2)(R),le=(0,g.yT)(R),te=ae||le,de=Object.assign(Object.assign({backgroundColor:R&&!te?R:void 0},N==null?void 0:N.style),s),L=q("tag",O),[ue,fe,ve]=$(L),ge=f()(L,N==null?void 0:N.className,{[`${L}-${R}`]:te,[`${L}-has-color`]:R&&!te,[`${L}-hidden`]:!ee,[`${L}-rtl`]:_==="rtl",[`${L}-borderless`]:!A},l,a,fe,ve),re=Q=>{Q.stopPropagation(),H==null||H(Q),!Q.defaultPrevented&&k(!1)},[,me]=(0,v.Z)((0,v.w)(e),(0,v.w)(N),{closable:!1,closeIconRender:Q=>{const Oe=n.createElement("span",{className:`${L}-close-icon`,onClick:re},Q);return(0,C.wm)(Q,Oe,K=>({onClick:se=>{var ne;(ne=K==null?void 0:K.onClick)===null||ne===void 0||ne.call(K,se),re(se)},className:f()(K==null?void 0:K.className,`${L}-close-icon`)}))}}),he=typeof U.onClick=="function"||y&&y.type==="a",oe=P||null,Ce=oe?n.createElement(n.Fragment,null,oe,y&&n.createElement("span",null,y)):y,ce=n.createElement("span",Object.assign({},ie,{ref:c,className:ge,style:de}),Ce,me,ae&&n.createElement(V,{key:"preset",prefixCls:L}),le&&n.createElement(G,{key:"status",prefixCls:L}));return ue(he?n.createElement(m.Z,{component:"Tag"},ce):ce)});F.CheckableTag=M;var X=F}}]);
