import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, Space, Typography, Alert, Descriptions } from 'antd';
import { keycloakService } from '@/services/keycloak';

const { Title, Text } = Typography;

const TestKeycloak: React.FC = () => {
  const [status, setStatus] = useState<string>('未初始化');
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [userInfo, setUserInfo] = useState<any>(null);
  const [token, setToken] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const checkStatus = async () => {
    try {
      setError(null);
      setStatus('检查中...');
      
      // 检查是否已认证
      const authenticated = keycloakService.isAuthenticated();
      setIsAuthenticated(authenticated);
      
      if (authenticated) {
        setStatus('已认证');
        const user = keycloakService.getUserInfo();
        setUserInfo(user);
        const accessToken = keycloakService.getToken();
        setToken(accessToken ? accessToken.substring(0, 50) + '...' : null);
      } else {
        setStatus('未认证');
        setUserInfo(null);
        setToken(null);
      }
    } catch (err: any) {
      setError(err.message);
      setStatus('错误');
    }
  };

  const handleInit = async () => {
    try {
      setError(null);
      setStatus('初始化中...');
      const result = await keycloakService.init();
      setStatus(result ? '初始化成功' : '初始化失败');
      await checkStatus();
    } catch (err: any) {
      setError(err.message);
      setStatus('初始化失败');
    }
  };

  const handleLogin = async () => {
    try {
      setError(null);
      await keycloakService.login('/test-keycloak');
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleLogout = async () => {
    try {
      setError(null);
      await keycloakService.logout();
      await checkStatus();
    } catch (err: any) {
      setError(err.message);
    }
  };

  useEffect(() => {
    checkStatus();
  }, []);

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>Keycloak 测试页面</Title>
      
      {error && (
        <Alert
          message="错误"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: '16px' }}
        />
      )}

      <Card title="Keycloak 状态" style={{ marginBottom: '16px' }}>
        <Descriptions column={1}>
          <Descriptions.Item label="状态">{status}</Descriptions.Item>
          <Descriptions.Item label="认证状态">
            <Text type={isAuthenticated ? 'success' : 'danger'}>
              {isAuthenticated ? '已认证' : '未认证'}
            </Text>
          </Descriptions.Item>
          {userInfo && (
            <>
              <Descriptions.Item label="用户ID">{userInfo.id}</Descriptions.Item>
              <Descriptions.Item label="用户名">{userInfo.username}</Descriptions.Item>
              <Descriptions.Item label="邮箱">{userInfo.email}</Descriptions.Item>
              <Descriptions.Item label="姓名">{userInfo.name}</Descriptions.Item>
            </>
          )}
          {token && (
            <Descriptions.Item label="访问令牌">{token}</Descriptions.Item>
          )}
        </Descriptions>
      </Card>

      <Card title="操作">
        <Space>
          <Button onClick={handleInit}>初始化 Keycloak</Button>
          <Button type="primary" onClick={handleLogin} disabled={isAuthenticated}>
            登录
          </Button>
          <Button onClick={handleLogout} disabled={!isAuthenticated}>
            登出
          </Button>
          <Button onClick={checkStatus}>刷新状态</Button>
        </Space>
      </Card>

      <Card title="调试信息" style={{ marginTop: '16px' }}>
        <Text code>
          当前 URL: {window.location.href}
        </Text>
        <br />
        <Text code>
          Web Crypto API: {typeof window.crypto !== 'undefined' ? '可用' : '不可用'}
        </Text>
        <br />
        <Text code>
          环境: {process.env.NODE_ENV}
        </Text>
      </Card>
    </div>
  );
};

export default TestKeycloak;
