// Keycloak wrapper to handle Web Crypto API issues
import Keycloak from 'keycloak-js';

// Store original crypto methods
const originalCrypto = window.crypto;
const originalGetRandomValues = originalCrypto?.getRandomValues;

// Create a polyfill for getRandomValues
const polyfillGetRandomValues = (array: any) => {
  for (let i = 0; i < array.length; i++) {
    array[i] = Math.floor(Math.random() * 256);
  }
  return array;
};

// Create a wrapper that intercepts Keycloak's crypto usage
export const createKeycloakInstance = (config: any) => {
  console.log('Creating Keycloak instance with crypto workaround...');
  
  // Temporarily patch crypto if needed
  let cryptoPatched = false;
  
  const patchCrypto = () => {
    if (!window.crypto) {
      console.warn('No window.crypto, creating minimal implementation');
      try {
        (window as any).crypto = {
          getRandomValues: polyfillGetRandomValues
        };
        cryptoPatched = true;
        console.log('✅ Created minimal crypto implementation');
      } catch (e) {
        console.error('❌ Failed to create crypto implementation:', e);
      }
    } else if (!window.crypto.getRandomValues) {
      console.warn('No getRandomValues, attempting to add polyfill');
      try {
        (window.crypto as any).getRandomValues = polyfillGetRandomValues;
        cryptoPatched = true;
        console.log('✅ Added getRandomValues polyfill');
      } catch (e) {
        console.error('❌ Failed to add getRandomValues:', e);
      }
    } else {
      // Test if getRandomValues works
      try {
        const testArray = new Uint8Array(1);
        window.crypto.getRandomValues(testArray);
        console.log('✅ Native getRandomValues works');
      } catch (e) {
        console.warn('Native getRandomValues failed, attempting to replace');
        try {
          (window.crypto as any).getRandomValues = polyfillGetRandomValues;
          cryptoPatched = true;
          console.log('✅ Replaced getRandomValues with polyfill');
        } catch (replaceError) {
          console.error('❌ Failed to replace getRandomValues:', replaceError);
        }
      }
    }
  };
  
  const restoreCrypto = () => {
    if (cryptoPatched && originalCrypto) {
      try {
        if (originalGetRandomValues) {
          (window.crypto as any).getRandomValues = originalGetRandomValues;
        }
        console.log('Restored original crypto methods');
      } catch (e) {
        console.warn('Could not restore original crypto methods:', e);
      }
    }
  };
  
  // Patch crypto before creating Keycloak instance
  patchCrypto();
  
  try {
    const keycloakInstance = new Keycloak(config);
    console.log('✅ Keycloak instance created successfully');
    
    // Wrap the init method to handle crypto issues
    const originalInit = keycloakInstance.init.bind(keycloakInstance);
    keycloakInstance.init = async (initOptions: any) => {
      console.log('Initializing Keycloak with options:', initOptions);
      
      // Ensure crypto is available during init
      patchCrypto();
      
      try {
        const result = await originalInit(initOptions);
        console.log('✅ Keycloak initialized successfully');
        return result;
      } catch (error) {
        console.error('❌ Keycloak initialization failed:', error);
        throw error;
      }
    };
    
    // Wrap the login method
    const originalLogin = keycloakInstance.login.bind(keycloakInstance);
    keycloakInstance.login = async (options: any) => {
      console.log('Keycloak login with options:', options);
      
      // Ensure crypto is available during login
      patchCrypto();
      
      try {
        const result = await originalLogin(options);
        console.log('✅ Keycloak login initiated successfully');
        return result;
      } catch (error) {
        console.error('❌ Keycloak login failed:', error);
        throw error;
      }
    };
    
    return keycloakInstance;
  } catch (error) {
    console.error('❌ Failed to create Keycloak instance:', error);
    restoreCrypto();
    throw error;
  }
};

export default createKeycloakInstance;
