"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[933],{48898:function(D,Z){var t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"};Z.Z=t},48118:function(D,Z,t){t.d(Z,{Z:function(){return p}});var S=t(1413),d=t(67294),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm72-112c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48zm400-188h-59.3c-2.6 0-5 1.2-6.5 3.3L763.7 538.1l-49.9-68.8a7.92 7.92 0 00-6.5-3.3H648c-6.5 0-10.3 7.4-6.5 12.7l109.2 150.7a16.1 16.1 0 0026 0l165.8-228.7c3.8-5.3 0-12.7-6.5-12.7zm-44 306h-64.2c-5.5 0-10.6 2.9-13.6 7.5a352.2 352.2 0 01-49.8 62.2A355.92 355.92 0 01651.1 840a355 355 0 01-138.7 27.9c-48.1 0-94.8-9.4-138.7-27.9a355.92 355.92 0 01-113.3-76.3A353.06 353.06 0 01184 650.5c-18.6-43.8-28-90.5-28-138.5s9.4-94.7 28-138.5c17.9-42.4 43.6-80.5 76.4-113.2 32.8-32.7 70.9-58.4 113.3-76.3a355 355 0 01138.7-27.9c48.1 0 94.8 9.4 138.7 27.9 42.4 17.9 80.5 43.6 113.3 76.3 19 19 35.6 39.8 49.8 62.2 2.9 4.7 8.1 7.5 13.6 7.5H892c6 0 9.8-6.3 7.2-11.6C828.8 178.5 684.7 82 517.7 80 278.9 77.2 80.5 272.5 80 511.2 79.5 750.1 273.3 944 512.4 944c169.2 0 315.6-97 386.7-238.4A8 8 0 00892 694z"}}]},name:"issues-close",theme:"outlined"},x=i,O=t(91146),g=function(_,$){return d.createElement(O.Z,(0,S.Z)((0,S.Z)({},_),{},{ref:$,icon:x}))},K=d.forwardRef(g),p=K},63783:function(D,Z,t){var S=t(1413),d=t(67294),i=t(36688),x=t(91146),O=function(p,w){return d.createElement(x.Z,(0,S.Z)((0,S.Z)({},p),{},{ref:w,icon:i.Z}))},g=d.forwardRef(O);Z.Z=g},40110:function(D,Z,t){var S=t(1413),d=t(67294),i=t(509),x=t(91146),O=function(p,w){return d.createElement(x.Z,(0,S.Z)((0,S.Z)({},p),{},{ref:w,icon:i.Z}))},g=d.forwardRef(O);Z.Z=g},184:function(D,Z,t){t.d(Z,{a:function(){return Ae}});var S=t(4942),d=t(74165),i=t(15861),x=t(1413),O=t(97685),g=t(91),K=t(12044),p=t(51812),w=t(48171),_=t(73177),$=t(21532),Ce=t(85265),je=t(93967),Ze=t.n(je),e=t(21770),Se=t(8880),ye=t(80334),c=t(67294),Oe=t(73935),Fe=t(89671),be=t(64847),Ee=function(b){return(0,S.Z)({},b.componentCls,{"&-sidebar-dragger":{width:"5px",cursor:"ew-resize",padding:"4px 0 0",borderTop:"1px solid transparent",position:"absolute",top:0,left:0,bottom:0,zIndex:100,backgroundColor:"transparent","&-min-disabled":{cursor:"w-resize"},"&-max-disabled":{cursor:"e-resize"}}})};function ce(h){return(0,be.Xj)("DrawerForm",function(b){var B=(0,x.Z)((0,x.Z)({},b),{},{componentCls:".".concat(h)});return[Ee(B)]})}var F=t(85893),Re=["children","trigger","onVisibleChange","drawerProps","onFinish","submitTimeout","title","width","resize","onOpenChange","visible","open"];function Ae(h){var b,B,De=h.children,N=h.trigger,Y=h.onVisibleChange,u=h.drawerProps,ee=h.onFinish,G=h.submitTimeout,Te=h.title,X=h.width,C=h.resize,Q=h.onOpenChange,z=h.visible,ve=h.open,f=(0,g.Z)(h,Re);(0,ye.ET)(!f.footer||!(u!=null&&u.footer),"DrawerForm \u662F\u4E00\u4E2A ProForm \u7684\u7279\u6B8A\u5E03\u5C40\uFF0C\u5982\u679C\u60F3\u81EA\u5B9A\u4E49\u6309\u94AE\uFF0C\u8BF7\u4F7F\u7528 submit.render \u81EA\u5B9A\u4E49\u3002");var r=c.useMemo(function(){var s,n,l,a={onResize:function(){},maxWidth:(0,K.j)()?window.innerWidth*.8:void 0,minWidth:300};return typeof C=="boolean"?C?a:{}:(0,p.Y)({onResize:(s=C==null?void 0:C.onResize)!==null&&s!==void 0?s:a.onResize,maxWidth:(n=C==null?void 0:C.maxWidth)!==null&&n!==void 0?n:a.maxWidth,minWidth:(l=C==null?void 0:C.minWidth)!==null&&l!==void 0?l:a.minWidth})},[C]),R=(0,c.useContext)($.ZP.ConfigContext),te=R.getPrefixCls("pro-form-drawer"),fe=ce(te),I=fe.wrapSSR,ne=fe.hashId,re=function(n){return"".concat(te,"-").concat(n," ").concat(ne)},Be=(0,c.useState)([]),Ie=(0,O.Z)(Be,2),he=Ie[1],we=(0,c.useState)(!1),ze=(0,O.Z)(we,2),le=ze[0],A=ze[1],j=(0,c.useState)(!1),y=(0,O.Z)(j,2),ae=y[0],se=y[1],me=(0,c.useState)(X||(C?r==null?void 0:r.minWidth:800)),H=(0,O.Z)(me,2),P=H[0],M=H[1],ie=(0,e.Z)(!!z,{value:ve||z,onChange:Q||Y}),J=(0,O.Z)(ie,2),W=J[0],E=J[1],k=(0,c.useRef)(null),xe=(0,c.useCallback)(function(s){k.current===null&&s&&he([]),k.current=s},[]),U=(0,c.useRef)(),Pe=(0,c.useCallback)(function(){var s,n,l,a=(s=(n=(l=f.formRef)===null||l===void 0?void 0:l.current)!==null&&n!==void 0?n:f.form)!==null&&s!==void 0?s:U.current;a&&u!==null&&u!==void 0&&u.destroyOnClose&&a.resetFields()},[u==null?void 0:u.destroyOnClose,f.form,f.formRef]);(0,c.useEffect)(function(){W&&(ve||z)&&(Q==null||Q(!0),Y==null||Y(!0)),ae&&M(r==null?void 0:r.minWidth)},[z,W,ae]),(0,c.useImperativeHandle)(f.formRef,function(){return U.current},[U.current]);var Me=(0,c.useMemo)(function(){return N?c.cloneElement(N,(0,x.Z)((0,x.Z)({key:"trigger"},N.props),{},{onClick:function(){var s=(0,i.Z)((0,d.Z)().mark(function l(a){var m,v;return(0,d.Z)().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:E(!W),se(!Object.keys(r)),(m=N.props)===null||m===void 0||(v=m.onClick)===null||v===void 0||v.call(m,a);case 3:case"end":return o.stop()}},l)}));function n(l){return s.apply(this,arguments)}return n}()})):null},[E,N,W,se,ae]),ge=(0,c.useMemo)(function(){var s,n,l,a,m;return f.submitter===!1?!1:(0,Se.T)({searchConfig:{submitText:(s=(n=R.locale)===null||n===void 0||(n=n.Modal)===null||n===void 0?void 0:n.okText)!==null&&s!==void 0?s:"\u786E\u8BA4",resetText:(l=(a=R.locale)===null||a===void 0||(a=a.Modal)===null||a===void 0?void 0:a.cancelText)!==null&&l!==void 0?l:"\u53D6\u6D88"},resetButtonProps:{preventDefault:!0,disabled:G?le:void 0,onClick:function(ue){var o;E(!1),u==null||(o=u.onClose)===null||o===void 0||o.call(u,ue)}}},(m=f.submitter)!==null&&m!==void 0?m:{})},[f.submitter,(b=R.locale)===null||b===void 0||(b=b.Modal)===null||b===void 0?void 0:b.okText,(B=R.locale)===null||B===void 0||(B=B.Modal)===null||B===void 0?void 0:B.cancelText,G,le,E,u]),pe=(0,c.useCallback)(function(s,n){return(0,F.jsxs)(F.Fragment,{children:[s,k.current&&n?(0,F.jsx)(c.Fragment,{children:(0,Oe.createPortal)(n,k.current)},"submitter"):n]})},[]),We=(0,w.J)(function(){var s=(0,i.Z)((0,d.Z)().mark(function n(l){var a,m,v;return(0,d.Z)().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return a=ee==null?void 0:ee(l),G&&a instanceof Promise&&(A(!0),m=setTimeout(function(){return A(!1)},G),a.finally(function(){clearTimeout(m),A(!1)})),o.next=4,a;case 4:return v=o.sent,v&&E(!1),o.abrupt("return",v);case 7:case"end":return o.stop()}},n)}));return function(n){return s.apply(this,arguments)}}()),Le=(0,_.X)(W,Y),q=(0,c.useCallback)(function(s){var n,l,a=(document.body.offsetWidth||1e3)-(s.clientX-document.body.offsetLeft),m=(n=r==null?void 0:r.minWidth)!==null&&n!==void 0?n:X||800,v=(l=r==null?void 0:r.maxWidth)!==null&&l!==void 0?l:window.innerWidth*.8;if(a<m){M(m);return}if(a>v){M(v);return}M(a)},[r==null?void 0:r.maxWidth,r==null?void 0:r.minWidth,X]),oe=(0,c.useCallback)(function(){document.removeEventListener("mousemove",q),document.removeEventListener("mouseup",oe)},[q]);return I((0,F.jsxs)(F.Fragment,{children:[(0,F.jsxs)(Ce.Z,(0,x.Z)((0,x.Z)((0,x.Z)({title:Te,width:P},u),Le),{},{afterOpenChange:function(n){var l;n||Pe(),u==null||(l=u.afterOpenChange)===null||l===void 0||l.call(u,n)},onClose:function(n){var l;G&&le||(E(!1),u==null||(l=u.onClose)===null||l===void 0||l.call(u,n))},footer:f.submitter!==!1&&(0,F.jsx)("div",{ref:xe,style:{display:"flex",justifyContent:"flex-end"}}),children:[C?(0,F.jsx)("div",{className:Ze()(re("sidebar-dragger"),ne,(0,S.Z)((0,S.Z)({},re("sidebar-dragger-min-disabled"),P===(r==null?void 0:r.minWidth)),re("sidebar-dragger-max-disabled"),P===(r==null?void 0:r.maxWidth))),onMouseDown:function(n){var l;r==null||(l=r.onResize)===null||l===void 0||l.call(r),n.stopPropagation(),n.preventDefault(),document.addEventListener("mousemove",q),document.addEventListener("mouseup",oe),se(!0)}}):null,(0,F.jsx)(F.Fragment,{children:(0,F.jsx)(Fe.I,(0,x.Z)((0,x.Z)({formComponentType:"DrawerForm",layout:"vertical"},f),{},{formRef:U,onInit:function(n,l){var a;f.formRef&&(f.formRef.current=l),f==null||(a=f.onInit)===null||a===void 0||a.call(f,n,l),U.current=l},submitter:ge,onFinish:function(){var s=(0,i.Z)((0,d.Z)().mark(function n(l){var a;return(0,d.Z)().wrap(function(v){for(;;)switch(v.prev=v.next){case 0:return v.next=2,We(l);case 2:return a=v.sent,v.abrupt("return",a);case 4:case"end":return v.stop()}},n)}));return function(n){return s.apply(this,arguments)}}(),contentRender:pe,children:De}))})]})),Me]}))}},4870:function(D,Z,t){t.r(Z),t.d(Z,{default:function(){return le}});var S=t(5574),d=t.n(S),i=t(67294),x=t(2453),O=t(55102),g=t(83622),K=t(43425),p=t(1413),w={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M931.4 498.9L94.9 79.5c-3.4-1.7-7.3-2.1-11-1.2a15.99 15.99 0 00-11.7 19.3l86.2 352.2c1.3 5.3 5.2 9.6 10.4 11.3l147.7 50.7-147.6 50.7c-5.2 1.8-9.1 6-10.3 11.3L72.2 926.5c-.9 3.7-.5 7.6 1.2 10.9 3.9 7.9 13.5 11.1 21.5 7.2l836.5-417c3.1-1.5 5.6-4.1 7.2-7.1 3.9-8 .7-17.6-7.2-21.6zM170.8 826.3l50.3-205.6 295.2-101.3c2.3-.8 4.2-2.6 5-5 1.4-4.2-.8-8.7-5-10.2L221.1 403 171 198.2l628 314.9-628.2 313.2z"}}]},name:"send",theme:"outlined"},_=w,$=t(91146),Ce=function(j,y){return i.createElement($.Z,(0,p.Z)((0,p.Z)({},j),{},{ref:y,icon:_}))},je=i.forwardRef(Ce),Ze=je,e=t(85893),Se=function(){var j=(0,i.useState)([]),y=d()(j,2),ae=y[0],se=y[1],me=(0,i.useState)(""),H=d()(me,2),P=H[0],M=H[1],ie=function(){if(P.trim()===""){x.ZP.warning("\u8BF7\u8F93\u5165\u6D88\u606F\u5185\u5BB9\uFF01");return}M("")};return(0,e.jsxs)("div",{style:{display:"flex",flexDirection:"column",height:"80vh"},children:[(0,e.jsxs)("div",{style:{flex:1,overflowY:"auto",paddingBottom:"1.5rem",borderBottom:"1px solid #262626"},children:[(0,e.jsxs)("div",{style:{display:"flex",justifyContent:"space-between"},children:[(0,e.jsx)("div",{style:{display:"flex",alignItems:"center"},children:(0,e.jsx)("h1",{children:"\u667A\u80FD\u52A9\u624B"})}),(0,e.jsx)("div",{style:{display:"flex"},children:(0,e.jsx)(K.Z,{})})]}),(0,e.jsxs)("p",{style:{color:"#A6AAAE",margin:0},children:["\u4F60\u53EF\u4EE5\u8BD5\u7740\u8BA9\u6211\uFF1A",(0,e.jsx)("br",{}),"\u5728AI\u667A\u80FD\u52A9\u624B\u9875\u9762\uFF0C\u901A\u8FC7\u4F7F\u7528\u64B0\u5199\u529F\u80FD\uFF0C\u60A8\u53EF\u8F7B\u677E\u8F93\u5165\u6240\u9700\u8F6C\u6362\u7684CUDA\u4EE3\u7801\uFF0C\u5E76\u901A\u8FC7\u5BF9\u8BDD\u7684\u65B9\u5F0F\u5BF9\u8F93\u51FA\u7684\u6587\u672C\u8FDB\u884C\u8C03\u6574\uFF0C\u5B9E\u73B0\u4FBF\u6377\u800C\u667A\u80FD\u7684\u4EE3\u7801\u7F16\u5199\u4F53\u9A8C..."]})]}),(0,e.jsx)("div",{}),(0,e.jsxs)("div",{style:{display:"flex",flexDirection:"row",flexWrap:"wrap",gap:" 0.5rem"},children:[(0,e.jsx)("button",{style:{backgroundColor:"#200033",color:"#9946B9",borderRadius:"0.5rem",border:"none"},children:"\u201CcudaSetDevice()\u201D API \u5982\u4F55\u8F6C\u8BD1\u4E3A SYCL \u8BED\u8A00 \uFF1F"}),(0,e.jsx)("button",{style:{backgroundColor:"#200033",color:"#9946B9",borderRadius:"0.5rem",border:"none"},children:"\u201CVectorAdd()\u201D\u65B9\u6CD5\u793A\u4F8B?"}),(0,e.jsx)("button",{style:{backgroundColor:"#200033",color:"#9946B9",borderRadius:"0.5rem",border:"none"},children:"\u201CVectorAdd()\u201D API \u5982\u4F55\u8F6C\u8BD1\u4E3A SYCL \u8BED\u8A00 \uFF1F"})]}),(0,e.jsxs)("div",{style:{display:"flex",paddingTop:"16px"},children:[(0,e.jsx)(O.Z,{placeholder:"\u8F93\u5165\u60A8\u7684\u95EE\u9898 ...",value:P,onChange:function(W){return M(W.target.value)},style:{flex:1,marginRight:"8px"}}),(0,e.jsxs)(g.ZP,{type:"primary",onClick:ie,children:[(0,e.jsx)(Ze,{}),"\u53D1\u9001"]})]})]})},ye=Se,c=t(53373),Oe=t(48898),Fe=function(j,y){return i.createElement($.Z,(0,p.Z)((0,p.Z)({},j),{},{ref:y,icon:Oe.Z}))},be=i.forwardRef(Fe),Ee=be,ce=t(40110),F={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M893.3 293.3L730.7 130.7c-7.5-7.5-16.7-13-26.7-16V112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V338.5c0-17-6.7-33.2-18.7-45.2zM384 184h256v104H384V184zm456 656H184V184h136v136c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V205.8l136 136V840zM512 442c-79.5 0-144 64.5-144 144s64.5 144 144 144 144-64.5 144-144-64.5-144-144-144zm0 224c-44.2 0-80-35.8-80-80s35.8-80 80-80 80 35.8 80 80-35.8 80-80 80z"}}]},name:"save",theme:"outlined"},Re=F,Ae=function(j,y){return i.createElement($.Z,(0,p.Z)((0,p.Z)({},j),{},{ref:y,icon:Re}))},h=i.forwardRef(Ae),b=h,B={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M140 188h584v164h76V144c0-17.7-14.3-32-32-32H96c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h544v-76H140V188z"}},{tag:"path",attrs:{d:"M414.3 256h-60.6c-3.4 0-6.4 2.2-7.6 5.4L219 629.4c-.3.8-.4 1.7-.4 2.6 0 4.4 3.6 8 8 8h55.1c3.4 0 6.4-2.2 7.6-5.4L322 540h196.2L422 261.4a8.42 8.42 0 00-7.7-5.4zm12.4 228h-85.5L384 360.2 426.7 484zM936 528H800v-93c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v93H592c-13.3 0-24 10.7-24 24v176c0 13.3 10.7 24 24 24h136v152c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V752h136c13.3 0 24-10.7 24-24V552c0-13.3-10.7-24-24-24zM728 680h-88v-80h88v80zm160 0h-88v-80h88v80z"}}]},name:"translation",theme:"outlined"},De=B,N=function(j,y){return i.createElement($.Z,(0,p.Z)((0,p.Z)({},j),{},{ref:y,icon:De}))},Y=i.forwardRef(N),u=Y,ee=t(63783),G=t(48118),Te=t(84017),X=t(184),C=t(88372),Q=t(5966),z=t(93410),ve=t(47019),f=t(64218),r=t(71230),R=t(15746),te=t(4393),fe=t(78367),I=t(83062),ne=t(11941),re=t(28846),Be=(0,re.kc)(function(A){var j=A.token;return{title:{marginBottom:"16px",color:j.colorTextHeading,fontWeight:"500",fontSize:"16px"},subtitle:{fontWeight:"100",fontSize:"14px"},logo:{width:"40px",height:"40px",float:"left",borderRadius:"50%",marginRight:"20px"}}}),Ie=Be,he=t(33577),we={width:"100%",height:100,overflow:"auto",boxShadow:"0 0 0 1px #1677ff",scrollbarWidth:"thin",scrollbarGutter:"stable"},ze={width:"100%",height:1e3},le=function(){var A=(0,i.useState)(!0),j=d()(A,2),y=j[0],ae=j[1],se=[{value:"cpp",label:"cpp"},{value:"c#",label:"c#"},{value:"typescript",label:"typescript"}],me=function(T){var L=T.icon,V=T.text;return(0,e.jsxs)("span",{children:[i.createElement(L,{style:{marginInlineEnd:8}}),V]})},H=function(T){var L=he.editor.getModels()[T],V=he.editor.getEditors()[T];L&&V&&V.trigger("keyboard","editor.action.startFindReplaceAction",{})},P=function(T,L){var V=new Blob([T],{type:"text/plain;charset=utf-8"}),de=document.createElement("a");de.href=URL.createObjectURL(V),de.download=L,de.click()},M=ve.Z.useForm(),ie=d()(M,1),J=ie[0],W=Ie(),E=W.styles,k=(0,i.useState)(`/*#------------------- 
 * Please,
 * Input CUDA Source Code @ here....  
 *#-------------------*/
`),xe=d()(k,2),U=xe[0],Pe=xe[1],Me=(0,i.useState)(`/*#------------------- 
 * Look,
 * Output SYCL Source Code @ here....  
 *#-------------------*/
`),ge=d()(Me,2),pe=ge[0],We=ge[1],Le=(0,i.useState)([{key:"1",label:"new"}]),q=d()(Le,2),oe=q[0],s=q[1],n=(0,i.useState)([{key:"1",label:"new"}]),l=d()(n,2),a=l[0],m=l[1],v=function(T){var L=T.file;if(L instanceof File){var V=new FileReader;V.onload=function(de){var Ve;Pe((Ve=de.target)===null||Ve===void 0?void 0:Ve.result),s([{key:"1",label:L.name}])},V.readAsText(L)}else console.error("The selected file is not a Blob.")},ue=function(){var T=`#include <sycl/ sycl.hpp >
#include < dpct / dpct.hpp >
#include < stdio.h >
#define VECTOR_SIZE 256

void VectorAddKernel(float * A, float * B, float * C, 
const sycl:: nd_item< 3 > & item_ct1) 
{
A[item_ct1.get_local_id(2)] = item_ct1.get_local_id(2) + 1.0f; 
B[item_ct1.get_local_id(2)] = item_ct1.get_local_id(2) + 1.0f; 
C[item_ct1.get_local_id(2)] =
A[item_ct1.get_local_id(2)] + B[item_ct1.get_local_id(2)];
}
int main() {
sycl::device dev_ct1; 
sycl::queue q_ct1(dev_ct1, 
sycl:: property_list{ sycl:: property:: queue:: in_order() }); 
float * d_A, * d_B, * d_C; 

d_A = sycl:: malloc_device < float > (VECTOR_SIZE, q_ct1); 
d_B = sycl:: malloc_device < float > (VECTOR_SIZE, q_ct1); 
d_C = sycl:: malloc_device < float > (VECTOR_SIZE, q_ct1); 

q_ct1.parallel_for(sycl:: nd_range < 3 > (sycl:: range < 3 > (1, 1, VECTOR_SIZE), 
sycl:: range < 3 > (1, 1, VECTOR_SIZE)), 
[=](sycl:: nd_item < 3 > item_ct1) {
VectorAddKernel(d_A, d_B, d_C, item_ct1); 
}); 

float Result[VECTOR_SIZE] = {}; 
q_ct1.memcpy(Result, d_C, VECTOR_SIZE * sizeof(float)).wait(); 

sycl:: free(d_A, q_ct1); 
sycl:: free(d_B, q_ct1); 
sycl:: free(d_C, q_ct1); 

for (int i = 0; i < VECTOR_SIZE; i++) {

if (i % 16 == 0) {

printf(""); 
} 
printf("%f ", Result[i]); 
} 

return 0; 
}
`;We(T),m(oe)};return(0,e.jsxs)(Te._z,{header:{breadcrumb:{}},children:[(0,e.jsx)(X.a,{title:"\u4F60\u597D\uFF0C\u6211\u662F\u4EE3\u7801\u52A9\u624B",resize:{onResize:function(){console.log("resize!")},maxWidth:window.innerWidth*.8,minWidth:400},form:J,trigger:(0,e.jsx)(f.Z,{offsetBottom:20,target:function(){return window},children:(0,e.jsx)("a",{style:{position:"fixed",right:"7vw",bottom:"7vh",zIndex:1e3},children:(0,e.jsx)("img",{src:t(13235),width:130})})}),autoFocusFirstInput:!0,drawerProps:{destroyOnClose:!1},submitter:!1,children:(0,e.jsx)(ye,{})}),(0,e.jsxs)(C.f,{children:[(0,e.jsxs)(r.Z,{gutter:24,children:[(0,e.jsx)(R.Z,{xl:12,lg:24,md:24,sm:24,xs:24,children:(0,e.jsx)(i.Suspense,{fallback:null,children:(0,e.jsxs)(te.Z,{bordered:!1,title:[(0,e.jsx)("br",{}),(0,e.jsx)("img",{src:t(22369),className:E.logo}),(0,e.jsxs)("div",{className:E.title,children:["CUDA Code - Input @ HERE",(0,e.jsx)("br",{}),(0,e.jsx)("span",{className:E.subtitle,children:"Cuda Programming Model [CUDA 11.7]"})]})],style:{width:"100%"},children:[(0,e.jsxs)(r.Z,{children:[(0,e.jsxs)(R.Z,{span:2,children:[(0,e.jsxs)(r.Z,{gutter:[16,16],children:[(0,e.jsx)(fe.Z,{beforeUpload:function(){return!1},onChange:v,showUploadList:!1,children:(0,e.jsx)(I.Z,{title:"\u6253\u5F00\u6587\u4EF6",children:(0,e.jsx)(g.ZP,{shape:"circle",icon:(0,e.jsx)(Ee,{})})})}),(0,e.jsx)("br",{})," ",(0,e.jsx)("br",{})]}),(0,e.jsxs)(r.Z,{gutter:[16,16],children:[(0,e.jsx)(I.Z,{title:"\u641C\u7D22",children:(0,e.jsx)(g.ZP,{shape:"circle",icon:(0,e.jsx)(ce.Z,{}),onClick:function(){return H(0)}})}),(0,e.jsx)("br",{})," ",(0,e.jsx)("br",{})]}),(0,e.jsxs)(r.Z,{gutter:[16,16],children:[(0,e.jsx)(I.Z,{title:"\u4FDD\u5B58",children:(0,e.jsx)(g.ZP,{shape:"circle",icon:(0,e.jsx)(b,{}),onClick:function(){return P(U,"cuda_code.cu")}})}),(0,e.jsx)("br",{})," ",(0,e.jsx)("br",{})]}),(0,e.jsxs)(r.Z,{gutter:[16,16],children:[(0,e.jsx)(I.Z,{title:"\u4E00\u952E\u8F6C\u8BD1",children:(0,e.jsx)(g.ZP,{shape:"circle",icon:(0,e.jsx)(u,{}),onClick:ue})}),(0,e.jsx)("br",{})," ",(0,e.jsx)("br",{})]})]}),(0,e.jsxs)(R.Z,{span:22,children:[(0,e.jsx)(ne.Z,{defaultActiveKey:"1",items:oe}),(0,e.jsx)(c.Z,{value:U,isREadOnly:!1,height:400,language:"cpp"})]})]}),(0,e.jsx)(Q.Z,{placeholder:"Command @ here",style:{marginTop:"24px"}})]})})}),(0,e.jsx)(R.Z,{xl:12,lg:24,md:24,sm:24,xs:24,children:(0,e.jsx)(i.Suspense,{fallback:null,children:(0,e.jsxs)(te.Z,{bordered:!1,title:[(0,e.jsx)("br",{}),(0,e.jsx)("img",{src:t(36806),className:E.logo}),(0,e.jsxs)("div",{className:E.title,children:["SYCL Code - Output @ HERE",(0,e.jsx)("br",{}),(0,e.jsx)("span",{className:E.subtitle,children:"SYCL Programming Model [Standard 2020 Revision 7]"})]})],style:{width:"100%"},children:[(0,e.jsxs)(r.Z,{children:[(0,e.jsxs)(R.Z,{span:2,children:[(0,e.jsxs)(r.Z,{gutter:[16,16],children:[(0,e.jsx)(I.Z,{title:"\u6559\u7A0B",children:(0,e.jsx)(g.ZP,{shape:"circle",icon:(0,e.jsx)(ee.Z,{})})}),(0,e.jsx)("br",{})," ",(0,e.jsx)("br",{})]}),(0,e.jsxs)(r.Z,{gutter:[16,16],children:[(0,e.jsx)(I.Z,{title:"\u641C\u7D22",children:(0,e.jsx)(g.ZP,{shape:"circle",icon:(0,e.jsx)(ce.Z,{}),onClick:function(){return H(1)}})}),(0,e.jsx)("br",{})," ",(0,e.jsx)("br",{})]}),(0,e.jsxs)(r.Z,{gutter:[16,16],children:[(0,e.jsx)(I.Z,{title:"\u4FDD\u5B58",children:(0,e.jsx)(g.ZP,{shape:"circle",icon:(0,e.jsx)(b,{}),onClick:function(){return P(pe,"sycl_code.cpp")}})}),(0,e.jsx)("br",{})," ",(0,e.jsx)("br",{})]}),(0,e.jsxs)(r.Z,{gutter:[16,16],children:[(0,e.jsx)(I.Z,{title:"\u6821\u9A8C",children:(0,e.jsx)(g.ZP,{shape:"circle",icon:(0,e.jsx)(G.Z,{})})}),(0,e.jsx)("br",{})," ",(0,e.jsx)("br",{})]})]}),(0,e.jsxs)(R.Z,{span:22,children:[(0,e.jsx)(ne.Z,{defaultActiveKey:"1",items:a}),(0,e.jsx)(c.Z,{value:pe,isREadOnly:!1,height:400,language:"cpp"})]})]}),(0,e.jsx)(Q.Z,{placeholder:"Command @ here",style:{marginTop:"24px"}})]})})})]}),(0,e.jsx)("br",{}),(0,e.jsx)("br",{}),(0,e.jsxs)(z.Z,{tabs:{type:"card"},children:[(0,e.jsx)(z.Z.TabPane,{tab:"CUDA Terminal",children:(0,e.jsx)("iframe",{src:"http://172.31.166.162:63080/",style:{height:"400px",width:"100%"},frameBorder:0})},"tab1"),(0,e.jsx)(z.Z.TabPane,{tab:"SYCL Terminal",children:(0,e.jsx)("iframe",{src:"http://172.30.232.22:61222/",style:{height:"400px",width:"100%"},frameBorder:0})},"tab2"),(0,e.jsx)(z.Z.TabPane,{tab:"Other Settings",children:(0,e.jsx)("iframe",{src:"http://127.0.0.1:63222/",style:{height:"400px",width:"100%"},frameBorder:0})},"tab3")]})]})]})}},13235:function(D,Z,t){D.exports=t.p+"static/ai-robot.61f54a4a.png"},22369:function(D,Z,t){D.exports=t.p+"static/NVLOGO.31a697e1.png"},36806:function(D,Z,t){D.exports=t.p+"static/SYCL_LOGO_200x200.224753ca.png"}}]);
