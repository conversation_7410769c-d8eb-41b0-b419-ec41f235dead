"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1555],{75573:function(L,f){var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"};f.Z=e},65184:function(L,f,e){e.d(f,{Z:function(){return d}});var v=e(1413),a=e(67294),p={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M740 161c-61.8 0-112 50.2-112 112 0 50.1 33.1 92.6 78.5 106.9v95.9L320 602.4V318.1c44.2-15 76-56.9 76-106.1 0-61.8-50.2-112-112-112s-112 50.2-112 112c0 49.2 31.8 91 76 106.1V706c-44.2 15-76 56.9-76 106.1 0 61.8 50.2 112 112 112s112-50.2 112-112c0-49.2-31.8-91-76-106.1v-27.8l423.5-138.7a50.52 50.52 0 0034.9-48.2V378.2c42.9-15.8 73.6-57 73.6-105.2 0-61.8-50.2-112-112-112zm-504 51a48.01 48.01 0 0196 0 48.01 48.01 0 01-96 0zm96 600a48.01 48.01 0 01-96 0 48.01 48.01 0 0196 0zm408-491a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"branches",theme:"outlined"},o=p,t=e(91146),x=function(R,M){return a.createElement(t.Z,(0,v.Z)((0,v.Z)({},R),{},{ref:M,icon:o}))},C=a.forwardRef(x),d=C},97245:function(L,f,e){var v=e(1413),a=e(67294),p=e(75573),o=e(91146),t=function(d,O){return a.createElement(o.Z,(0,v.Z)((0,v.Z)({},d),{},{ref:O,icon:p.Z}))},x=a.forwardRef(t);f.Z=x},40666:function(L,f,e){e.d(f,{Z:function(){return d}});var v=e(1413),a=e(67294),p={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M938 458.8l-29.6-312.6c-1.5-16.2-14.4-29-30.6-30.6L565.2 86h-.4c-3.2 0-5.7 1-7.6 2.9L88.9 557.2a9.96 9.96 0 000 14.1l363.8 363.8c1.9 1.9 4.4 2.9 7.1 2.9s5.2-1 7.1-2.9l468.3-468.3c2-2.1 3-5 2.8-8zM459.7 834.7L189.3 564.3 589 164.6 836 188l23.4 247-399.7 399.7zM680 256c-48.5 0-88 39.5-88 88s39.5 88 88 88 88-39.5 88-88-39.5-88-88-88zm0 120c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32z"}}]},name:"tag",theme:"outlined"},o=p,t=e(91146),x=function(R,M){return a.createElement(t.Z,(0,v.Z)((0,v.Z)({},R),{},{ref:M,icon:o}))},C=a.forwardRef(x),d=C},43756:function(L,f,e){e.r(f),e.d(f,{default:function(){return me}});var v=e(5574),a=e.n(v),p=e(31622),o=e(1413),t=e(67294),x={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M911.7 385.3l-.3-1.5c-.2-1-.3-1.9-.6-2.9-.2-.6-.4-1.1-.5-1.7-.3-.8-.5-1.7-.9-2.5-.2-.6-.5-1.1-.8-1.7-.4-.8-.8-1.5-1.2-2.3-.3-.5-.6-1.1-1-1.6-.8-1.2-1.7-2.4-2.6-3.6-.5-.6-1.1-1.3-1.7-1.9-.4-.5-.9-.9-1.4-1.3-.6-.6-1.3-1.1-1.9-1.6-.5-.4-1-.8-1.6-1.2-.2-.1-.4-.3-.6-.4L531.1 117.8a34.3 34.3 0 00-38.1 0L127.3 361.3c-.2.1-.4.3-.6.4-.5.4-1 .8-1.6 1.2-.7.5-1.3 1.1-1.9 1.6-.5.4-.9.9-1.4 1.3-.6.6-1.2 1.2-1.7 1.9-1 1.1-1.8 2.3-2.6 3.6-.3.5-.7 1-1 1.6-.4.7-.8 1.5-1.2 2.3-.3.5-.5 1.1-.8 1.7-.3.8-.6 1.7-.9 2.5-.2.6-.4 1.1-.5 1.7-.2.9-.4 1.9-.6 2.9l-.3 1.5c-.2 1.5-.3 3-.3 4.5v243.5c0 1.5.1 3 .3 4.5l.3 1.5.6 2.9c.2.6.3 1.1.5 1.7.3.9.6 1.7.9 2.5.2.6.5 1.1.8 1.7.4.8.7 1.5 1.2 2.3.3.5.6 1.1 1 1.6.5.7.9 1.4 1.5 2.1l1.2 1.5c.5.6 1.1 1.3 1.7 1.9.4.5.9.9 1.4 1.3.6.6 1.3 1.1 1.9 1.6.5.4 1 .8 1.6 1.2.2.1.4.3.6.4L493 905.7c5.6 3.8 12.3 5.8 19.1 5.8 6.6 0 13.3-1.9 19.1-5.8l365.6-243.5c.2-.1.4-.3.6-.4.5-.4 1-.8 1.6-1.2.7-.5 1.3-1.1 1.9-1.6.5-.4.9-.9 1.4-1.3.6-.6 1.2-1.2 1.7-1.9l1.2-1.5 1.5-2.1c.3-.5.7-1 1-1.6.4-.8.8-1.5 1.2-2.3.3-.5.5-1.1.8-1.7.3-.8.6-1.7.9-2.5.2-.5.4-1.1.5-1.7.3-.9.4-1.9.6-2.9l.3-1.5c.2-1.5.3-3 .3-4.5V389.8c-.3-1.5-.4-3-.6-4.5zM546.4 210.5l269.4 179.4-120.3 80.4-149-99.6V210.5zm-68.8 0v160.2l-149 99.6-120.3-80.4 269.3-179.4zM180.7 454.1l86 57.5-86 57.5v-115zm296.9 358.5L208.3 633.2l120.3-80.4 149 99.6v160.2zM512 592.8l-121.6-81.2L512 430.3l121.6 81.2L512 592.8zm34.4 219.8V652.4l149-99.6 120.3 80.4-269.3 179.4zM843.3 569l-86-57.5 86-57.5v115z"}}]},name:"codepen",theme:"outlined"},C=x,d=e(91146),O=function(i,s){return t.createElement(d.Z,(0,o.Z)((0,o.Z)({},i),{},{ref:s,icon:C}))},R=t.forwardRef(O),M=R,U=e(97245),H=e(65184),B={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M296 250c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H296zm184 144H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm-48 458H208V148h560v320c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V108c0-17.7-14.3-32-32-32H168c-17.7 0-32 14.3-32 32v784c0 17.7 14.3 32 32 32h264c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm440-88H728v-36.6c46.3-13.8 80-56.6 80-107.4 0-61.9-50.1-112-112-112s-112 50.1-112 112c0 50.7 33.7 93.6 80 107.4V764H520c-8.8 0-16 7.2-16 16v152c0 8.8 7.2 16 16 16h352c8.8 0 16-7.2 16-16V780c0-8.8-7.2-16-16-16zM646 620c0-27.6 22.4-50 50-50s50 22.4 50 50-22.4 50-50 50-50-22.4-50-50zm180 266H566v-60h260v60z"}}]},name:"audit",theme:"outlined"},X=B,Y=function(i,s){return t.createElement(d.Z,(0,o.Z)((0,o.Z)({},i),{},{ref:s,icon:X}))},k=t.forwardRef(Y),q=k,_={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 305H624V192c0-17.7-14.3-32-32-32H184v-40c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v784c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V640h248v113c0 17.7 14.3 32 32 32h416c17.7 0 32-14.3 32-32V337c0-17.7-14.3-32-32-32zM184 568V232h368v336H184zm656 145H504v-73h112c4.4 0 8-3.6 8-8V377h216v336z"}}]},name:"flag",theme:"outlined"},ee=_,Z=function(i,s){return t.createElement(d.Z,(0,o.Z)((0,o.Z)({},i),{},{ref:s,icon:ee}))},te=t.forwardRef(Z),r=te,h=e(40666),u={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M248 752h72V264h-72z"}},{tag:"path",attrs:{d:"M740 863c61.86 0 112-50.14 112-112 0-48.33-30.6-89.5-73.5-105.2l-.01-113.04a50.73 50.73 0 00-34.95-48.2l-434.9-142.41-22.4 68.42 420.25 137.61.01 95.92C661 658.34 628 700.8 628 751c0 61.86 50.14 112 112 112m-456 61c61.86 0 112-50.14 112-112s-50.14-112-112-112-112 50.14-112 112 50.14 112 112 112m456-125a48 48 0 110-96 48 48 0 010 96m-456 61a48 48 0 110-96 48 48 0 010 96m0-536c61.86 0 112-50.14 112-112s-50.14-112-112-112-112 50.14-112 112 50.14 112 112 112m0-64a48 48 0 110-96 48 48 0 010 96"}}]},name:"merge",theme:"outlined"},l=u,c=function(i,s){return t.createElement(d.Z,(0,o.Z)((0,o.Z)({},i),{},{ref:s,icon:l}))},b=t.forwardRef(c),E=b,V=e(43425),S=e(2830),D={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 000-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0014.4 7z"}}]},name:"menu-unfold",theme:"outlined"},F=D,I=function(i,s){return t.createElement(d.Z,(0,o.Z)((0,o.Z)({},i),{},{ref:s,icon:F}))},A=t.forwardRef(I),ne=A,ae={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 000 13.8z"}}]},name:"menu-fold",theme:"outlined"},oe=ae,le=function(i,s){return t.createElement(d.Z,(0,o.Z)((0,o.Z)({},i),{},{ref:s,icon:oe}))},N=t.forwardRef(le),se=N,re=e(50136),P=e(20057),n=e(85893),ie=function(i){var s=i.collapsed,y=s===void 0?!1:s,ue=i.onCollapse,fe=(0,P.s0)(),W=(0,P.TH)(),K=(0,t.useState)((0,p.gh)()||"dark"),$=a()(K,2),m=$[0],w=$[1];(0,t.useEffect)(function(){w((0,p.gh)());var J=new MutationObserver(function(g){g.forEach(function(Q){Q.attributeName==="data-theme"&&w((0,p.gh)())})});return J.observe(document.body,{attributes:!0}),function(){J.disconnect()}},[]);var he=[{key:"/tools/repo",icon:(0,n.jsx)(M,{}),label:"\u4EE3\u7801\u4ED3\u5E93"},{key:"/tools/repo/files",icon:(0,n.jsx)(U.Z,{}),label:"\u6587\u4EF6\u7BA1\u7406"},{key:"/tools/repo/branch",icon:(0,n.jsx)(H.Z,{}),label:"\u5206\u652F\u7BA1\u7406"},{key:"/tools/repo/commits",icon:(0,n.jsx)(q,{}),label:"\u63D0\u4EA4\u7BA1\u7406"},{key:"/tools/repo/compare",icon:(0,n.jsx)(r,{}),label:"\u6BD4\u8F83\u4FEE\u8BA2\u7248\u672C"},{key:"/tools/repo/tags",icon:(0,n.jsx)(h.Z,{}),label:"\u6807\u7B7E"},{key:"/tools/repo/mergeRequests",icon:(0,n.jsx)(E,{}),label:"\u5408\u5E76\u8BF7\u6C42"},{key:"/tools/repo/settings",icon:(0,n.jsx)(V.Z,{}),label:"\u8BBE\u7F6E"}],G=function(g){var Q=g.key;Q.startsWith("/")&&fe(Q,{state:W.state})},ve=function(){var g=W.pathname;return g.startsWith("/tools/repo/compare/compare_result")?"/tools/repo/compare":g.startsWith("/tools/repo/newProject")?"/tools/repo":g.startsWith("/tools/repo/commits/newMergeRequest")||g.startsWith("/tools/repo/newMergeRequest")?"/tools/repo/mergeRequests":g.startsWith("/tools/repo/files/newFile")?"/tools/repo/files":g.startsWith("/tools/repo/newTag")?"/tools/repo/tags":g.startsWith("/tools/repo/activities")?"/tools/repo/activities":g};return(0,n.jsxs)("div",{style:{height:"100%",display:"flex",flexDirection:"column",width:y?"64px":"200px"},children:[(0,n.jsxs)("div",{style:{height:"64px",display:"flex",alignItems:"center",justifyContent:y?"center":"flex-start",paddingLeft:y?"0":"24px",fontWeight:"bold",fontSize:"18px",borderBottom:m==="light"?"1px solid #D4D4D7":"1px solid #393B3C",borderRight:m==="light"?"1px solid #D4D4D7":"1px solid #393B3C",backgroundColor:m==="light"?"#fff":"#1a1c1e",color:m==="light"?"#1a1c1e":"#fff",overflow:"hidden",whiteSpace:"nowrap"},children:[(0,n.jsx)(S.Z,{style:{fontSize:y?"22px":"20px",marginRight:y?"0":"10px"}}),!y&&"Workbench"]}),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",flex:1,overflow:"hidden"},children:[(0,n.jsx)(re.Z,{mode:"inline",items:he,defaultOpenKeys:["control","development"],selectedKeys:[ve()],onClick:G,style:{flex:1,borderRight:m==="light"?"1px solid #D4D4D7":"1px solid #393B3C",overflow:"auto"}}),(0,n.jsx)("div",{onClick:ue,style:{height:"48px",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",color:m==="light"?"#535557":"#C7C8C7",backgroundColor:m==="light"?"#fff":"#1a1c1e",borderRight:m==="light"?"1px solid #D4D4D7":"1px solid #393B3C",fontSize:"14px"},children:y?(0,n.jsx)(ne,{style:{fontSize:"16px"}}):(0,n.jsx)(se,{style:{fontSize:"16px"}})})]})]})},ce=ie,z=e(26058),T=z.Z.Sider,de=z.Z.Content,me=function(){var j=(0,t.useState)(!1),i=a()(j,2),s=i[0],y=i[1],ue=function(){y(!s)},fe=(0,t.useState)(!1),W=a()(fe,2),K=W[0],$=W[1];return(0,t.useEffect)(function(){var m=function(){var G=document.querySelector(".ant-pro-sider");if(G){var ve=G.clientWidth;$(ve<100)}};m();var w=new MutationObserver(m);return w.observe(document.body,{attributes:!0,subtree:!0,childList:!0}),function(){return w.disconnect()}},[]),(0,n.jsxs)(z.Z,{children:[(0,n.jsxs)(T,{width:200,collapsedWidth:64,trigger:null,collapsed:s,className:"fixed-sidebar repo-sider",children:[(0,n.jsx)("style",{children:`
    .fixed-sidebar {
      position: fixed !important;
      left: `.concat(K?"64px":"256px",` !important;
      top: 0;
      bottom: 0;
      height: 100vh;
      z-index: 90 !important;
    }
  `)}),(0,n.jsx)(ce,{collapsed:s,onCollapse:ue})]}),(0,n.jsx)(z.Z,{style:{marginLeft:s?"70px":"200px"},children:(0,n.jsx)(de,{children:(0,n.jsx)(P.j3,{})})})]})}},26058:function(L,f,e){e.d(f,{Z:function(){return te}});var v=e(74902),a=e(67294),p=e(93967),o=e.n(p),t=e(98423),x=e(53124),C=e(82401),d=e(50344),O=e(70985);function R(r,h,u){return typeof u=="boolean"?u:r.length?!0:(0,d.Z)(h).some(c=>c.type===O.Z)}var M=e(24793),U=function(r,h){var u={};for(var l in r)Object.prototype.hasOwnProperty.call(r,l)&&h.indexOf(l)<0&&(u[l]=r[l]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,l=Object.getOwnPropertySymbols(r);c<l.length;c++)h.indexOf(l[c])<0&&Object.prototype.propertyIsEnumerable.call(r,l[c])&&(u[l[c]]=r[l[c]]);return u};function H({suffixCls:r,tagName:h,displayName:u}){return l=>a.forwardRef((b,E)=>a.createElement(l,Object.assign({ref:E,suffixCls:r,tagName:h},b)))}const B=a.forwardRef((r,h)=>{const{prefixCls:u,suffixCls:l,className:c,tagName:b}=r,E=U(r,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:V}=a.useContext(x.E_),S=V("layout",u),[D,F,I]=(0,M.ZP)(S),A=l?`${S}-${l}`:S;return D(a.createElement(b,Object.assign({className:o()(u||A,c,F,I),ref:h},E)))}),X=a.forwardRef((r,h)=>{const{direction:u}=a.useContext(x.E_),[l,c]=a.useState([]),{prefixCls:b,className:E,rootClassName:V,children:S,hasSider:D,tagName:F,style:I}=r,A=U(r,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),ne=(0,t.Z)(A,["suffixCls"]),{getPrefixCls:ae,className:oe,style:le}=(0,x.dj)("layout"),N=ae("layout",b),se=R(l,S,D),[re,P,n]=(0,M.ZP)(N),ie=o()(N,{[`${N}-has-sider`]:se,[`${N}-rtl`]:u==="rtl"},oe,E,V,P,n),ce=a.useMemo(()=>({siderHook:{addSider:z=>{c(T=>[].concat((0,v.Z)(T),[z]))},removeSider:z=>{c(T=>T.filter(de=>de!==z))}}}),[]);return re(a.createElement(C.V.Provider,{value:ce},a.createElement(F,Object.assign({ref:h,className:ie,style:Object.assign(Object.assign({},le),I)},ne),S)))}),Y=H({tagName:"div",displayName:"Layout"})(X),k=H({suffixCls:"header",tagName:"header",displayName:"Header"})(B),q=H({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(B),_=H({suffixCls:"content",tagName:"main",displayName:"Content"})(B);var ee=Y;const Z=ee;Z.Header=k,Z.Footer=q,Z.Content=_,Z.Sider=O.Z,Z._InternalSiderContext=O.D;var te=Z}}]);
