(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8645,3145],{23145:function(n,i,t){"use strict";t.r(i)},48645:function(n,i,t){var o={"./editorBaseApi":20927,"./editorBaseApi.js":20927,"./editorSimpleWorker":81465,"./editorSimpleWorker.js":81465,"./editorWorker":85215,"./editorWorker.js":85215,"./editorWorkerHost":98008,"./editorWorkerHost.js":98008,"./findSectionHeaders":72846,"./findSectionHeaders.js":72846,"./getIconClasses":22016,"./getIconClasses.js":22016,"./languageFeatureDebounce":88191,"./languageFeatureDebounce.js":88191,"./languageFeatures":71922,"./languageFeatures.js":71922,"./languageFeaturesService":7421,"./languageFeaturesService.js":7421,"./languageService":81032,"./languageService.js":81032,"./languagesAssociations":73536,"./languagesAssociations.js":73536,"./languagesRegistry":4765,"./languagesRegistry.js":4765,"./markerDecorations":36357,"./markerDecorations.js":36357,"./markerDecorationsService":86036,"./markerDecorationsService.js":86036,"./model":73733,"./model.js":73733,"./modelService":51200,"./modelService.js":51200,"./resolverService":88216,"./resolverService.js":88216,"./semanticTokensDto":14704,"./semanticTokensDto.js":14704,"./semanticTokensProviderStyling":68997,"./semanticTokensProviderStyling.js":68997,"./semanticTokensStyling":73343,"./semanticTokensStyling.js":73343,"./semanticTokensStylingService":84146,"./semanticTokensStylingService.js":84146,"./textModelSync/textModelSync.impl":28944,"./textModelSync/textModelSync.impl.js":28944,"./textModelSync/textModelSync.protocol":23145,"./textModelSync/textModelSync.protocol.js":23145,"./textResourceConfiguration":71765,"./textResourceConfiguration.js":71765,"./treeSitterParserService":28922,"./treeSitterParserService.js":28922,"./treeViewsDnd":80642,"./treeViewsDnd.js":80642,"./treeViewsDndService":58345,"./treeViewsDndService.js":58345,"./unicodeTextModelHighlighter":31446,"./unicodeTextModelHighlighter.js":31446};function r(e){var s=a(e);return t(s)}function a(e){if(!t.o(o,e)){var s=new Error("Cannot find module '"+e+"'");throw s.code="MODULE_NOT_FOUND",s}return o[e]}r.keys=function(){return Object.keys(o)},r.resolve=a,n.exports=r,r.id=48645}}]);
